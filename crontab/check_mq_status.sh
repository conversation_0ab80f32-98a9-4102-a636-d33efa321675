#!/bin/bash

# 简化的MQ进程状态检查脚本

echo "=== MQ消费者进程状态检查 ==="
echo "时间: $(date)"
echo "------------------------"

# 检查各个MQ进程
check_process() {
    local name=$1
    local pattern=$2
    local count=$(ps -ef | grep "$pattern" | grep -v grep | wc -l)
    
    if [ $count -gt 0 ]; then
        echo "✓ $name: 运行中 (进程数: $count)"
        # 显示进程详情
        ps -ef | grep "$pattern" | grep -v grep | while read line; do
            echo "  └─ $line"
        done
    else
        echo "✗ $name: 未运行"
    fi
    echo ""
}

# 检查所有MQ消费者进程
check_process "syncChain" "rp-field/crontab/index.php syncChain"
check_process "syncMergeMember" "rp-field/crontab/index.php syncMergeMember"
check_process "syncStock" "rp-field/crontab/index.php syncStock"
check_process "syncStaffInfo" "rp-field/crontab/index.php syncStaffInfo"
check_process "receiveCodeInfo" "rp-field/crontab/index.php receiveMqInfo receiveCodeInfo"
check_process "receiveErpDeliveryNotice" "rp-field/crontab/index.php receiveMqInfo receiveErpDeliveryNotice"
check_process "receiveMemberVipInfo" "rp-field/crontab/index.php receiveMqInfo receiveMemberVipInfo"

echo "------------------------"
echo "总进程数: $(ps -ef | grep "rp-field/crontab/index.php" | grep -v grep | wc -l)"

# 检查最近的错误日志
echo ""
echo "=== 最近的错误日志 ==="
if [ -d "/data/work/logs" ]; then
    echo "检查 /data/work/logs 目录..."
    find /data/work/logs -name "*.log" -mtime -1 -exec echo "文件: {}" \; -exec tail -5 {} \; 2>/dev/null | head -20
else
    echo "日志目录 /data/work/logs 不存在"
fi

# 检查系统负载
echo ""
echo "=== 系统状态 ==="
echo "负载: $(uptime)"
echo "内存: $(free -h | grep Mem)"
echo "磁盘: $(df -h / | tail -1)"
