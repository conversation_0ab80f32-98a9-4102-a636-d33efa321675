#!/bin/bash

# 启动MQ消费者进程的脚本

LOG_DIR="/data/work/logs"
SCRIPT_DIR="/data/rp-field/crontab"
PHP_PATH="/usr/local/bin/php"

# 确保日志目录存在
mkdir -p $LOG_DIR

echo "=== 启动MQ消费者进程 ==="
echo "时间: $(date)"
echo "日志目录: $LOG_DIR"
echo "脚本目录: $SCRIPT_DIR"
echo "PHP路径: $PHP_PATH"
echo "------------------------"

# 启动单个进程
start_process() {
    local name=$1
    local cmd=$2
    local log_file="$LOG_DIR/${name}.log"
    
    # 检查进程是否已经运行
    local count=$(ps -ef | grep "$cmd" | grep -v grep | wc -l)
    if [ $count -gt 0 ]; then
        echo "进程 $name 已经在运行 (进程数: $count)"
        return
    fi
    
    echo "启动进程: $name"
    echo "命令: $PHP_PATH $cmd"
    echo "日志: $log_file"
    
    # 启动进程
    nohup $PHP_PATH $cmd </dev/null >>$log_file 2>&1 &
    local pid=$!
    
    # 等待一下确保进程启动
    sleep 2
    
    # 检查进程是否成功启动
    if kill -0 $pid 2>/dev/null; then
        echo "✓ 进程 $name 启动成功，PID: $pid"
    else
        echo "✗ 进程 $name 启动失败"
        echo "最后几行日志:"
        tail -5 $log_file 2>/dev/null
    fi
    echo ""
}

# 启动所有MQ消费者
echo "开始启动所有MQ消费者进程..."
echo ""

start_process "syncChain" "$SCRIPT_DIR/index.php syncChain"
start_process "syncMergeMember" "$SCRIPT_DIR/index.php syncMergeMember"
start_process "syncStock" "$SCRIPT_DIR/index.php syncStock"
start_process "syncStaffInfo" "$SCRIPT_DIR/index.php syncStaffInfo"
start_process "receiveCodeInfo" "$SCRIPT_DIR/index.php receiveMqInfo receiveCodeInfo"
start_process "receiveErpDeliveryNotice" "$SCRIPT_DIR/index.php receiveMqInfo receiveErpDeliveryNotice"
start_process "receiveMemberVipInfo" "$SCRIPT_DIR/index.php receiveMqInfo receiveMemberVipInfo"

echo "========================"
echo "启动完成！"
echo ""
echo "使用以下命令检查状态:"
echo "bash $SCRIPT_DIR/check_mq_status.sh"
echo ""
echo "停止所有进程:"
echo "bash $SCRIPT_DIR/stop_mq.sh"
