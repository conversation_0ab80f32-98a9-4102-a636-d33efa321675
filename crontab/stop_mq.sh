#!/bin/bash

# 停止MQ消费者进程的脚本

echo "=== 停止MQ消费者进程 ==="
echo "时间: $(date)"
echo "------------------------"

# 停止单个进程
stop_process() {
    local name=$1
    local pattern=$2
    
    echo "检查进程: $name"
    local pids=$(ps -ef | grep "$pattern" | grep -v grep | awk '{print $2}')
    
    if [ -z "$pids" ]; then
        echo "进程 $name 未运行"
    else
        echo "找到进程 $name，PID: $pids"
        echo "正在停止..."
        
        # 先尝试优雅停止
        echo $pids | xargs kill -TERM 2>/dev/null
        sleep 3
        
        # 检查是否还在运行
        local remaining=$(ps -ef | grep "$pattern" | grep -v grep | awk '{print $2}')
        if [ ! -z "$remaining" ]; then
            echo "强制停止进程..."
            echo $remaining | xargs kill -9 2>/dev/null
        fi
        
        echo "✓ 进程 $name 已停止"
    fi
    echo ""
}

# 停止所有MQ消费者
echo "开始停止所有MQ消费者进程..."
echo ""

stop_process "syncChain" "rp-field/crontab/index.php syncChain"
stop_process "syncMergeMember" "rp-field/crontab/index.php syncMergeMember"
stop_process "syncStock" "rp-field/crontab/index.php syncStock"
stop_process "syncStaffInfo" "rp-field/crontab/index.php syncStaffInfo"
stop_process "receiveCodeInfo" "rp-field/crontab/index.php receiveMqInfo receiveCodeInfo"
stop_process "receiveErpDeliveryNotice" "rp-field/crontab/index.php receiveMqInfo receiveErpDeliveryNotice"
stop_process "receiveMemberVipInfo" "rp-field/crontab/index.php receiveMqInfo receiveMemberVipInfo"

echo "========================"
echo "停止完成！"
echo ""
echo "使用以下命令检查状态:"
echo "bash /data/rp-field/crontab/check_mq_status.sh"
