#!/bin/bash

# 基于原脚本逻辑的新版本MQ管理脚本

LOG_DIR="/data/work/logs"
PHP_PATH="/usr/local/bin/php"

# 确保日志目录存在
mkdir -p $LOG_DIR

# 检查进程函数 - 完全按照原脚本逻辑
check_process() {
    local process_name=$1
    local grep_pattern=$2
    local count=$(ps -ef|grep "$grep_pattern"|grep -v grep|wc -l)
    echo $count
}

# 启动进程函数 - 完全按照原脚本逻辑  
start_process() {
    local process_name=$1
    local php_cmd=$2
    local log_file=$3
    
    echo "启动 $process_name..."
    nohup $PHP_PATH $php_cmd </dev/null >>$log_file 2>&1 &
    echo "$process_name 已启动，日志: $log_file"
}

# 停止进程函数
stop_process() {
    local process_name=$1
    local grep_pattern=$2
    
    local pids=$(ps -ef|grep "$grep_pattern"|grep -v grep|awk '{print $2}')
    if [ ! -z "$pids" ]; then
        echo "停止 $process_name (PID: $pids)..."
        echo $pids | xargs kill -9 2>/dev/null
        echo "$process_name 已停止"
    else
        echo "$process_name 未运行"
    fi
}

# 检查状态函数
check_status() {
    echo "=== MQ进程状态检查 ==="
    echo "时间: $(date)"
    echo "------------------------"
    
    # 检查接收核销信息
    local count1=$(check_process "receiveCodeInfo" "rp-field/crontab/index.php receiveMqInfo receiveCodeInfo$")
    if [ $count1 -gt 0 ]; then
        echo "✓ receiveCodeInfo: 运行中 (进程数: $count1)"
    else
        echo "✗ receiveCodeInfo: 未运行"
    fi
    
    # 检查发货
    local count2=$(check_process "receiveErpDeliveryNotice" "rp-field/crontab/index.php receiveMqInfo receiveErpDeliveryNotice$")
    if [ $count2 -gt 0 ]; then
        echo "✓ receiveErpDeliveryNotice: 运行中 (进程数: $count2)"
    else
        echo "✗ receiveErpDeliveryNotice: 未运行"
    fi
    
    # 检查同步库存
    local count3=$(check_process "syncStock" "rp-field/crontab/index.php syncStock$")
    if [ $count3 -gt 0 ]; then
        echo "✓ syncStock: 运行中 (进程数: $count3)"
    else
        echo "✗ syncStock: 未运行"
    fi
    
    # 检查同步门店
    local count4=$(check_process "syncChain" "rp-field/crontab/index.php syncChain$")
    if [ $count4 -gt 0 ]; then
        echo "✓ syncChain: 运行中 (进程数: $count4)"
    else
        echo "✗ syncChain: 未运行"
    fi
    
    # 检查合并会员
    local count5=$(check_process "syncMergeMember" "rp-field/crontab/index.php syncMergeMember$")
    if [ $count5 -gt 0 ]; then
        echo "✓ syncMergeMember: 运行中 (进程数: $count5)"
    else
        echo "✗ syncMergeMember: 未运行"
    fi
    
    # 检查会员卡、保障卡
    local count6=$(check_process "receiveMemberVipInfo" "rp-field/crontab/index.php receiveMqInfo receiveMemberVipInfo$")
    if [ $count6 -gt 0 ]; then
        echo "✓ receiveMemberVipInfo: 运行中 (进程数: $count6)"
    else
        echo "✗ receiveMemberVipInfo: 未运行"
    fi
    
    # 检查员工信息同步
    local count7=$(check_process "syncStaffInfo" "rp-field/crontab/index.php syncStaffInfo$")
    if [ $count7 -gt 0 ]; then
        echo "✓ syncStaffInfo: 运行中 (进程数: $count7)"
    else
        echo "✗ syncStaffInfo: 未运行"
    fi
    
    echo "------------------------"
    local total=$((count1 + count2 + count3 + count4 + count5 + count6 + count7))
    echo "总运行进程数: $total"
}

# 启动所有进程 - 完全按照原脚本逻辑
start_all() {
    echo "=== 启动所有MQ进程 ==="
    echo "时间: $(date)"
    echo "------------------------"
    
    # 接收核销信息
    if [ $(check_process "receiveCodeInfo" "rp-field/crontab/index.php receiveMqInfo receiveCodeInfo$") -eq 0 ]; then
        start_process "receiveCodeInfo" "/data/rp-field/crontab/index.php receiveMqInfo receiveCodeInfo" "/data/work/logs/receiveCodeInfo.txt"
    else
        echo "receiveCodeInfo 已在运行"
    fi
    
    # 发货
    if [ $(check_process "receiveErpDeliveryNotice" "rp-field/crontab/index.php receiveMqInfo receiveErpDeliveryNotice$") -eq 0 ]; then
        start_process "receiveErpDeliveryNotice" "/data/rp-field/crontab/index.php receiveMqInfo receiveErpDeliveryNotice" "/data/work/logs/receiveErpDeliveryNotice.txt"
    else
        echo "receiveErpDeliveryNotice 已在运行"
    fi
    
    # 同步库存
    if [ $(check_process "syncStock" "rp-field/crontab/index.php syncStock$") -eq 0 ]; then
        start_process "syncStock" "/data/rp-field/crontab/index.php syncStock" "/data/work/logs/syncStock.txt"
    else
        echo "syncStock 已在运行"
    fi
    
    # 同步门店
    if [ $(check_process "syncChain" "rp-field/crontab/index.php syncChain$") -eq 0 ]; then
        start_process "syncChain" "/data/rp-field/crontab/index.php syncChain" "/data/work/logs/syncChain.txt"
    else
        echo "syncChain 已在运行"
    fi
    
    # 合并会员
    if [ $(check_process "syncMergeMember" "rp-field/crontab/index.php syncMergeMember$") -eq 0 ]; then
        start_process "syncMergeMember" "/data/rp-field/crontab/index.php syncMergeMember" "/data/work/logs/syncMergeMember.txt"
    else
        echo "syncMergeMember 已在运行"
    fi
    
    # 会员卡、保障卡
    if [ $(check_process "receiveMemberVipInfo" "rp-field/crontab/index.php receiveMqInfo receiveMemberVipInfo$") -eq 0 ]; then
        start_process "receiveMemberVipInfo" "/data/rp-field/crontab/index.php receiveMqInfo receiveMemberVipInfo" "/data/work/logs/receiveMemberVipInfo.txt"
    else
        echo "receiveMemberVipInfo 已在运行"
    fi
    
    # 员工信息同步
    if [ $(check_process "syncStaffInfo" "rp-field/crontab/index.php syncStaffInfo$") -eq 0 ]; then
        start_process "syncStaffInfo" "/data/rp-field/crontab/index.php syncStaffInfo" "/data/work/logs/syncStaffInfo.txt"
    else
        echo "syncStaffInfo 已在运行"
    fi
    
    echo "------------------------"
    echo "启动完成！"
}

# 停止所有进程
stop_all() {
    echo "=== 停止所有MQ进程 ==="
    echo "时间: $(date)"
    echo "------------------------"
    
    stop_process "receiveCodeInfo" "rp-field/crontab/index.php receiveMqInfo receiveCodeInfo$"
    stop_process "receiveErpDeliveryNotice" "rp-field/crontab/index.php receiveMqInfo receiveErpDeliveryNotice$"
    stop_process "syncStock" "rp-field/crontab/index.php syncStock$"
    stop_process "syncChain" "rp-field/crontab/index.php syncChain$"
    stop_process "syncMergeMember" "rp-field/crontab/index.php syncMergeMember$"
    stop_process "receiveMemberVipInfo" "rp-field/crontab/index.php receiveMqInfo receiveMemberVipInfo$"
    stop_process "syncStaffInfo" "rp-field/crontab/index.php syncStaffInfo$"
    
    echo "------------------------"
    echo "停止完成！"
}

# 主函数
case "$1" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        stop_all
        sleep 3
        start_all
        ;;
    status)
        check_status
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动所有MQ消费者进程"
        echo "  stop    - 停止所有MQ消费者进程"
        echo "  restart - 重启所有MQ消费者进程"
        echo "  status  - 显示所有进程状态"
        exit 1
        ;;
esac

exit 0
