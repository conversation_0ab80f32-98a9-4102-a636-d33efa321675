#!/bin/bash

# MQ消费者监控和重启脚本
# 用于监控和管理所有AMQP消费者进程

LOG_DIR="/data/work/logs"
SCRIPT_DIR="/data/rp-field/crontab"
PHP_PATH="/usr/local/bin/php"

# 确保日志目录存在
mkdir -p $LOG_DIR

# 定义所有MQ消费者进程
declare -A MQ_PROCESSES=(
    ["syncChain"]="$SCRIPT_DIR/index.php syncChain"
    ["syncMergeMember"]="$SCRIPT_DIR/index.php syncMergeMember"
    ["syncStock"]="$SCRIPT_DIR/index.php syncStock"
    ["syncStaffInfo"]="$SCRIPT_DIR/index.php syncStaffInfo"
    ["receiveCodeInfo"]="$SCRIPT_DIR/index.php receiveMqInfo receiveCodeInfo"
    ["receiveErpDeliveryNotice"]="$SCRIPT_DIR/index.php receiveMqInfo receiveErpDeliveryNotice"
    ["receiveMemberVipInfo"]="$SCRIPT_DIR/index.php receiveMqInfo receiveMemberVipInfo"
)

# 检查进程是否运行
check_process() {
    local process_name=$1
    local command=$2
    local count=$(ps -ef | grep "$command" | grep -v grep | wc -l)
    echo $count
}

# 启动进程
start_process() {
    local process_name=$1
    local command=$2
    local log_file="$LOG_DIR/${process_name}.log"

    echo "[$(date)] 启动进程: $process_name" >> $log_file
    nohup $PHP_PATH $command </dev/null >>$log_file 2>&1 &
    local pid=$!
    echo "[$(date)] 进程 $process_name 已启动，PID: $pid" >> $log_file
    echo "[$(date)] 进程 $process_name 已启动，PID: $pid"
}

# 停止进程
stop_process() {
    local process_name=$1
    local command=$2

    echo "[$(date)] 停止进程: $process_name"
    local pids=$(ps -ef | grep "$command" | grep -v grep | awk '{print $2}')
    if [ ! -z "$pids" ]; then
        echo $pids | xargs kill -9 2>/dev/null
        echo "[$(date)] 进程 $process_name 已停止"
    else
        echo "[$(date)] 进程 $process_name 未运行"
    fi
}

# 重启进程
restart_process() {
    local process_name=$1
    local command=$2
    
    echo "[$(date)] 重启进程: $process_name"
    stop_process $process_name "$command"
    sleep 2
    start_process $process_name "$command"
}

# 监控所有进程
monitor_all() {
    echo "[$(date)] 开始监控所有MQ消费者进程"
    
    for process_name in "${!MQ_PROCESSES[@]}"; do
        command="${MQ_PROCESSES[$process_name]}"
        count=$(check_process $process_name "$command")
        
        if [ $count -eq 0 ]; then
            echo "[$(date)] 进程 $process_name 未运行，正在启动..."
            start_process $process_name "$command"
        else
            echo "[$(date)] 进程 $process_name 正在运行 (进程数: $count)"
        fi
    done
}

# 停止所有进程
stop_all() {
    echo "[$(date)] 停止所有MQ消费者进程"
    
    for process_name in "${!MQ_PROCESSES[@]}"; do
        command="${MQ_PROCESSES[$process_name]}"
        count=$(check_process $process_name "$command")
        
        if [ $count -gt 0 ]; then
            echo "[$(date)] 停止进程: $process_name"
            stop_process $process_name "$command"
        fi
    done
}

# 重启所有进程
restart_all() {
    echo "[$(date)] 重启所有MQ消费者进程"
    stop_all
    sleep 5
    monitor_all
}

# 显示状态
status() {
    echo "=== MQ消费者进程状态 ==="
    echo "时间: $(date)"
    echo "------------------------"
    
    for process_name in "${!MQ_PROCESSES[@]}"; do
        command="${MQ_PROCESSES[$process_name]}"
        count=$(check_process $process_name "$command")
        
        if [ $count -gt 0 ]; then
            echo "✓ $process_name: 运行中 (进程数: $count)"
        else
            echo "✗ $process_name: 未运行"
        fi
    done
    echo "------------------------"
}

# 清理超时日志
clean_timeout_logs() {
    echo "[$(date)] 清理超时日志"
    
    # 清理7天前的日志
    find $LOG_DIR -name "*.log" -mtime +7 -delete
    
    # 清理包含"Consumer timeout"的日志行，保留最近100行
    for process_name in "${!MQ_PROCESSES[@]}"; do
        log_file="$LOG_DIR/${process_name}.log"
        if [ -f "$log_file" ]; then
            # 保留最后1000行，删除其余的
            tail -n 1000 "$log_file" > "${log_file}.tmp" && mv "${log_file}.tmp" "$log_file"
        fi
    done
}

# 主函数
case "$1" in
    start)
        monitor_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        restart_all
        ;;
    status)
        status
        ;;
    monitor)
        monitor_all
        ;;
    clean)
        clean_timeout_logs
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|monitor|clean}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动所有未运行的MQ消费者进程"
        echo "  stop    - 停止所有MQ消费者进程"
        echo "  restart - 重启所有MQ消费者进程"
        echo "  status  - 显示所有进程状态"
        echo "  monitor - 监控并启动未运行的进程"
        echo "  clean   - 清理旧日志文件"
        exit 1
        ;;
esac

exit 0
