<?php

use Upet\Models\Goods;
use Upet\Modules\Goods\Actions\GetStockAction;
use Upet\Modules\Goods\Queues\SyncMiniShopStockQueue;

defined('InShopNC') or exit('Access Invalid!');

class syncStockControl extends BaseCronControl
{
    /**
     * 同步库存
     *
     * @param bool $ignoreException 是否忽略异常
     */
    public function indexOp($ignoreException = false)
    {
        $rs = $this->newDCMQQueue('ordercenter', 'dc_sz_stock_update_to_sc');
        if (!$rs) {
            return;
        }
        list($cnn, $queue) = $rs;

        $this->output('Waiting for messages. To exit press CTRL+C ');

        // 使用安全的消费方法
        $this->safeConsume($queue, function (AMQPEnvelope $envelope, AMQPQueue $q) use ($ignoreException) {
            if (get_env() <> 'www') {
                $this->output("接收到消息：" . $envelope->getBody());
            }

            $body = json_decode($envelope->getBody(), true);

            if (empty($body)) {
                log_error('sync-stock', $envelope->getBody());
            } else {
                try {
                    log_info('sync-stock', '同步库存mq接收：' . $envelope->getBody());
                    $this->syncStock(explode(',', $body['goodsids']));
                } catch (Exception $e) {
                    log_error('sync-stock', 'Exception:' . $e->getMessage());
                }
            }

            return $q->ack($envelope->getDeliveryTag());
        }, 10); // 10秒超时

        $this->output('Close connection...');

        $cnn->disconnect();
    }

    /**
     * 同步商品库存
     *
     * @param $ids
     */
    protected function syncStock($ids)
    {
        $stocks = action(new GetStockAction($ids));

        if (get_env() <> 'www') {
            log_info('sync-stock 库存数据：' ,$stocks);
        }

        $goods = Goods::alias('g')
            ->leftJoin('goods_common_live l', 'l.goods_common_id = g.goods_commonid')
            ->field('g.goods_id,g.goods_storage,g.goods_commonid,l.out_status,goods_price,goods_promotion_price,goods_promotion_type,goods_marketprice,g.store_id')
            ->whereIn('goods_id', array_keys($stocks))
            ->select();

        if ($goods->isEmpty()) {
            log_info('sync-stock 查询不到数据：' ,$ids);
            return;
        }
        $store_ids = [];
        foreach ($goods as $good) {
            if (!isset($stocks[$good->goods_id])){
                log_info('sync-stock 查询不到库存数据：' ,$good);
                continue;
            }

            $stock = max($stocks[$good->goods_id], 0);
            if ($good->goods_storage == $stock) {
                log_info('sync-stock 查询库存数据相等：' . $stock, $good);
                continue;
            }
            //判断库存从有到无，或从无到有时再同步es
            if (($good->goods_storage == 0 && $stock > 0) || ($good->goods_storage > 0 && $stock == 0)) {
                $store_ids[$good->store_id][] = $good->goods_id;
            }
            $good->goods_storage = $stock;
            $good->save();

            if ($good->out_status > 0) {
                SyncMiniShopStockQueue::dispatch($good->toArray(), $stock);
            }
        }
        //如果有库存则要保存handlelog同步到es todo
        /** @var goodsLogic $logic_goods*/
        $logic_goods = Logic('goods');
        foreach ($store_ids as $k => $v){
            $logic_goods->SynsGoodsHandleToEs(array_unique($v),$k,4);
        }
    }

    /**
     * 实时输出消息
     *
     * @param $message
     */
    protected function output($message)
    {
        echo '[' . date('Y-m-d H:i:s', time()) . '] ' . $message . PHP_EOL;
        ob_flush();
    }
}