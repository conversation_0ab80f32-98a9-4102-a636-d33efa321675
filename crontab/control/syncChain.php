<?php

use Shopnc\db\driver\Mysql;

class syncChainControl extends BaseCronControl
{
    public function indexOp()
    {
        $rs = $this->newBJMQQueue('acp-hospital-sync', 'acp-hospital-sync.sz');
        if (!$rs) {
            return;
        }
        list($cnn, $queue) = $rs;

        $area = [
            '大湾区' => 1,
            '华南区' => 2,
            '华北区' => 3,
            '东北区' => 4,
            '华西区' => 5,
            '华中区' => 6,
            '华东区' => 7,
            '西北区' => 8,
            '上海区' => 9,
            '南京区' => 10,
            '苏皖区' => 11,
            '浙闽一区' => 12,
            '浙闽二区' => 13,
        ];

        $queue->consume(function ($envelope, $q) use ($area){
            $msg = $envelope->getBody();
            $data = json_decode($msg, true);
            $data = json_decode($data['body'], true);

            $this->log('门店同步-data:' . $msg);

            if ($data['hospital'] && $data['hospital']['systemId']) {
                $hospital_data = $data['hospital'];
                $chain_mod = new Model('chain');
                $chain_mod->beginTransaction();
                $has_chain_list = Model('chain')->where(['chain_erp_id' => $hospital_data['systemId']])->select();
                $has_chain_list1 = Model('chain')->where(['account_id' => $hospital_data['structOuterCode']])->select();
                if (!$has_chain_list && !$has_chain_list1) {
                    $insert = [];
                    $insert['store_id'] = 1;
                    $insert['chain_name'] = $hospital_data['clinicName'];
                    $insert['chain_address'] = $hospital_data['address'];
                    $insert['chain_phone'] = $hospital_data['hospitalPhone'];
                    $insert['chain_time'] = TIMESTAMP;
                    $insert['chain_close_time'] = $hospital_data['businessTimeEnd'];
                    $insert['chain_apply_time'] = strtotime($hospital_data['createTime']);
                    $insert['lng'] = $hospital_data['longitude'];
                    $insert['lat'] = $hospital_data['latitude'];
                    $insert['chain_state'] = $hospital_data['businessStatus'];
                    $insert['chain_erp_id'] = $hospital_data['systemId'];
                    $insert['account_id'] = $hospital_data['structOuterCode'];
                    $insert['store_id'] = $_SESSION['store_id'] ? $_SESSION['store_id'] : 1;
                    $insert['store_name'] = $_SESSION['store_name'];
                    $insert['chain_user'] = 'upet_e' . $hospital_data['systemId'];
                    $insert['chain_pwd'] = md5("123456");
                    $insert['area_info'] = $hospital_data['province'] . ' ' . $hospital_data['city'];
                    $insert['chain_address'] = $hospital_data['address'];
                    $insert['chain_opening_hours'] = $hospital_data['businessTimeStart'] . '-' . $hospital_data['businessTimeEnd'];
                    $insert['chain_apply_time'] = TIMESTAMP;
                    $insert['chain_cycle'] = 31;
                    $insert['transport_areas'] = serialize(array());
                    $insert['express_city'] = serialize(array());
                    $insert['jxaddress'] = $hospital_data['address'];
                    $insert['is_own'] = 1;
                    $insert['tx_lat'] = $hospital_data['latitude'];
                    $insert['tx_lng'] = $hospital_data['longitude'];
                    $insert['chain_erp_name'] = $hospital_data['clinicName'];
                    $insert['chain_erp_status'] = 1; //ERP同步
                    if ($area[$hospital_data['areaName']]) {
                        $insert['region_id'] = $area[$hospital_data['areaName']];
                    }
                    $insert['chain_erp_time'] = TIMESTAMP;
                    $res = Model('chain')->insert($insert);
                } else {
                    $updata = [];
                    $updata['store_id'] = 1;
                    $updata['chain_erp_name'] = $hospital_data['clinicName'];
                    $updata['chain_address'] = $hospital_data['address'];
                    $updata['chain_phone'] = $hospital_data['hospitalPhone'];
                    $updata['chain_state'] = $hospital_data['businessStatus'];
                    $updata['chain_close_time'] = $hospital_data['businessTimeEnd'];
                    $updata['chain_apply_time'] = strtotime($hospital_data['createTime']);
                    $updata['tx_lng'] = $hospital_data['longitude'];
                    $updata['tx_lat'] = $hospital_data['latitude'];
                    $updata['chain_opening_hours'] = $hospital_data['businessTimeStart'] . '-' . $hospital_data['businessTimeEnd'];
                    $updata['jxaddress'] = $hospital_data['address'];
                    $updata['chain_lat'] = $hospital_data['latitude'];
                    $updata['chain_lng'] = $hospital_data['longitude'];
                    if ($area[$hospital_data['areaName']]) {
                        $updata['region_id'] = $area[$hospital_data['areaName']];
                    }
                    $updata['chain_erp_time'] = TIMESTAMP;
                    if ($has_chain_list) {
                        $res = Model('chain')->where(['chain_erp_id' => $hospital_data['systemId']])->update($updata);
                    } else if ($has_chain_list1) {
                        $res = Model('chain')->where(['account_id' => $hospital_data['structOuterCode']])->update($updata);
                    }
                }
                if ($res > 0) {
                    $chain_mod->commit();

                    $q->ack($envelope->getDeliveryTag()); //手动发送ACK应答
                    $this->log('门店同步-success:' . json_decode($res));

                } else {
                    $chain_mod->rollback();
                }
            } else {
                $q->ack($envelope->getDeliveryTag()); //手动发送ACK应答
                $this->log('门店同步-fail:' . $msg);
            }

            Mysql::close();
        });

        $cnn->disconnect();
    }

    /**
     * @desc 虚拟订单核销未同步门店id数据
     * @data  2021-2-20
     */
    public function updateVrOrderChainIdOp()
    {
        $model = Model();
        $field = 'rec_id,erp_chargeoffhospitalid,chain_id,chain_name';
        $time = time() - 5 * 60;
        $where = "vr_usetime > $time and vr_state=1 AND (chain_id IS NULL OR chain_name IS NULL) AND erp_chargeoffhospitalid IS NOT NULL AND erp_chargeoffhospitalid <> 0";
        $vr_order_list = $model->table('vr_order_code')->field($field)->where($where)->select();
//        print_r(\Shopnc\Log::read());
        if ($vr_order_list) {
            $chain_list = $model->table('chain')->field('chain_erp_id,chain_id,chain_name')->limit(false)->select();
            $chain_key = array_column($chain_list, 'chain_erp_id');
            $chain_list = array_combine($chain_key, $chain_list);
            $i = 0;
            foreach ($vr_order_list as $v) {
                if ($chain_list[$v['erp_chargeoffhospitalid']]) {
                    $chain_id = $chain_list[$v['erp_chargeoffhospitalid']]['chain_id'];
                    $chain_name = $chain_list[$v['erp_chargeoffhospitalid']]['chain_name'];
                    $model->table('vr_order_code')->where('rec_id =' . $v['rec_id'])->update(['chain_id' => $chain_id, 'chain_name' => $chain_name]);
                    $i++;
                }
            }
            echo '虚拟订单核销未同步门店id数据:SUCCESS,共：' . $i;
        } else {
            echo '虚拟订单核销未同步门店id数据:NULL';
        }
    }
}
