<?php

use Shopnc\db\driver\Mysql;

class syncMergeMemberControl  extends BaseCronControl
{
    // 合并手机号
    public function indexOp()
    {
        $rs = $this->newBJMQQueue('scrm-merge-user', 'scrm-merge-user.sz-ds');
        if (!$rs) {
            return;
        }
        list($cnn, $queue) = $rs;

        $queue->consume(function ($envelope, $queue) {
            $msg = $envelope->getBody();
            $this->log('会员合并接收数据:' . $msg);

            $receive_data = json_decode($msg, true);

            if (is_array($receive_data) && !empty($receive_data)) {
                $body = $receive_data['body'];
                $data = json_decode($body, true);
                if (is_array($data) && !empty($data)) {
                    $model_member = Model('member');
                    if ($data['applicationDate']){
                        $data['applicationDate'] = substr($data['applicationDate'],0,10);
                        $date = date('Y-m-d',$data['applicationDate']) ?: '';
                    }else{
                        $date = date('Y-m-d',time());
                    }

                    $result = $model_member->addMemberMergeLog(
                        [
                            'newPhone'=>$data['targetMobile'],
                            'oldPhone'=>$data['originalMobile'],
                            'mqinfo'=>serialize($data),
                            'type'=>$data['memberType'] ?: 0,
                            'vipstime'=>$data['vipstime'] ?: 0,
                            'vipetime'=>$data['vipetime'] ?: 0,
                            'bzkstime'=>$data['bzkstime'] ?: 0,
                            'bzketime'=>$data['bzketime'] ?: 0,
                            'create_user'=>$data['createUser'] ?: '',
                            'applicant'=>$data['applicant'] ?: '',
                            'department'=>$data['department'] ?: '',
                            //applicationDate 时间戳 转换成 日期格式
                            'application_date'=>$date?:'',
                            'approval_record'=>$data['approvalRecord'] ?: '',
                            ]
                    );

                    if ($result) {
                        $queue->ack($envelope->getDeliveryTag());//手动发送ACK应答
                    }

                    Mysql::close();
                    return;
                }
            }

            Mysql::close();

            $queue->ack($envelope->getDeliveryTag());
            $this->log('会员合并接收数据异常:' . $msg);
        });

        $cnn->disconnect();
    }
}
