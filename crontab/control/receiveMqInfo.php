<?php

use Shopnc\db\driver\Mysql;
use Upet\Modules\Member\Queues\UpdateMemberVipQueue;

defined('InShopNC') or exit('Access Invalid!');

class receiveMqInfoControl extends BaseCronControl
{
    public function indexOp()
    {

    }

    public function receiveMemberVipInfoOp()
    {
        $rs = $this->newBaseMQQueue('datacenter', 'dc-sz-card-notifty');
        if (!$rs) {
            return;
        }
        list($cnn, $queue) = $rs;

        // 使用安全的消费方法
        $this->safeConsume($queue, function ($envelope, $queue) {
            $receive_msg = $envelope->getBody();
            $data = json_decode($receive_msg, true);
            if (is_array($data) && !empty($data)) {
                $CardStatus = $data['CardStatus'];
                $msg = "";
                $stateStr = str_replace(array("0", "1", "2", "3", "4"), array('开卡中', '开卡', '退款中', "已退款", "已过期"), $CardStatus);
                if ($CardStatus != '1') { // 1-有效，其他都是无效的。会员卡状态(0-开卡中，1-正常，2-退款中，3-已退款，4-已过期)
                    if ($data['CardType'] == "1") {
                        //$update_data['member_isbzk'] = 0;
                        $msg = "保障卡";
                    } else {
                        //$update_data['member_isvip'] = 0;
                        $msg = "会员卡";
                    }
                } else {
                    if ($data['CardType'] == "1") {
                        //$update_data['member_isbzk'] = 1;
                        $msg = "保障卡";
                    } else {
                        //$update_data['member_isvip'] = 1;
                        $msg = "会员卡";
                    }
                }
                $content = "用户:" . $data['Mobile'] . $msg . $stateStr;

                $member_model = Model('member');
                $member_info = $member_model->getMemberInfo(['member_mobile' => $data['Mobile']], "member_id");
                $mqlog_data = array();
                $mqlog_data['mv_member_id'] = $member_info['member_id'] ? $member_info['member_id'] : 0;
                $mqlog_data['mv_msg'] = $content;
                $mqlog_data['mv_time'] = time();
                $mqlog_data['mv_push_infos'] = serialize($receive_msg);
                $mqlog_data['mv_state'] = $data['CardStatus'];
                $mqlog_data['mv_status'] = 0;
                $result = $member_model->addMemberVipLog($mqlog_data);
                if ($result) {
                    $queue->ack($envelope->getDeliveryTag());//手动发送ACK应答
                }
            }else{
                $queue->ack($envelope->getDeliveryTag());//手动发送ACK应答
            }

            // 在长连接情况下，框架自带的模型没有断线重连功能，另外考虑，多处保持数据库连接容易导致数据库连接超限
            // 同时消息接大多数情况下只有单条，所以消费完直接关闭数据库连接
            Mysql::close();
        }, 10); // 10秒超时

        $cnn->disconnect();
    }

    /**
     * 订阅数据中心核销码 核销信息
     */
    public function receiveCodeInfoOp()
    {
        $rs = $this->newBaseMQQueue('ordercenter', 'dc-sz-chargeoff-to-mall');
        if (!$rs) {
            return;
        }
        list($cnn, $queue) = $rs;

        // 使用安全的消费方法
        $this->safeConsume($queue, function ($envelope, $queue) {
            $msg = $envelope->getBody();
            $model_order = Model('order');
            $log_data = array();
            $log_data['or_push_infos'] = serialize($msg);
            $log_data['or_type'] = 1;
            $log_data['or_addtime'] = time();
            $result = $model_order->addOrderRabbitLog($log_data);
            if ($result) {
                $queue->ack($envelope->getDeliveryTag());//手动发送ACK应答
            }

            // 在长连接情况下，框架自带的模型没有断线重连功能，另外考虑，多处保持数据库连接容易导致数据库连接超限
            // 同时消息接大多数情况下只有单条，所以消费完直接关闭数据库连接
            Mysql::close();
        }, 10); // 10秒超时

        $cnn->disconnect();
    }

    // 收数据中心发货通知
    public function receiveErpDeliveryNoticeOp()
    {
        $rs = $this->newDCMQQueue('ordercenter', 'dc-sz-qqd-dispatch');
        if (!$rs) {
            return;
        }
        list($cnn, $queue) = $rs;

        // 使用安全的消费方法
        $this->safeConsume($queue, function ($envelope, $queue) {
            $msg = $envelope->getBody();
            $data = json_decode($msg, true);
            $model_order = Model('order');
            $mqlog_data = array();
            $mqlog_data['order_sn'] = $data['order_id'];
            $mqlog_data['exchange'] = "ordercenter";
            $mqlog_data['queuename'] = "dc-sz-qqd-dispatch";
            $mqlog_data['mqinfos'] = serialize($msg);
            $mqlog_data['type'] = 1;
            $mqlog_data['state'] = 0;
            $result = $model_order->addOrderMqlog($mqlog_data);
            if ($result) {
                $queue->ack($envelope->getDeliveryTag());//手动发送ACK应答
            }

            // 在长连接情况下，框架自带的模型没有断线重连功能，另外考虑，多处保持数据库连接容易导致数据库连接超限
            // 同时消息接大多数情况下只有单条，所以消费完直接关闭数据库连接
            Mysql::close();
        }, 10); // 10秒超时

        $cnn->disconnect();
    }
}