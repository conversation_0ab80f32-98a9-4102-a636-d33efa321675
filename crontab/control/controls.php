<?php

/**
 * 父类
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Log;

defined('InShopNC') or exit('Access Invalid!');

class BaseCronControl
{

    public function shutdown()
    {
        exit("success at " . date('Y-m-d H:i:s', TIMESTAMP) . "\n");
    }

    public function __construct()
    {
        register_shutdown_function(array($this, "shutdown"));
    }

    /**
     * 记录日志
     * @param string $content 日志内容
     * @param boolean $if_sql 是否记录SQL
     */
    protected function log($content, $if_sql = true)
    {
        if ($if_sql) {
            $log = Log::read();
            if (!empty($log) && is_array($log)) {
                $content .= end($log);
            }
        }
        Log::record('queue\\' . $content, Log::RUN);
    }

    /**
     * 更新订单中的佣金比例[多个地方调用，写到父类中]
     */
    protected function _order_commis_rate_update()
    {

        //实物订单，每次最多处理50W个商品佣金
        $_break = false;
        $model_order = Model('order');
        $store_bind_class = Model('store_bind_class');
        $model_refund_return = Model('refund_return');
        $model_goods = Model('goods');
        //获取前一天的日期的时间戳
        $yesterdate = date("Y-m-d", strtotime("-1 day"));
        for ($i = 0; $i < 5000; $i++) {
            if ($_break) {
                break;
            }
            $model_order->beginTransaction();
            $where = [];
            $where['commis_rate'] = 200;
            $where['store_id'] = ['in', '1,2'];
            $where['add_time'] = array('gt', strtotime($yesterdate));
            $goods_list = $model_order->getOrderGoodsList($where, 'rec_id,order_id,goods_id,store_id,gc_id', 100, null, '');
            if (!empty($goods_list)) {
                $commis_rate_list = $store_bind_class->getStoreGcidCommisRateList($goods_list);
                //更新订单商品佣金值
                foreach ($goods_list as $v) {
                    if (empty($v['gc_id'])) {
                        $goods_info = $model_goods->getGoodsInfo(array('goods_id' => $v['goods_id']), 'goods_serial,goods_body,goods_commonid,spec_name,goods_spec,gc_id');
                        //规格
                        $_tmp_name = unserialize($goods_info['spec_name']);
                        $_tmp_value = unserialize($goods_info['goods_spec']);
                        $_tmp_spec = '';
                        if (is_array($_tmp_name) && is_array($_tmp_value)) {
                            $_tmp_name = array_values($_tmp_name);
                            $_tmp_value = array_values($_tmp_value);
                            foreach ($_tmp_name as $sk => $sv) {
                                $_tmp_spec .= $sv . '：' . $_tmp_value[$sk] . ',';
                            }
                        }
                        $model_order->editOrderGoods(array('goods_spec' => rtrim($_tmp_spec, ','), 'gc_id' => $goods_info['gc_id']), array('rec_id' => $v['rec_id']));
                        $v['gc_id'] = $goods_info['gc_id'];
                    }

                    //如果未查到店铺或分类ID，则佣金置0
                    if (!isset($commis_rate_list[$v['store_id']][$v['gc_id']])) {
                        $commis_rate = 0;
                    } else {
                        $commis_rate = $commis_rate_list[$v['store_id']][$v['gc_id']];
                    }
                    $update = $model_order->editOrderGoods(array('commis_rate' => $commis_rate), array('rec_id' => $v['rec_id']));
                    if (!$update) {
                        $this->log('更新实物订单商品佣金值失败');
                        $_break = true;
                        break;
                    }
                    $update = $model_refund_return->editRefundReturn(array('order_id' => $v['order_id'], 'goods_id' => $v['goods_id']), array('commis_rate' => $commis_rate));
                    if (!$update) {
                        $this->log('更新实物订单退款佣金值失败');
                        $_break = true;
                        break;
                    }
                }
            } else {
                break;
            }
            $model_order->commit();
        }
        $model_order->commit();

        //虚拟订单，每次最多处理50W个商品佣金
        $_break = false;
        $model_order = Model('vr_order');
        $model_vr_refund = Model('vr_refund');

        for ($i = 0; $i < 5000; $i++) {
            if ($_break) {
                break;
            }
            $model_order->beginTransaction();
            $condition['commis_rate'] = 200;
            //3-宠商云排除(走新项目eshop) 4-百林康源排除(无此业务)
            $condition['store_id'] = array('in', '1,2');
            $condition['add_time'] = array('gt', strtotime($yesterdate));
            $goods_list = $model_order->getOrderList($condition, '', 'order_id,store_id,gc_id', '', 100);
            if (!empty($goods_list)) {
                $commis_rate_list = $store_bind_class->getStoreGcidCommisRateList($goods_list);
                //更新订单商品佣金值
                foreach ($goods_list as $v) {
                    //如果未查到店铺或分类ID，则佣金置0
                    if (!isset($commis_rate_list[$v['store_id']][$v['gc_id']])) {
                        $commis_rate = 0;
                    } else {
                        $commis_rate = $commis_rate_list[$v['store_id']][$v['gc_id']];
                    }
                    $update = $model_order->editOrder(array('commis_rate' => $commis_rate), array('order_id' => $v['order_id']));
                    if (!$update) {
                        $this->log('更新虚拟订单商品佣金值失败');
                        $_break = true;
                        break;
                    }
                    $update = $model_order->editOrderCode(array('commis_rate' => $commis_rate), array('order_id' => $v['order_id']));
                    if (!$update) {
                        $this->log('更新虚拟订单兑换码佣金值失败');
                        $_break = true;
                        break;
                    }
                    $update = $model_vr_refund->editRefund(array('order_id' => $v['order_id']), array('commis_rate' => $commis_rate));
                    if (!$update) {
                        $this->log('更新虚拟订单商品退款佣金值失败');
                        $_break = true;
                        break;
                    }
                }
            } else {
                break;
            }
            $model_order->commit();
        }
        $model_order->commit();
    }

    // 中台通用mq
    public function newDCMQQueue($exchangeName, $queueName)
    {
        return $this->newMQQueue([
            'login' => C('cloud_mq_login'),
            'password' => C('cloud_mq_password'),
            'host' => C('cloud_mq_host'),
            'post' => C('cloud_mq_port'),
            'vhost' => C('cloud_mq_vhost'),
        ], $exchangeName, $queueName);
    }

    // base服务的mq，线上vhost不同，包括核销、卡通知
    public function newBaseMQQueue($exchangeName, $queueName)
    {
        return $this->newMQQueue([
            'login' => C('dc_mq_login'),
            'password' => C('dc_mq_password'),
            'host' => C('dc_mq_host'),
            'post' => C('dc_mq_port'),
            'vhost' => C('dc_mq_vhost'),
        ], $exchangeName, $queueName);
    }

    // 北京mq
    public function newBJMQQueue($exchangeName, $queueName)
    {
        return $this->newMQQueue([
            'login' => C('bj_mq_login'),
            'password' => C('bj_mq_password'),
            'host' => C('bj_mq_host'),
            'post' => C('bj_mq_port'),
            'vhost' => C('bj_mq_vhost'),
        ], $exchangeName, $queueName, '#');
    }

    // 北京mq
    public function newShrMQQueue($exchangeName, $queueName)
    {
        return $this->newMQQueue([
            'login' => C('shr_mq_login'),
            'password' => C('shr_mq_password'),
            'host' => C('shr_mq_host'),
            'post' => C('shr_mq_port'),
            'vhost' => C('shr_mq_vhost'),
        ], $exchangeName, $queueName, '#');
    }

    public function newMQQueue($config, $exchangeName, $queueName, $routeName = null)
    {
        try {
            // 添加连接超时设置
            $config['heartbeat'] = 15;
            $config['connect_timeout'] = 10;//添加连接超时
            $config['read_timeout'] = 30;//添加读取超时
            $config['write_timeout'] = 30;//添加写入超时

            $cnn = new AMQPConnection($config);

            if (!$cnn->connect()) {
                $this->log("Cannot connect to the broker: " . json_encode($config));
                return false;
            }

            $ch = new AMQPChannel($cnn);
            $ex = new AMQPExchange($ch);
            $ex->setName($exchangeName);
            $ex->setType(AMQP_EX_TYPE_DIRECT);
            $ex->setFlags(AMQP_DURABLE);
            $queue = new AMQPQueue($ch);
            $queue->setName($queueName);
            $queue->setFlags(AMQP_DURABLE);
            $queue->bind($ex->getName(), $routeName ?: $queueName);

            return [$cnn, $queue];

        } catch (AMQPConnectionException $e) {
            log_error("AMQP Connection Error: " . $e->getMessage() . " Config: " . json_encode($config));
            return false;
        } catch (Exception $e) {
            log_error("AMQP General Error: " . $e->getMessage());
            return false;
        }
    }
}