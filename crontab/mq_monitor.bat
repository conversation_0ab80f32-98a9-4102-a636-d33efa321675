@echo off
REM MQ消费者监控和重启脚本 (Windows版本)
REM 用于监控和管理所有AMQP消费者进程

setlocal enabledelayedexpansion

set LOG_DIR=D:\data\work\logs
set SCRIPT_DIR=D:\www\rp-field\crontab
set PHP_PATH=C:\php\php.exe

REM 确保日志目录存在
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM 定义所有MQ消费者进程
set "PROCESSES=syncChain syncMergeMember syncStock syncStaffInfo receiveCodeInfo receiveErpDeliveryNotice receiveMemberVipInfo"

goto %1

:start
echo [%date% %time%] 启动所有MQ消费者进程
call :monitor_all
goto :eof

:stop
echo [%date% %time%] 停止所有MQ消费者进程
call :stop_all
goto :eof

:restart
echo [%date% %time%] 重启所有MQ消费者进程
call :stop_all
timeout /t 5 /nobreak >nul
call :monitor_all
goto :eof

:status
echo === MQ消费者进程状态 ===
echo 时间: %date% %time%
echo ------------------------
for %%p in (%PROCESSES%) do (
    call :check_process %%p
    if !count! gtr 0 (
        echo ✓ %%p: 运行中 ^(进程数: !count!^)
    ) else (
        echo ✗ %%p: 未运行
    )
)
echo ------------------------
goto :eof

:monitor
call :monitor_all
goto :eof

:clean
echo [%date% %time%] 清理旧日志文件
forfiles /p "%LOG_DIR%" /m *.log /d -7 /c "cmd /c del @path" 2>nul
goto :eof

:monitor_all
echo [%date% %time%] 开始监控所有MQ消费者进程
for %%p in (%PROCESSES%) do (
    call :check_process %%p
    if !count! equ 0 (
        echo [%date% %time%] 进程 %%p 未运行，正在启动...
        call :start_process %%p
    ) else (
        echo [%date% %time%] 进程 %%p 正在运行 ^(进程数: !count!^)
    )
)
goto :eof

:stop_all
for %%p in (%PROCESSES%) do (
    call :check_process %%p
    if !count! gtr 0 (
        echo [%date% %time%] 停止进程: %%p
        call :stop_process %%p
    )
)
goto :eof

:check_process
set process_name=%1
set count=0
for /f %%i in ('tasklist /fi "imagename eq php.exe" /fo csv ^| find /c "%process_name%"') do set count=%%i
goto :eof

:start_process
set process_name=%1
set log_file=%LOG_DIR%\%process_name%.log

if "%process_name%"=="receiveCodeInfo" (
    start /b "" "%PHP_PATH%" "%SCRIPT_DIR%\index.php" receiveMqInfo receiveCodeInfo >> "%log_file%" 2>&1
) else if "%process_name%"=="receiveErpDeliveryNotice" (
    start /b "" "%PHP_PATH%" "%SCRIPT_DIR%\index.php" receiveMqInfo receiveErpDeliveryNotice >> "%log_file%" 2>&1
) else if "%process_name%"=="receiveMemberVipInfo" (
    start /b "" "%PHP_PATH%" "%SCRIPT_DIR%\index.php" receiveMqInfo receiveMemberVipInfo >> "%log_file%" 2>&1
) else (
    start /b "" "%PHP_PATH%" "%SCRIPT_DIR%\index.php" %process_name% >> "%log_file%" 2>&1
)

echo [%date% %time%] 进程 %process_name% 已启动 >> "%log_file%"
goto :eof

:stop_process
set process_name=%1
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq php.exe" /fo csv ^| find "%process_name%"') do (
    taskkill /pid %%i /f >nul 2>&1
)
goto :eof

:help
echo 用法: %0 {start^|stop^|restart^|status^|monitor^|clean}
echo.
echo 命令说明:
echo   start   - 启动所有未运行的MQ消费者进程
echo   stop    - 停止所有MQ消费者进程
echo   restart - 重启所有MQ消费者进程
echo   status  - 显示所有进程状态
echo   monitor - 监控并启动未运行的进程
echo   clean   - 清理旧日志文件
goto :eof

:default
call :help
