<?php
/**
 * 关键词高亮演示文件
 * 
 * 这个文件展示了各种关键词高亮的效果
 */

class KeywordHighlightDemo {
    
    public function demonstrateKeywords() {
        // TODO: 实现用户登录功能
        $userLogin = false;
        
        // FIXME: 这里有一个潜在的安全漏洞
        $password = $_POST['password'] ?? '';
        
        // NOTE: 记住要验证用户输入
        if (empty($password)) {
            // BUG: 密码验证逻辑有问题
            return false;
        }
        
        // HACK: 临时解决方案，需要重构
        $hashedPassword = md5($password);
        
        // XXX: 这个方法已经过时了
        $result = $this->oldAuthMethod($hashedPassword);
        
        // DEBUG: 输出调试信息
        error_log("Login attempt for user: " . $username);
        
        // REVIEW: 这段代码需要代码审查
        return $result;
    }
    
    private function processUserData($data) {
        // TODO: 添加数据验证
        // FIXME: 处理特殊字符
        // NOTE: 这个方法处理用户提交的数据
        
        foreach ($data as $key => $value) {
            // BUG: 没有检查数组键的有效性
            $cleanValue = htmlspecialchars($value);
            
            // HACK: 快速修复XSS问题
            $data[$key] = strip_tags($cleanValue);
        }
        
        return $data;
    }
    
    public function databaseQuery($sql) {
        // XXX: 使用了过时的mysql扩展
        // TODO: 迁移到PDO
        // FIXME: SQL注入风险
        
        $connection = mysql_connect('localhost', 'user', 'pass');
        
        // DEBUG: 记录SQL查询
        error_log("Executing SQL: " . $sql);
        
        // REVIEW: 需要添加错误处理
        $result = mysql_query($sql, $connection);
        
        return $result;
    }
    
    /**
     * 文件上传处理
     * 
     * TODO: 添加文件类型验证
     * FIXME: 文件大小限制不起作用
     * NOTE: 支持的文件类型：jpg, png, gif, pdf
     */
    public function handleFileUpload($file) {
        // BUG: 没有检查文件是否真的是图片
        $allowedTypes = ['jpg', 'png', 'gif', 'pdf'];
        
        // HACK: 简单的文件类型检查
        $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
        
        if (!in_array($fileExtension, $allowedTypes)) {
            // DEBUG: 记录不允许的文件类型
            error_log("Rejected file type: " . $fileExtension);
            return false;
        }
        
        // XXX: 使用了不安全的文件移动方法
        // REVIEW: 需要更安全的文件处理方式
        $uploadPath = '/uploads/' . $file['name'];
        
        return move_uploaded_file($file['tmp_name'], $uploadPath);
    }
    
    /**
     * 缓存管理
     */
    public function cacheManager() {
        // TODO: 实现Redis缓存
        // NOTE: 当前使用文件缓存
        
        $cacheFile = '/tmp/cache_' . md5($key) . '.tmp';
        
        if (file_exists($cacheFile)) {
            // DEBUG: 从缓存读取数据
            $data = file_get_contents($cacheFile);
            
            // FIXME: 没有检查缓存是否过期
            return unserialize($data);
        }
        
        // HACK: 如果缓存不存在，返回空数组
        return [];
    }
    
    /**
     * API 接口处理
     * 
     * REVIEW: 整个API需要重新设计
     * TODO: 添加API版本控制
     * FIXME: 错误处理不完整
     */
    public function apiHandler($request) {
        // BUG: 没有验证API密钥
        $apiKey = $request['api_key'] ?? '';
        
        // XXX: 硬编码的API密钥
        if ($apiKey !== 'secret_key_123') {
            // DEBUG: 记录无效的API访问
            error_log("Invalid API key: " . $apiKey);
            return ['error' => 'Unauthorized'];
        }
        
        // NOTE: 处理不同的API端点
        switch ($request['endpoint']) {
            case 'users':
                // TODO: 实现用户列表API
                return $this->getUserList();
                
            case 'orders':
                // FIXME: 订单API返回敏感信息
                return $this->getOrderList();
                
            default:
                // HACK: 默认返回空结果
                return ['data' => []];
        }
    }
}

// TODO: 添加单元测试
// FIXME: 这个类需要重构
// NOTE: 这只是一个演示文件
// BUG: 存在多个安全问题
// HACK: 包含临时解决方案
// XXX: 使用了过时的方法
// DEBUG: 包含调试代码
// REVIEW: 需要代码审查

?>
