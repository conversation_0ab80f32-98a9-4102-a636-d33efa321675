@charset "utf-8";
/* CSS Document */

body { background: #FFF; margin: 0; padding: 0;}
.wrapper { width: 1000px; margin: 0 auto;}
/* =====================
 * 表单元素格式化及伪类效果
 * ===================== */
input[type="text"], input[type="password"], input.text, input.password { font: 12px/18px Arial; color: #777; background-color: #FFF; vertical-align: top; display: inline-block; height: 18px; padding: 3px 1px; border: solid 1px #CCC; outline: 0 none;}
input[type="text"]:focus, input[type="password"]:focus, input.text:focus, input.password:focus, textarea:focus { color: #333; border-color: #75B9F0; box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); outline: 0 none;}
input[type="text"].error, input[type="password"].error, textarea.error { border-color: #ED6C4F; box-shadow: 0 0 0 2px rgba(232, 71, 35, 0.15); outline: 0 none;}
textarea, .textarea { font: 12px/20px Arial; color: #777; background-color: #FFF; vertical-align: top; display: inline-block; height: 60px; padding: 4px; border: solid 1px #CCC; outline: 0 none;}
input.error, input:hover.error, input:focus.error, textarea.error, textarea.error:hover, textarea.error:focus { background-color: #FFBFBF; border: dotted 1px #D90000;}


input[type="file"] {  background-color: #FFF; vertical-align: middle; display: inline-block; width: 206px;}
input[type="button"] { line-height: 22px; color: #555; background-color: #FEFFFF; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FEFFFF', endColorstr='#F7FCFF'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FEFFFF), to(#F7FCFF)) ; background-image: -moz-linear-gradient(top, #FEFFFF, #F7FCFF); vertical-align: middle; display: inline-block; height: 22px; padding: 0 4px; border: 1px solid; border-color: #DBE9EB #BCD7DA #BCD7DA #DBE9EB; cursor: pointer; box-shadow: 1px 1px 0 rgba(153,153,153,0.15);}
input[type="button"] { *display: inline;}
select, .select { color: #777; background-color: #FFF; height: 26px; vertical-align: middle; *display: inline; padding: 2px; border: solid 1px #CCC; *zoom:1;}
select option, .select option { line-height: 20px; display: block; height: 20px; padding: 2px;}
input[type="checkbox"] { vertical-align: middle; margin-right: 5px;}
label.error { color: #333; line-height: 18px; background: #FFEEEE  url(../images/joinin_pic.png) no-repeat -395px -135px; vertical-align: middle; display: inline-block; height: 18px; padding: 1px 5px 1px 20px; margin-left: 4px; border: solid 1px #FCC;}
label.error { *display: inline;}

/*顶部内容样式*/
.header { width: 1000px; height: 80px; margin: 0 auto;}
.header_logo { width: 240px; height: 60px; text-align: left; float: left; margin: 12px auto auto 10px;}
.header_logo img { max-width: 240px; max-height: 60px;}
.header_menu { width: 480px; height: 24px; float: right; margin-top: 30px}
.header_menu li { text-align: center; width: 160px; float: left;}
.header_menu li a { font: 600 14px/24px "microsoft yahei"; color: #555; margin: 0 auto;}
.header_menu li a i { background: url(../images/joinin_pic.png) no-repeat; vertical-align: middle; display: inline-block; *display: inline; width: 24px; height: 24px; margin-right: 6px; *zoom: 1;}
.header_menu li a.joinin i { background-position: 0 0;}
.header_menu li a.login i { background-position: -30px 0;}
.header_menu li a.faq i { background-position: -60px 0;}
.header_menu li a.joinin:hover, .header_menu li.current a.joinin { text-decoration: none; color: #27A9E3;}
.header_menu li a.login:hover, .header_menu li.current a.login { text-decoration: none; color: #28B779;}
.header_menu li a.faq:hover, .header_menu li.current a.faq { text-decoration: none; color: #FFB748;}
.header_menu li a.joinin:hover i, 
.header_menu li.current a.joinin i { background-position: -90px 0;}
.header_menu li a.login:hover i, 
.header_menu li.current a.login i { background-position: -120px 0;}
.header_menu li a.faq:hover i, 
.header_menu li.current a.faq i { background-position: -150px 0;}
.header_line { font-size: 0; line-height: 2px; background-color: #D93600; width: 100%; height: 2px; border-bottom: solid 2px #EEE; position: relative; z-index: 1;}
.header_line span { font-size: 0; line-height: 2px; background: url(../images/joinin_pic.png) no-repeat 0 -30px; display: block; width: 480px; height: 2px; margin-left: 20px; position: absolute; z-index: 1; left: 50%; top: 0;}


/* 满屏背静切换焦点图 */
.banner { width: 100%; height: 350px; margin-top: -2px; position: relative; z-index: 1;}
.user-box { filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#3F000000', endColorstr='#3F000000');background:rgba(0,0,0,0.25); margin-left: -470px; width: 280px; height: 300px; position: absolute; z-index: 9; top: 25px; left: 50%; border-radius: 5px;}
.user-login { width: 100%; height: 100%; position: relative; z-index: 1; overflow: hidden;}
.user-login h3 { font: 600 16px/24px "microsoft yahei"; color: #FFF; padding: 12px 10px 4px 10px; margin: 0 10px 10px; box-shadow: 0 1px 0 rgba(0,0,0,0.1);}
.user-login h3 em { font-size: 12px; color: #FC9;}
.user-login dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; padding: 10px 0; margin: 0 10px;}
.user-login dt, .user-login dd { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; *zoom: 1;}
.user-login dt { line-height: 28px; color: #FFF; text-align: right; width: 28%; margin-right: 2%;}
.user-login dd { color: #FFF; text-align: left; width: 70%; position: relative; z-index: 1;}
.user-login dd .text { width: 150px; border: none !important; padding: 4px !important;}
.user-login dd .button { font: 600 14px/30px "microsoft yahei"; color: #FFF; background: #FF7F00 none; width: 80px; height: 30px; margin-right: 10px; border: none; border-radius: 4px; cursor: pointer; box-shadow: 2px 2px 0 rgba(0,0,0,0.25);}
.user-login dd a { color: #FFF;}
.user-login label.error { color: #FC6; line-height: 20px; background: none; display: block; padding: 0; margin: 0; border: 0; position: absolute; z-index: 1; top: 26px; left: 2px; text-shadow: 1px 1px 0 rgba(0,0,0,0.1);}
.user-login .register { color: #CCC; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#7F000000', endColorstr='#7F000000'); background:rgba(0,0,0,0.5); text-align: center; width: 100%; padding: 10px 0; border-radius: 0 0 5px 5px; position: absolute; z-index: 1; bottom: 0; left: 0;}
.user-login .register a { color: #FF0;}


.user-joinin { width: 100%; height: 100%; position: relative; z-index: 1; overflow: hidden;}
.user-joinin h3 { font: 600 16px/24px "microsoft yahei"; color: #FFF; padding: 12px 10px 8px 10px; margin: 0 10px 10px; box-shadow: 0 1px 0 rgba(0,0,0,0.1);}
.user-joinin dl { font-size: 12px; line-height: 22px; color: #FFF; margin: 0 20px;}
.user-joinin dt { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; *zoom: 1;}
.user-joinin dd { background: url(../images/joinin_pic.png) no-repeat -470px -60px; padding: 10px 0 10px 10px;}
.user-joinin dd a { color: #FFB748;}
.user-joinin .bottom { color: #CCC; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#7F000000', endColorstr='#7F000000'); background:rgba(0,0,0,0.5); text-align: center; width: 100%; padding: 15px 0; border-radius: 0 0 5px 5px; position: absolute; z-index: 1; bottom: 0; left: 0;}
.user-joinin .bottom a { font: 600 14px/20px "microsoft yahei"; color: #FFF; background: #FF7F00 none; display: inline-block; *display: inline; height: 20px; padding: 5px 10px; margin-right: 10px; border: none; border-radius: 4px; cursor: pointer; box-shadow: 2px 2px 0 rgba(0,0,0,0.25); zoom: 1;}


.full-screen-slides { width: 100%; height: 350px; position: relative; z-index: 1;}
.full-screen-slides li { background-repeat: no-repeat; background-position: center top; width: 100%; height: 100%; position: absolute; z-index: 1; top: 0; left: 0;}
.full-screen-slides-pagination { font-size: 0; *word-spacing:-1px/*IE6、7*/; text-align: center; display: block; list-style:none; width: 100px; height: 10px; margin-left: -50px; position: absolute; z-index: 9; bottom: 15px; left: 50%;}
.full-screen-slides-pagination li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display:inline/*IE6、7*/; list-style:none; width: 12px; height: 12px; margin-left: 8px; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#BF000000', endColorstr='#BF000000');background:rgba(0,0,0,0.75); overflow: hidden; border-radius: 6px; cursor: pointer; *zoom: 1;}
.full-screen-slides-pagination a { width:100%; height:100%; padding:0; margin:0; text-indent: -9999px;}
.full-screen-slides-pagination .current { background: #FFF; box-shadow: inset 1px 1px 1px rgba(0,0,0,0.5);}

.indextip { color: #777; background-color: #EEE; width: 100%; height: 60px;}
.indextip .container { width: 1000px; height: 60px; margin: 0 auto; overflow: hidden;} 
.indextip .title { width: 60px; height: 48px; display: inline-block; *display: inline; padding: 6px 12px 6px 24px; *zoom: 1;}
.indextip .title i { background: url(../images/joinin_pic.png) no-repeat -190px 0; display: block; width: 24px; height: 24px; float: left; margin: 0 18px;}
.indextip .title h3 { font: 14px/24px "microsoft yahei"; text-align: center; width: 100%;  height: 24px; clear: both;}
.indextip .content { font: 12px/20px "microsoft yahei"; vertical-align: top; display: inline-block; *display: inline; width: 880px; padding: 10px; *zoom: 1;}

.breadcrumb { font-size: 0; *word-spacing:-1px/*IE6、7*/; width: 1000px; height: 20px; padding: 10px 0; margin: 0 auto;}
.breadcrumb span { font-size: 12px; color: #999; vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; *zoom: 1;}
.breadcrumb a { color: #777;}
.breadcrumb .icon-home { background: url(../images/joinin_pic.png) no-repeat -240px 0; width: 16px; height: 16px; margin-right: 4px;}
.breadcrumb .arrow { line-height: 20px; color: #AAAAAA; font-family: "宋体"; margin: 0 6px;}

.main { width: 1000px; padding: 0; margin: 0 auto; overflow: hidden;}
.main .index-title { font: 600 16px/20px "microsoft yahei"; color: #333; width: 950px; padding: 5px 15px; margin: 0 auto; border-bottom: dotted 1px #CCC;}
.joinin-index-step { font-size: 0; *word-spacing:-1px/*IE6、7*/; width: 800px; height: 110px; margin: 30px auto; overflow: hidden;}
.joinin-index-step span { font-size: 12px; vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; *zoom: 1;}
.joinin-index-step span.step { line-height: 20px; text-align: center; width: 80px;}
.joinin-index-step span.step i { background: url(../images/joinin_pic.png) no-repeat; display: block; width: 80px; height: 80px; margin-bottom: 10px;}
.joinin-index-step span.step i.a { background-position: 0 -40px;}
.joinin-index-step span.step i.b { background-position: -80px -40px;}
.joinin-index-step span.step i.c { background-position: -160px -40px;}
.joinin-index-step span.step i.d { background-position: -240px -40px;}
.joinin-index-step span.step i.e { background-position: -320px -40px;}
.joinin-index-step span.arrow { background: url(../images/joinin_pic.png) no-repeat -410px -40px; width: 14px; height: 22px; margin: 0 41px;}


/*页内整体布局*/
.main { width: 1000px; padding: 0; margin: 0 auto 10px auto; overflow: hidden;}
/*左侧边栏菜单*/
.sidebar { width: 206px; float: left; border: solid 1px #D7D7D7;}
.sidebar .title { background-color: #F5F5F5; height: 36px; border-top: solid 1px #D7D7D7; margin-top: -1px;}
.sidebar .title h3 { font: 600 14px/20px "microsoft yahei"; color: #333333; height: 20px; padding: 8px 10px;}
.sidebar .content { width: 190px; margin: 0 auto;}
.sidebar .content dl { overflow: hidden;}
.sidebar .content dt { line-height: 20px; font-weight: 600; color: #555; height: 20px; padding: 10px 0 9px 0; border-bottom: solid 1px #F5F5F5; cursor: default; }
.sidebar .content dt.current { color: #FFF; background-color: #27A9E3;}
.sidebar .content dt i { background: url(../images/joinin_pic.png) no-repeat; vertical-align: middle; display: inline-block; *display: inline/*IE7*/; width: 11px; height: 11px; margin-right: 10px; margin-left: 6px; *zoom: 1/*IE7*/;}
.sidebar .content dt i.hidle { background-position: -300px 0}
.sidebar .content dt i.show { background-position: -280px 0}
.sidebar .content dt.current i.hidle { background-position: -300px -15px;}
.sidebar .content dt.current i.show { background-position: -280px -15px;}
.sidebar .content dd { margin-bottom: 10px;}
.sidebar .content ul { width: 100%; padding: 6px 0; }
.sidebar .content ul li { padding: 6px 0;}
.sidebar .content ul li a { color: #999;}
.sidebar .content ul li i { background: url(../images/joinin_pic.png) no-repeat -320px 0; vertical-align: middle; display: inline-block; width: 3px; height: 5px; margin-right: 10px; margin-left: 14px;}
.sidebar .content ul li.current { color: #FFF; background-color: #27A9E3;}
/*右侧内容部分*/
.right-layout { width: 778px; min-height: 500px; float: right; border: solid 1px #D7D7D7;}
/*申请流程*/
.joinin-step ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; width: 616px; height: 46px; margin: 40px auto 20px auto;}
.joinin-step ul li { font-size: 12px; background: url(../images/joinin_pic.png) no-repeat 0 -155px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; width: 200px; height: 16px; position: relative; z-index: 1; *zoom: 1;}
.joinin-step ul li.step1 { background-position: -52px -130px; width: 68px;}
.joinin-step ul li.step6 { width: 128px;}
.joinin-step ul li.step1_0 { background-position: -52px -155px; width: 120px;}
.joinin-step ul li.step6_0 { width: 128px;}
.joinin-step ul li.current { background-position: -240px -155px;}
.joinin-step ul li.step1.current { background-position: -172px -130px;}
.joinin-step ul li.step1_0.current { background-position: -351px -155px;}
.joinin-step ul li span { color: #999; text-align: center; width: 120px; height: 20px; margin-left: -40px; position: absolute; z-index: 1; bottom: -30px; left: 50%;}
.joinin-step ul li.step1 span { margin-left: -86px;}
.joinin-step ul li.step6 span { margin-left: 0px;}

.joinin-step ul li.step1_0 span { margin-left: -106px;}
.joinin-step ul li.step6_0 span { margin-left: 0px;}
/*提示文字*/
.alert { color: #C09853; background-color: #FCF8E3; padding: 8px 35px 8px 14px; margin: 10px auto; border: 1px solid #FBEED5; text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);}
.alert a { color: #927036; text-decoration: underline;}
.alert h4 { font-size: 16px; font-weight: bold; line-height: 1.5em; margin-bottom: 2px;}
.alert ul { margin-bottom: 10px}
.alert li { margin: 4px 0;}
.alert li em { font-weight: 600; color: #F30; margin: 0 2px;}
.alert i { font-size: 14px; margin-right: 4px; vertical-align: middle;}
/*申请详情提交*/
.joinin-concrete { padding: 19px; border-top: dotted 1px #EEE;}
.joinin-concrete .title { height: 20px; text-align: center; padding: 20px 0;}
.joinin-concrete .title h3 { font: 600 16px/20px "microsoft yahei"; color: #666;}
/*入驻协议*/
.apply-agreement-content { height: 300px; padding: 10px; border: solid 1px #EEE; overflow: auto;}
.apple-agreement { text-align: center; height: 20px; line-height: 20px; margin: 20px 0;}
/*申请表格*/
.joinin-concrete table.all { line-height: 22px; color: #555; width: 100%; margin-top: 20px;}
.joinin-concrete table.all thead th { font: 600 14px/22px "microsoft yahei"; color: #333; height: 22px; padding: 8px 4px; }
.joinin-concrete table.all thead th em { color: #F00; margin-left: 12px;}

.joinin-concrete table.all tbody th { vertical-align: top; text-align: right; width: 150px; height: 22px; padding: 8px 4px;}
.joinin-concrete table.all tbody th i { font-size: 0; background: url(../images/joinin_pic.png) no-repeat -310px -130px; vertical-align: middle; display: inline-block; *display: inline/*IE7*/; width: 7px; height: 7px; margin-right: 6px; overflow: hidden; *zoom: 1;}
.joinin-concrete table.all tbody td { vertical-align: top; text-align: left; height: 20px; padding: 8px 4px;}
.joinin-concrete table.all tbody td span { *display: none/*IE7*/; color: #777; clear: both; margin-top: 10px;}
.joinin-concrete table.all tbody td span.block { display: block;}
.joinin-concrete table.all tbody td p.emphasis { color: #F30;}

.joinin-concrete tabel.all tfoot td { line-height: 20px; height: 20px; padding: 0;}

.joinin-concrete table.type { width: 600px; margin: 0 auto; border: solid 1px #DDD;}
.joinin-concrete table.type thead th { color: #777; text-align: center; background-color: #F7F7F7; width: 25%;}
.joinin-concrete table.type tbody td { text-align: center; border-style: solid; border-width: 1px 1px 0 0; border-color: #DDD #DDD transparent transparent; }
.joinin-concrete table.type tbody td a { color: #F30;}



.joinin-concrete .explain { font: 16px/32px "microsoft yahei"; color: #777; text-align: center; margin: 120px 0 100px 0}
.joinin-concrete .explain i { background: url(../images/joinin_pic.png) no-repeat -350px 0; vertical-align: middle; display: inline-block; width: 32px; height: 28px; margin-right: 8px;}

/*底部及按钮*/
.joinin-concrete .bottom { text-align: center; height: 30px; padding: 20px 0 10px 0; border-top: solid 1px #EEE;}
.joinin-concrete .btn { font: normal 12px/20px "microsoft yahei"; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 4px 12px; border: solid 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; cursor: pointer;}
.joinin-concrete .btn:hover { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF;}





.joinin-info { width: 980px; margin: 10px auto 0 auto; overflow: hidden;}
.joinin-info .tabs-nav { font-size: 0; *word-spacing:-1px/*IE6、7*/; background-color: #F5F5F5; width: 984px; border-bottom: solid 2px #0286D8;}
.joinin-info .tabs-nav li { font: 14px/20px "microsoft yahei"; color: #333; vertical-align: middle; letter-spacing: normal; word-spacing: normal; text-align: center; display: inline-block; *display: inline; width: 242px; padding: 10px 0; border-right: solid 4px #FFF; *zoom: 1;}
.joinin-info .tabs-nav li.tabs-selected { background-color: #0286D8;}
.joinin-info .tabs-nav li.tabs-selected h3 { color: #FFF; font-weight: 600;}
.joinin-info .tabs-panel { margin: 15px 0;}
.joinin-info .tabs-hide { display: none;}





/*文章新闻内容*/
.article-con { background: #FFF; display: block; padding: 19px; margin-bottom: 10px; overflow: hidden; }
.article-con h1 { font: 600 16px/32px "microsoft yahei"; color: #333; text-align: center; }
.article-con h2 { color: #999; font-size: 12px; padding: 5px 0 20px; margin-bottom: 20px; font-weight: normal; text-align: center; border-bottom: dotted 1px #CCC; }
.article-con .title-bar { border-bottom: solid 1px #E6E6E6; padding-bottom: 15px; margin-bottom: 15px;}
.article-con .title-bar h3 { font: normal 18px/20px "microsoft yahei";}
.article-con .default p { display: block; clear: both; padding: 5px;}
.article-con img { max-width: 740px;}







/*商家入驻表单*/
.joinin-pay { background-color: #FFF; }
.store-joinin { background-color: #FFF; width: 100%; line-height: 20px; margin-bottom: 20px; border-style: solid; border-width: 0 0 1px 1px; border-color: transparent transparent #C9DDE0 #C9DDE0; box-shadow: 2px 2px 2px rgba(204,204,204,0.25);}
.store-joinin thead th { font-weight: 600; color: #214752; background-color: #E3EFF0; height: 20px; padding: 6px 4px; border-style: solid; border-width: 1px 1px 0 0; border-color: #C9DDE0 #C9DDE0 transparent transparent;} 
.store-joinin tbody th { color: #555555; background-color: #F0F7FA; text-align: right; width: 108px; height: 20px; padding: 6px 4px; border-style: solid; border-width: 1px 1px 0 0; border-color: #C9DDE0 #C9DDE0 transparent transparent;}
.store-joinin tbody td { color: #333; min-width: 119px; height: 20px; padding: 4px 6px; border-style: solid; border-width: 1px 1px 0 0; border-color: #C9DDE0 #C9DDE0 transparent transparent;}
.store-joinin tbody td img { max-width: 100px; max-height: 100px; padding: 4px; border: solid 1px #EEE;}
.store-joinin tbody td textarea { width: 400px; height: 100px;}
table.type { width: 700px; border: solid 1px #EEE;}
table.type thead th { color: #555; background-color: #F7F7F7; text-align: center; padding: 4px; border-color: #EEE; }
table.type tbody td { color: #777; text-align: center; padding: 4px; border-color: #EEE;}
table.type tbody td input { width: 60px; padding: 0;}

.apply-company-wait{background: url(../images/apply-company-info.jpg) no-repeat left; height:22px; width:286px; margin:100px auto 0; text-align:left; text-indent:42px; line-height:24px; font-size:16px; font-weight:lighter; color:#888}
