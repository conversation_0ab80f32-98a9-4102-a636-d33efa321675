<?php
/**
 * 阿闻消息推送
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */


class Msg{

    protected $pushApi = 'http://open.test.rvet.cn/notice/add'; //测试接口地址

    public function __construct(){
    	
       //初始化操作
        header("Content-type:text/html;charset=utf-8");
        ini_set('date.timezone','Asia/Shanghai');
       
        define('SCRIPT_ROOT_REVT',  BASE_DATA_PATH.'/api/rvet');        
        require_once SCRIPT_ROOT_REVT.'/lib/rvetrsa.php';
        
    }

    /**
     * 数据推送
     * @param array $data [
     *  'object'=>'',   //1用户 2 医生(必填 用户填 1)
     *  'mobile'=>'',  //用户或医生id/手机号 (必填)
     *  'title'=>'',    //标题 (必填)
     *  'content'=>'',  //内容必填
     *
     *  'data_id'=>'',// 数据ID (选填 默认为0)
     *  'url'=>'',  //外链接(选填填)
     *  'type'=>''  //type 通知类型 (选填 电商填 8)
     * ]
     * @return string 返回数据
     */
    public function psuh($data){
    	
        //判断不能为空
        if(isset($data['object']) && isset($data['mobile']) && isset($data['title']) && isset($data['content']) && empty($data['object']) && empty($data['mobile']) && empty($data['title']) && empty($data['content'])){
            return false;
        }
        $body = [
            'notice'=>rvetrsa::privateEncrypt(json_encode($data)),
        ];
        var_dump($body);die;
        //请求返回处理
        $rlt = $this->_post_url($this->pushApi,$body);
       
       /*  if(!empty(json_decode($rlt,true))){
            //记录日志
            $this->_logError($rlt);
            return false;
        } */
        return true;
        /*if( != 1){

        }
        */
    }

    /**
     * 记录日志
     */
    protected function _logError($file){
        if(!empty($file)){
            $myfile = fopen("./log/error.txt", "a+");
            fwrite($myfile, date('Y-m-d H:i:s',time())."     ".$file);
            fwrite($myfile,"\n");
            fwrite($myfile,"\n");
            fclose($myfile);
        }
    }


    /**
     * 通过URL获取页面信息
     * @param string $url 地址
     * @return string 返回页面信息
     */
    public function _get_url($url){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$url);  //设置访问的url地址
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);//不输出内容
        $result =  curl_exec($ch);
        curl_close ($ch);
        return $result;
    }

    /**
     * 模拟POST提交
     * @param string $url 地址
     * @param string $data 提交的数据
     * @return string 返回结果
     */
    public function _post_url($url, $data){
        $curl = curl_init(); // 启动一个CURL会话
        curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE); // 对认证证书来源的检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE); // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)'); // 模拟用户使用的浏览器
        //curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
        //curl_setopt($curl, CURLOPT_AUTOREFERER, 1);    // 自动设置Referer
        curl_setopt($curl, CURLOPT_POST, 1);             // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);   // Post提交的数据包x
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);         // 设置超时限制 防止死循环
        curl_setopt($curl, CURLOPT_HEADER, 0);           // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);   // 获取的信息以文件流的形式返回

        $tmpInfo = curl_exec($curl); // 执行操作
        if(curl_errno($curl))
        {
            echo 'Errno'.curl_error($curl);//捕抓异常
        }
        curl_close($curl); // 关闭CURL会话
        return $tmpInfo; // 返回数据
    }

}
//调用demo
/*$msg = new Msg();
$rlt = $msg->psuh([
    'object' => 1,   //1用户 2 医生(必填 用户填 1)
    'mobile' => '18124688120',  //用户或医生id/手机号 (必填)
    'title' => '测试测试',    //标题 (必填)
    'content' => 'xsxxx',  //内容必填
//    'data_id' => 0,// 数据ID (选填 默认为0)
//    'url' => '',  //外链接(选填填)
    'type' => 8  //type 通知类型 (选填 电商填 8)
]);

echo '<pre>';
print_r($rlt);die;*/
