<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
class Login
{
	/*
	 * 提供给宠医云回调的接口
	 */
	public function loginCallBack($jwt,$true=true)
	{	
		if(!$jwt){
			return array("status"=>false,"msg"=>"参数错误","data"=>array());
		}		
		
		$mobile =$this->_returnMobile($jwt);
		
		if($true){
			$token = substr($jwt, strpos($jwt,".")+1, strrpos($jwt,".")- strlen($jwt));
			$arr = json_decode(base64_decode($token),true);
			if(isset($arr['mobile'])){
				
				$mobile = $arr['mobile'];
			}
			if(isset($arr['unique_name'])){
				
				$mobile = $arr['unique_name'];
			}
		}
		
		$memberModel = Model('member');
		$condition = array();
		$condition['member_mobile'] = $mobile;	
		$condition['member_name'] = $mobile;
		return  $this->_memberIsExist($condition);
	}
	
	public function _returnMobile($jwt){
		$str=$jwt;//1s、2d、3y、4l、5q、6b、7z、8m、9r、0c  "9s1m2y3s4r5c6y7l2y3q4z1"
		$lenght=strlen($str);	
		
		$arr=array("s"=>1,"d"=>2,"y"=>3,"l"=>4,"q"=>5,"b"=>6,"z"=>7,"m"=>8,"r"=>9,"c"=>0);//字母规则
		//取基数位
		$mobile="";
		for ($i=0; $i<=$lenght; $i++) {
			if($i%2==1){
				$mobile.=@$arr[substr($str,$i,1)];
			}
		}
		return strrev($mobile);
		 
	}
	/*
	 * 依据指定的条件条件会员是否存在
	 * @param array $param 条件数组
	 * @return boolean true:用户存在 |false:用户不存在
	*/
	private function _memberIsExist($param=array())
	{		
		if(!preg_match("/^1[3456789]{1}\d{9}$/",$param['member_mobile'])){
			return array("status"=>false,"msg"=>"手机号格式错误","data"=>array());
		}		
		$memberModel = Model('member');
		$ret = $memberModel->getMemberInfo(array("member_mobile"=>$param['member_mobile']));		
		
		if(!$ret)
		{
			
			$insert_array = array();
			$member_time = time();
			
			$phone =$param['member_mobile'];
			$num = substr($phone,-4);
			$logic_connect_api = Logic('connect_api');
			$member_name = $logic_connect_api->getMemberName('upet', $num);
			
			$insert_array['member_name'] =$member_name;
			$insert_array['member_passwd'] = "fe657496b1c46be00e86c05d2b190c9a";
			/* $insert_array['member_email']  = '<EMAIL>';
			$insert_array['member_truename'] = $param['member_truename']; */
			$insert_array['member_sex'] = null;
			$insert_array['member_qq']  = null;
			$insert_array['member_ww']  = null;
			$insert_array['member_mobile_bind'] = 1;
			$insert_array['member_mobile'] = $param['member_mobile'];
			$insert_array['member_time']   = $member_time; 	
			$insert_array['geval_comment_status']   = 2; //来源IOS
			$member_id = $memberModel->addMember($insert_array);
			if($member_id)
			{
				$token = $this->_get_token($member_id,$param['member_name'],'ios');
				if($token) 
				{
					//output_data(array('username' => $param['member_mobile'], 'userid' =>$member_id, 'key' => $token));
					return array("status"=>true,"msg"=>"登陆成功","data"=>array('username' => $param['member_name'], 'userid' =>$member_id, 'key' => $token));
				} 
				else 
				{
					//output_error('登录失败');
					return array("status"=>false,"msg"=>"登陆失败","data"=>array());
				}
			}	
		}else{
			$member_id=$ret['member_id'];
			$member_name=$ret['member_name'];
		}
		$token = $this->_get_token($member_id, $member_name,'ios');
		
		if($token)
		{		
		
			//output_data(array('username' => $ret['member_mobile'], 'userid' =>$ret['member_id'], 'key' => $token));
			return array("status"=>true,"msg"=>"登陆成功","data"=>array('username' => $ret['member_name'], 'userid' =>$ret['member_id'], 'key' => $token));
		}
		else
		{
			//output_error('登录失败');
			return array("status"=>false,"msg"=>"登陆失败","data"=>array());
		}
		
	}
	
	/**
	 * 生成token
	 */
	private function _get_token($member_id, $member_name, $client) {
		$model_mb_user_token = Model('mb_user_token');
		
		//查找该用户IOS登陆最近的KEY		
		$token_after=$model_mb_user_token->where(array("member_id"=>$member_id,"client_type"=>$client))->order("login_time desc")->find();		
		if($token_after){
			return $token_after['token'];
		}
		
		$mb_user_token_info = array();
		$token = md5($member_name . strval(TIMESTAMP) . strval(rand(0,999999)));
		$mb_user_token_info['member_id'] = $member_id;
		$mb_user_token_info['member_name'] = $member_name;
		$mb_user_token_info['token'] = $token;
		$mb_user_token_info['login_time'] = TIMESTAMP;
		$mb_user_token_info['client_type'] = $client;
		$result = $model_mb_user_token->addMbUserToken($mb_user_token_info);
		if($result) {
			return $token;
		} else {
			return null;
		}
	}
}

