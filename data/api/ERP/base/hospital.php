<?php
class Hospital
{
	private  $dataBaseUrl; //基础接口URL地址（基础接口）
	private  $port;   //端口号
    private  $path;   //接口名
    private  $getAllAreaHospitalUrl; //获取所有分院的城市列表URL	
    
    public  function __construct(){	
    	        
        $this->path='base/Hospital';
    	$this->port='80';
    	$this->dataBaseUrl=C('erp_url_host').'/'.$this->path; //.':'.$this->port
    	$this->getAllAreaHospitalUrl=C('erp_url_host').'/api/area/all';
    	$this->getHisgetdoctorUrl = C('erp_url_host').'/api/reserve';
    	$this->getHisgetdoctorInfo = C('erp_url_host').'/base/Doctor';
    	
    }
    
    /**
     * 查询所有区域
     * @param array $param
     */
    public function areaall($param=array())
    {
    	$url =$this->getAllAreaHospitalUrl;
    	$result = doCurlGetRequest($url,$param);
    	return $result;
    }
    
	/**
	 * 查询某一区域的所有医院列表
	 * @param array $param
	 */
	public function area($param=array())
	{
		$url = $this->dataBaseUrl.'/area';
		$result = doCurlGetRequest($url,$param);
		return $result;
	}
   
	/**
	 *查询某一区域的指定品牌(一个或多个)的医院列表
	 * @param array $param
	 */
	public function areaorbrand($param=array())
	{
		$url = $this->dataBaseUrl.'/areaorbrand';
		$result = doCurlGetRequest($url,$param);
		return $result;
	}
	/**
	 * 查询指定医院(一个或多个)基本信息接口
	 * @param array $param
	 */
	public function base($param=array())
	{
		$url = $this->dataBaseUrl.'/base';
		$result = doCurlGetRequest($url,$param);
		return $result;
	}
	/**
	 * 查询某一医院的详细信息(基本信息+扩展信息)
	 * @param array $param
	 */
	public function extend($param=array())
	{
		$url = $this->dataBaseUrl.'/extend';
		$result = doCurlGetRequest($url,$param);
		return $result;
	}

    /**
     * 查询门店下的医生信息
     *
     */
	public function hisgetdoctors($param = array()){
        $url = $this->getHisgetdoctorUrl.'/hisgetdoctors';
        $result = doCurlGetRequest($url,$param);
        return $result;
    }

    /**
     * 查询的医生详细信息
     *
     */
    public function hisgetdoctorposition($doctorId){
        $url = $this->getHisgetdoctorInfo.'/'.$doctorId;
        $result = doCurlGetRequest($url,[]);
        return $result;
    }
   
}

