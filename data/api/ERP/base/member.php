<?php
class Member
{
	private  $dataBaseUrl; //基础接口URL地址（基础接口）
	private  $port;   //端口号
            private  $path;   //接口名  
    
    public  function __construct(){	
    	$this->path='base/Member';
    	$this->port='80';  
    	$this->dataBaseUrl=C('erp_url_host').'/'.$this->path; //.':'.$this->port
    }
    
    
    /**
     * 新增用户返回memberid
     * @param array $data
     */
    public function baseMemberadd($param=array())
    {
    	$url = $this->dataBaseUrl.'/add';    	    	
    	$result = doCurlPostRequest($url, $param);    	
    	return $result;
    }
    
    /**
     * 编辑用户
     * @param array $data
     */
    public function baseMemberedit($param=array())
    {    	
    	$url = $this->dataBaseUrl.'/edit';
    	$result = doCurlPostRequest($url, $param);
    	return $result;
    }
    
    /**
     * 查询用户基本信息
     * @param array $data
     */
    public function basicmemberinfos($param=array())
    {
    	$url = $this->dataBaseUrl.'/basicmemberinfos';
    	$result = doCurlGetRequest($url, $param);
    	return $result;
    }

    /**
     * 查询用户基本信息
     * @param array $data
     */
    public function basicmemberinfo($param=array())
    {
        $url = $this->dataBaseUrl.'/basicinfo';
        $result = doCurlGetRequest($url, $param);
        return $result;
    }

    /**
     * 查询用户详细信息
     * @param array $data
     */
    public function detailmemberinfos($param=array())
    {
    	$url = $this->dataBaseUrl.'/detailmemberinfos';
    	$result = doCurlGetRequest($url, $param);
    	return $result;
    }
    
    /**
     * 查询某一用户的账户信息
     * @param array $data
     */
    public function accountmemberinfos($param=array())
    {
    	$url = $this->dataBaseUrl.'/accountmemberinfos';
    	$result = doCurlGetRequest($url, $param);
    	return $result;
    }
    
    /**
     * 用户登陆,无需密码 
     * @param array $data
     */
    public function login($param=array())
    {
    	$url = $this->dataBaseUrl.'/login';
    	$result = doCurlPostRequest($url, $param);
    	return $result;
    }
    
   
}

