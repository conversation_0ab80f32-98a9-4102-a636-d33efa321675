<?php
Class Rsa
{
    private static $PREFIX = 'rp';
    //电商私钥
	private static $PRIVATE_KEY_PATH ='**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
	//ERP公钥
	private static $PUBLIC_KEY_PATH  = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5g/o5aP3JYJ9e0LKzoJyNx/mt
jWTvb1Umh26IywSA9H9+PynFyb98IlETtcXJYKXCbRmwHn30tEuPo8U6ystfi9HL
dQAkZqrON5b772sgaBZW24oNtpy7NXt83/eSDM01esSnYfxpjeTZ5wnGyI6/MVQk
HG5Eruprm8hw/aZE7wIDAQAB
-----END PUBLIC KEY-----';	
	
	private static $encrypted   = '';
	private static $decrypted   = '';
	private static $instance   = null;
	/**
	 * 获取Resource类型私钥
	 * @return bool|resource
	*/
	private static function getPrivateKey()
	{
		//$privKey = openssl_pkey_get_private(file_get_contents(self::$PRIVATE_KEY_PATH)) ? openssl_pkey_get_private(file_get_contents(self::$PRIVATE_KEY_PATH)) : '';
		$privKey = openssl_pkey_get_private(self::$PRIVATE_KEY_PATH) ? openssl_pkey_get_private(self::$PRIVATE_KEY_PATH) : '';
		return $privKey;
	}
	
	/**
	 * 获取Resource类型公钥
	 * @return bool|resource
	 */
	 private static function getPublicKey()
	 {
		//$publicKey = openssl_pkey_get_public(file_get_contents(self::$PUBLIC_KEY_PATH)) ? openssl_pkey_get_public(file_get_contents(self::$PUBLIC_KEY_PATH)) : '';
	 	$publicKey = openssl_pkey_get_public(self::$PUBLIC_KEY_PATH) ? openssl_pkey_get_public(self::$PUBLIC_KEY_PATH) : '';
		return $publicKey; 
	 }
	 
	 /**
	 * 私钥签名
	 * @param string|array $data
	 * @return null|string
	 */
	 public static function privateSignature($data='')
	 {
            unset($data['sign']);
            ksort($data);
            array_values($data);           
            $requestStr = self::$PREFIX."_".implode("_",$data);
            
            $genSignature = self::getSign($requestStr,self::getPrivateKey());
            return $genSignature;
	 }
         
         /**
          * 公钥加密
          * @param type $signature
          * @return type
          */
         public function publicEncrypt($signature='')
         {
             $crypto = '';
             foreach(str_split($signature,117) as $chunk){
                openssl_public_encrypt($chunk,self::$encrypted,self::getPublicKey(),OPENSSL_PKCS1_PADDING);
                $crypto .= self::$encrypted;
             }
            return base64_encode($crypto);
         }
	
	/**
	 * 私钥解密
	 * @param string $encrypted
	 * @return null|string
	 */
	 public static function privDecrypt($encrypted='')
	 {
            $crypto = '';
            foreach(str_split(base64_decode($encrypted),128) as $chunk)
            {
                openssl_private_decrypt($chunk, self::$decrypted,self::getPrivateKey(),OPENSSL_PKCS1_PADDING);
                $crypto.= self::$decrypted;
            }
            return $crypto;
	 }
         
         /**
          * 公钥验证签名
          * @param string $crypto
          * 
          */
         public static function publicVerifySignature($crypto)
         {
            $signature = substr($crypto,strrpos($crypto,"*")+1);
            return self::checkSign(self::getPublicKey(), $signature,substr($crypto,0,strrpos($crypto,"*")));
         }
	 
	 /**
	  * 签名
	  * @param string $signString 待签名的字符串
	  * @param  type  $priKey Resource类型的私钥
	  * @return string 签名字符串
	 */
	 private static function getSign($signString,$priKey)
	 {
		 $signature = ''; 
		 openssl_sign($signString,$signature,$priKey,OPENSSL_ALGO_MD5);
		 openssl_free_key($priKey);
		 return base64_encode($signature);
	 }
         
         /**
          * 验证签名
          * @param string $pubKey Resource类型公钥
          * @param string $sign 签名字符串
          * 
          */
         private static function checkSign($pubKey,$sign,$str)
         {
             $result = openssl_verify($str, base64_decode($sign),$pubKey,OPENSSL_ALGO_MD5);
             openssl_free_key($pubKey);
             if($result==1){
                 return true;
             }
             else{
                return false;
             }
         }
         
         /**
          * 实例化对象
          * @return objectt 实例化后的对象
          */
         public static function create()
         {
             if(!self::$instance instanceof Rsa)
             {
                 self::$instance = new Rsa();
             }
             return self::$instance;
         }
         
         /**
          * 封装登录用户的Token信息
          * @param string $str
          * @return string Token 访问权限资源/路由的Token令牌
          * 
          */
         public function getToken($str='')
         {
             if(empty($str))
             {
                 return false;
             }
            $decrypt = self::privDecrypt($str);
            $result  = self::publicVerifySignature($decrypt);
            if($result)
            {
                $session_data = array();
                $session_data['data'] = substr($decrypt,3,strrpos($decrypt,"*")-3);
                $session_data['expire'] = time() + 7*24*3600;
                $_SESSION['jsession'] = $session_data;
                if(isset($_SESSION['jsession']['data'])&&empty($_SESSION['jsession']['data']))
                {
                    $redis_cache = Cache::getInstance('redis');
                    $msg = substr($decrypt, strpos($decrypt,".")+1, strrpos($decrypt,".")- strlen($decrypt));
                    $json_arr = json_decode(base64_decode($msg),true);
                    $redis_cache->set($json_arr['nameid'],substr($decrypt,3,strrpos($decrypt,"*")-3));
                    return substr($decrypt,3,strrpos($decrypt,"*")-3);
                }
            }
         }    
}
?>
