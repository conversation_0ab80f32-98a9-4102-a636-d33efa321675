<?php
class Stock
{
		
	private  $dataOrderUrl; //库存URL地址（订单处理接口）
	private  $port;   //端口号
	private  $path;   //接口名	
	
	public  function __construct(){
		$this->path='order/Goods';
		$this->port='80';
		$this->dataOrderUrl=C('erp_url_host').'/'.$this->path;	//.':'.$this->port
	}
	
	/**
	 * 查询某一商品档案信息
	 * @param string $goodsId 商品Id
	 */
	public function Goodsinfo($param=array())
	{
		$url = $this->dataOrderUrl.'/info';		
		$result = doCurlGetRequest($url,$param);
		return $result;
	}
	/**
	 * 查询某一商品在各仓的库存情况
	 * @param array $param
	 */
	public function Goodsgetstocks($param=array())
	{
		$url = $this->dataOrderUrl.'/getstocks';
		$result = doCurlGetRequest($url,$param);
		return $result;
	}
	
	/**
	 * 查询某一商品在指定仓库(一个或多个)的商品信息及库存
	 * @param array $param
	 */
	public function GoodsgetStocksInfo($param=array())
	{
		$url = $this->dataOrderUrl.'/getStocksInfo';
		$result = doCurlGetRequest($url,$param);
		return $result;
	}
	
	/**
	 * 查询指定仓库(一个或多个)所有商品及库存（查询速度慢，暂时不用）
	 * @param array $param
	 */
	public function GoodsgetStocksId($param=array())
	{
		$url = $this->dataOrderUrl.'/getStocksId';
		$result = doCurlGetRequest($url,$param);
		return $result;
	}
	
	/**
	 * 销售出库 ERP专用 暂不管
	 * @param array $param
	 */
	public function GoodssellOut($param=array())
	{
		$url = $this->dataOrderUrl.'/sellOut';
		$result = doCurlPostRequest($url,$param);
		return $result;
	}
	
	
    
}

