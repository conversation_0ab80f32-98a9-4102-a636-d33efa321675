<?php
class Order
{
		
	private  $dataOrderUrl; //库存URL地址（订单处理接口）
	private  $port;   //端口号
	private  $path;   //接口名	
	
	public  function __construct(){
		$this->path='order/Order';
		$this->port='80';
		$this->dataOrderUrl=C('erp_url_host').'/'.$this->path;		 //.':'.$this->port
	}
	
	/**
	 * 添加订单
	 * @param
	 */
	public function Orderadd($param=array())
	{		
		$url = $this->dataOrderUrl.'/add';			
		$result = doCurlPostRequest($url, $param,true);
		return $result;
	}

    /**
     * 批量添加订单
     * @param
     */
    public function OrderBatchAdd($param=array())
    {
        //$url = $this->dataOrderUrl.'/batch-add';
        $url = $this->dataOrderUrl.'/sync';//添加订单和支付订单一起
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }
	
	/**
	 * 支付订单
	 * @param
	 */
	public function Orderpay($param=array())
	{
		$url = $this->dataOrderUrl.'/pay';
		$result = doCurlPostRequest($url, $param,true);
		return $result;
	}

    /**
     * 支付订单 批量支付
     * @param
     */
    public function OrderpayBatch($param=array())
    {
        $url = $this->dataOrderUrl.'/batch-pay';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }
		
	/**
	 * 获取指定的订单ID
	 * @param
	 */
	public function Orderget($param=array())
	{
		$url = $this->dataOrderUrl.'/get';
		$result = doCurlGetRequest($url, $param);
		return $result;
	}
	
	/**
	 *根据手机号获取订单列表
	 * @param
	 */
	public function OrdergetByMobile($param=array())
	{
		$url = $this->dataOrderUrl.'/getByMobile';
		$result = doCurlGetRequest($url, $param);
		return $result;
	}

    /**
     * 虚拟商品多个核销码 批量退款
     */
    public function batchApplyErpRefund($param = array())
    {
        $url = $this->dataOrderUrl.'/batch-apply-refund';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }

    /**
     * 虚拟订单售后退款
     */
    public function applyErpRefund($param = array())
    {
        $url = $this->dataOrderUrl.'/batch-apply-refund';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }
    
    /**
     * 批量自动退货退款
     */
    public function batchAutoRefund($param = array())
    {
    	$url = $this->dataOrderUrl.'/batch-auto-refund';
    	$result = doCurlPostRequest($url, $param,true);
    	return $result;
    }

	/**
	 * 订单退款
	 * @param
	 */
	public function Orderrefund($param=array())
	{
		$url = $this->dataOrderUrl.'/refund';
		$result = doCurlPostRequest($url, $param);
		return $result;
	}
	
	/**
	 * 取消订单
	 * @param
	 */
	public function Ordercancell($param=array())
	{
		$url = $this->dataOrderUrl.'/cancell';
		$result = doCurlPostRequest($url, $param);
		return $result;
	}
	
	/**
	 * 确认退款--电商回调
	 * @param
	 */
	public function Orderconfirmrefund($param=array())
	{
		$url = $this->dataOrderUrl.'/confirmrefund';
		$result = doCurlPostRequest($url, $param);		
		return $result;
	}

    /**
     * 批量确认退款--电商回调
     * @param
     */
    public function batchOrderconfirmrefund($param=array())
    {
        $url = $this->dataOrderUrl.'/batch-confirmrefund';
        $result = doCurlPostRequest($url, $param,true);       
        return $result;
    }
	
	/**
	 * 驳回退款--电商回调
	 * @param
	 */
	public function Orderrejectrefund($param=array())
	{
		$url = $this->dataOrderUrl.'/rejectrefund';
		$result = doCurlPostRequest($url, $param);
		return $result;
	}
    /**
     * 批量 驳回退款 -- 电商回调
     */
    public function batchOrderrejectrefund($param=array())
    {
        $url = $this->dataOrderUrl.'/batch-rejectrefund';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }
	/**
	 * 申请退货退款
	 * @param
	 */
	public function Orderrefunding($param=array())
	{
		$url = $this->dataOrderUrl.'/refunding';
		$result = doCurlPostRequest($url, $param);
		return $result;
	}
	
	/**
	 * 更新物流信息
	 * @param
	 */
	public function updateExpressInfo($param=array()) 
	{
		$url = $this->dataOrderUrl.'/upexpress';
		$result = doCurlPostRequest($url, $param);
		return $result;
	}

	/**
     * erp售后订单申请
     */
	public function orderApplayAfter($param = array())
    {
        $url = C('datacenter_orderpay_url').'/order-api/refund/apply';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }
    
    /**
     * erp退货释放库存
     */
    public function orderReturnRelease($param = array())
    {
    	$url = C('erp_after_order_url').'/mall/order/return-release';
    	$result = doCurlPostRequest($url, $param,true);
        return $result;
    }

    /**
     * erp 取消未支付订单 释放订单
     */
    public function orderCancelReturnRelease($param = array())
    {
        $url = C('erp_after_order_url').'/mall/order/cancel-unpaid';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }

    /**
     * erp 取消未支付订单 释放订单
     */
    public function orderRefundReject($param)
    {
        $url = C('erp_after_refund_url').'/boss/ordercenter/order/OrderRefundReject';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }

    /**
     * erp 取消未支付订单 释放订单
     */
    public function orderRefundAgree($param)
    {
        $url = C('erp_after_refund_url').'/boss/ordercenter/order/OrderRefundAgree';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }

    /**
     * erp售后物流订单申请
     */
    public function orderExpressinfo($param = array())
    {
        $url = C('datacenter_orderpay_url').'/order-api/order/expressinfoupdate';
        $result = doCurlPostRequest($url, $param,true);
        return $result;
    }
}

