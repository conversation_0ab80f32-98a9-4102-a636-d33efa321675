<?php
class Points
{
		
	private  $dataOrderUrl; //库存URL地址（订单处理接口）
	private  $port;   //端口号
	private  $path;   //接口名
    private  $couponUrl;//优惠券调用地址
	
	public  function __construct(){
        $this->path = 'order/Integral';
        $this->port = '80';
        /* $this->dataOrderUrl=C('erp_url_host').':'.$this->port.'/'.$this->path;		 */
        $this->dataOrderUrl = C('erp_url_host') . '/' . $this->path;
        $this->couponUrl = C('coupon_request_url');//http://*********:9999/base/coupon/new   http://**************:9999  http://**************:9998
    }
	
	/**
	 * 我的积分
	 * @param
	 */
	public function my($param=array())
	{
        $url = $this->dataOrderUrl.'/my';
        $result = $this->doCurlGet($url, $param);
        return $result;
	}

    /**
     * 我的积分
     * @param
     */
    public function myPoint($param=array())
    {
        $url = $this->dataOrderUrl.'/my';
        $result = doCurlGetRequest($url, $param);
        return $result;
    }

    public function couponInfo($param = array())
    {
        $url = $this->couponUrl.'/base/coupon/new';
        //$result = doCurlPostRequest($url, $param,true);
        $result = $this->getPostData($url,$param);
        return $result;
    }
    public function getPostData($url,$param) {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($param));
        $data = curl_exec($curl);
        curl_close($curl);
        return $data;
    }
	
	/**
	 * 消费积分
	 * @param
	 */
	public function consume($param=array())
	{
		$url = $this->dataOrderUrl.'/consume';
	
		$result = doCurlPostRequest($url, $param);
		return $result;
	}

    /**
     * 冻结积分
     */
    public function freeze($param=array()) {
        $url = $this->dataOrderUrl.'/freeze';

        $result = $this->doCurlPost($url, $param);
        //$result = doCurlPostRequest($url, $param);
        return $result;
    }

    /**
     * 取消冻结积分
     */
    public function cancel($param=array()) {
        $url = $this->dataOrderUrl.'/cancel';
        $result = $this->doCurlPost($url, $param);
        return $result;

    }

    /**
     * 积分明细列表
     */
    public function infoList($param) {
        $url = $this->dataOrderUrl.'/list';
        //$result = $this->doCurlGet($url, $param);
        $result = doCurlGetRequest($url, $param);
        return $result;
    }

    /**
     * 通过手机号查询订单
     */
    public function getMobieOrder($param) {
        $url = 'order/Order/getByMobile';
        $result = $this->doCurlGet($url, $param);
        return $result;
    }


    /**
     * 添加订单
     * @param
     */
    public function Orderadd($param=array())
    {
        $url = 'order/Order/add';
        $result = $this->doCurlPost($url, $param);
        return $result;
    }

    /**
     * get请求
     */
    private function doCurlGet($url,$param) {
        $token = $param['token'];
        unset($param['token']);
        if(!empty($param))
        {
            $url=$url."?".http_build_query($param);
        }
        $handle = curl_init();
        if(stripos($url, "https://")!==FALSE)
        {
            curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($handle, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($handle, CURLOPT_SSLVERSION,1);
        }

        /* $headers = array();
        $headers['timestamp'] = time();
        $headers['sn'] = str_pad(substr(time(),6,4),9,0,STR_PAD_LEFT).rand(1, 1000).rand(1, 1000);
        $headers['ua'] = '10';
        $headers['source'] = '1';

         define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
        require_once SCRIPT_ROOT.'/rsa.php';
        $rsa = Rsa::create();

        $data=$rsa->privateSignature(array_merge($param,$headers));

        if(!empty($data))
        {
            $headers['sign'] = $data;
        }
         */
        //排序成header格式
        $header=array();
        /* foreach ($headers as $k=>$v){
            $header[]=$k.':'.$v;
        } */

        $header[]  =  "Accept:application/json";
        $header[]  =  "Authorization: Bearer ". $token;
        curl_setopt($handle, CURLOPT_HTTPHEADER, $header);
        curl_setopt($handle,CURLOPT_URL,$url);
        curl_setopt($handle,CURLOPT_RETURNTRANSFER,1);
        curl_setopt($handle,CURLOPT_TIMEOUT,60);
        $sContent = curl_exec($handle);
        $aStatus  = curl_getinfo($handle);
        curl_close($handle);
        return (json_encode(array('http_code'=>$aStatus['http_code'],'msg'=>$sContent)));
    }

    /**
     * POST请求
     * @param string $url
     * @param array $param
     * @return mixed|boolean
     */
    function doCurlPost($url,$param)
    {

        $handle = curl_init();
        if(stripos($url, "https://")!==FALSE)
        {
            curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($handle, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($handle, CURLOPT_SSLVERSION, 1);
        }
        $headers['timestamp'] = time();
        $headers['sn'] = str_pad(substr(time(),6,4),9,0,STR_PAD_LEFT).rand(1, 1000).rand(1, 1000);
        $headers['ua'] = '10';
        $headers['source'] = '1';
        $header=array();
        foreach ($headers as $k=>$v){
            $header[]=$k.':'.$v;
        }
        $headers[]  =  "Accept:application/json";
        $header[]  =  "Authorization: Bearer ". $param['token'];
        curl_setopt($handle, CURLOPT_URL, $url);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER,1);
        curl_setopt($handle, CURLOPT_HTTPHEADER, $header);
        curl_setopt($handle, CURLOPT_POST,TRUE);
        unset($param['token']);
        curl_setopt($handle, CURLOPT_POSTFIELDS,$param);
        //curl_setopt($handle, CURLOPT_FOLLOWLOCATION, true);
        $sContent = curl_exec($handle);
        $aStatus  = curl_getinfo($handle);
        curl_close($handle);
        return array('http_code'=>$aStatus['http_code'],'msg'=>$sContent) ;
    }
	
	
    
   
}

