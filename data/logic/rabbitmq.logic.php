<?php
/**
* rabbitmq接口对接类
* <AUTHOR>
* @date 2019.06.21
*/
defined('InShopNC') or exit('Access Invalid!');
//require_once BASE_DATA_PATH.'/api/rabbitmq/vendor/autoload.php';
//use PhpAmqpLib\Connection\AMQPStreamConnection;
//use PhpAmqpLib\Message\AMQPMessage;
class rabbitmqLogic {
    public function releaseMessage($data,$exchangeName = 'datacenter',$queueName = 'ds-sz-verification-code',$type=1) {
        //dc-sz-ds-verification-code
        $message = json_encode($data);
        if ($type == 2) {//拆单相关
            $username = C('cloud_mq_login');
            $password = C('cloud_mq_password');
            $url_host = C('cloud_mq_host');
            $port = C('cloud_mq_port');
            $vhost = C('cloud_mq_vhost');
        }else{
            $username = C('dc_mq_login');
            $password = C('dc_mq_password');
            $url_host = C('dc_mq_host');
            $port = C('dc_mq_port');
            $vhost = C('dc_mq_vhost');
        }
        $config = array(
            'host' => $url_host,
            'vhost' => $vhost,
            'port' => $port,
            'login' => $username,
            'password' => $password
        );
        //error_log(print_r($config.PHP_EOL,true),3,'/wwwroot/rp-dsapi/data/mqlog/mq_config'.'.log');
        $connection = new AMQPConnection($config);
        if (!$connection->connect()) {
            echo "Cannot connect to the broker";
            exit();
        }
        $channel = new AMQPChannel($connection);
        $ex = new AMQPExchange($channel);
        //消息的路由键，一定要和消费者端一致
        if ($type == 2) {
            $routingKey = $queueName;
        }else {
            $routingKey = '';
        }
        //交换机名称，一定要和消费者端一致，
        $ex->setName($exchangeName);
        $ex->setType(AMQP_EX_TYPE_DIRECT);
        $ex->setFlags(AMQP_DURABLE);
        $ex->declareExchange();

        $queue = new AMQPQueue($channel);
        $queue->setName($queueName);
        $queue->setFlags(AMQP_DURABLE);

        $result = $ex->publish($message, $routingKey, AMQP_NOPARAM, array('delivery_mode' => 2));
        //error_log(print_r($result.PHP_EOL,true),3,'/wwwroot/rp-dsapi/data/mqlog/mq_config_res'.'.log');
        $connection->disconnect();
        if ($result) {
            return true;
        }else{
            return false;
        }
    }

    public function receiveMsg($queueName = "dc-sz-qqd-dispatch",$exchange = "ordercenter") {
        die;
        $queueName = 'ds-sz-verification-code';
        $routeKey = '';
        $username = "admin";
        $password = "BF$08hm@6@Bn@FGnbGAf";
        $url_host = C('dc_mq_host');
        $vhost = "scrm-prod";
        $port = 4672;
        $config = array(
            'host' => $url_host,
            'vhost' => $vhost,
            'port' => $port,
            'login' => $username,
            'password' => $password
        );
        //连接broker
        $cnn = new AMQPConnection($config);
        if (!$cnn->connect()) {
            echo "Cannot connect to the broker";
            exit();
        }
        //在连接内创建一个通道
        $ch = new AMQPChannel($cnn);
        //创建一个交换机
        $ex = new AMQPExchange($ch);
        //声明路由键
        $routingKey = '';
        //声明交换机名称
        $exchangeName = 'datacenter';
        //设置交换机名称
        $ex->setName($exchangeName);
        //设置交换机类型
        //AMQP_EX_TYPE_DIRECT:直连交换机
        //AMQP_EX_TYPE_FANOUT:扇形交换机
        //AMQP_EX_TYPE_HEADERS:头交换机
        //AMQP_EX_TYPE_TOPIC:主题交换机
        $ex->setType(AMQP_EX_TYPE_DIRECT);
        //设置交换机持久
        $ex->setFlags(AMQP_DURABLE);
        //声明交换机
        $ex->declareExchange();
        //创建一个消息队列
        $q = new AMQPQueue($ch);
        //设置队列名称
        $q->setName($queueName);
        //设置队列持久
        $q->setFlags(AMQP_DURABLE);
        //声明消息队列
        $q->declareQueue();
        //交换机和队列通过$routingKey进行绑定
        $q->bind($ex->getName(), $routingKey);
        //接收消息并进行处理的回调方法
        $q->consume(function ($envelope,$queue){
            $msg = $envelope->getBody();
            var_dump(" [x] Received:" . $msg);
            $queue->nack($envelope->getDeliveryTag());
        });
        /*while (TRUE) {
            $q->consume(function ($envelope,$queue){
                $msg = $envelope->getBody();
                var_dump(" [x] Received:" . $msg);
                $queue->nack($envelope->getDeliveryTag());
            });
        }*/

        /*function receive($envelope, $queue) {
            //休眠两秒，
            sleep(2);
            //echo消息内容
            echo $envelope->getBody()."\n";
            //显式确认，队列收到消费者显式确认后，会删除该消息
            $queue->ack($envelope->getDeliveryTag());
        }
//设置消息队列消费者回调方法，并进行阻塞
        $q->consume("receive");*/

        $cnn->disconnect();
        die;

    }




}
