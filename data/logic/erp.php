<?php
/**
 * 默认展示页面
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class erpControl extends BaseHomeControl{
	
	public function __construct(){
		die("暂不开放");
		header("Content-Type: text/html;charset=utf-8");
		
		define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
		require_once SCRIPT_ROOT.'/rsa.php';
		
		require_once BASE_DATA_PATH.'/api/rvet/'.'login.php';
		
	}
    public function indexOp(){     
    	
    	//$rsa = new Rsa();
    	/* $privateEncrypt = $rsa->privEncrypt('aassssasssddd');
    	echo '私钥加密后:'.$privateEncrypt.'<br>'; */
    	//$data['id'] = "1";
    	//$data['name'] = 'asdf';
    	//$data['email']= '<EMAIL>';
    	//$privateEncrypt = $rsa->publicEncrypt($data);
        //$data = 'nSZBE9hjE+Vjv+cVjdAVkvwb6SQdwIxNnt3Z0WRYrYsMvN4VpqEbzQHNCp20jBEZpwCwVkZzrq8VAmQSgBbBa64lImLwsX7RiuM+lL4QYqj22VOuzujmKdRllCSVExWTsay4Lo0GaSQQE/6BgydVE/8qqPxgcIA0lFuVzfTf+6VbWP+W5rxun1wO43T901Kl+KQnGjsoZWLzWB/Ug8nm8CBaQ2+4HbkFFD3J/C+IVKAWtcvE78i773iaV/zMHfZR1PEFdTJyjIXTEKsFgiPj3Yhk/QbtldqLCZnRs8Tofb9p8V5gQWzmdS/yS0aDm87WVEz9/xWz5JTK9I9pj2tadg==';
    	//$data = 'iEV+4/Eikz3zJei/+LA273+YDJhC4Y2wTqESF4km4pAT05e3rEGWTbz5Q3Y/LDcEUnvzRz9SXZXSnlXVuU4g7NEZiFmS+LOoAWnSb+gKzENsxqhD5I3WJHpXBlghWXqrkHlQNAZruLkn9NUIolihgp7I8lpacDCFoQBPN8NAU6eaAvzhXE23gK7IMLxUvEPJL7BMCck91u49u6GUGUWFuI4f1PaS1YveAatYNmyPDwdDk/i7ZClbX5KjyJ2OJWKXZBzayZonoXjMLixnnCcDaCqJyiJ/epiMa+roUv6j4SiJecDLj7P635614lItgdM4YdUD48zmgErQHNvqn83erA==';
        //$privateEncrypt = $rsa->privDecrypt($data);
        //echo $privateEncrypt;
        
       /*  require_once SCRIPT_ROOT.'/base/'.'hospital.php';
        require_once SCRIPT_ROOT.'/order/'.'stock.php';
        $hospital = new Hospital();
        $hospital->getAllAreaHospital(); */
        //$stock = new Stock();
        //$stock->GoodsFillBack(120100001,10,123);
       // $stock->getAllGoodsByStockId(101);
        //echo pack("H*","5368616e67686169");
    	echo '*******会员模块*******'."</br>";
        echo '<a href="?act=erp&op=memberadd" target="_blank">->会员添加-(传参 Mobile)</a>'."</br>";
        echo '<a href="?act=erp&op=memberedit" target="_blank">->会员编辑-(传参 MemberId)</a>'."</br>";
        echo '<a href="?act=erp&op=memberinfos" target="_blank">->会员基本信息查询-(传参 memberIdStr)</a>'."</br>";
        echo '<a href="?act=erp&op=detailmemberinfos" target="_blank">->会员详细信息查询-(传参 memberIdStr)</a>'."</br>";
        echo '<a href="?act=erp&op=accountmemberinfos" target="_blank">->查询某一用户的账户信息-(传参 memberIdStr)</a>'."</br>";
        echo '<a href="?act=erp&op=memberlogin" target="_blank">->会员登陆-(传参 mobile)</a>'."</br>";
        echo "</br>";
        echo '*******积分模块*******'."</br>";      
        echo '<a href="?act=erp&op=my" target="_blank">->我的积分</a>'."</br>";
        echo '<a href="?act=erp&op=consume" target="_blank">->消耗积分</a>'."</br>";
        echo '<a href="#">->生成积分 ERP本期不做</a>'."</br>";
        echo "</br>";
        echo '*******订单模块*******'."</br>";
        echo '<a href="?act=erp&op=orderadd" target="_blank">->订单添加-(传参 memberid)</a>'."</br>";
        echo '<a href="?act=erp&op=orderpay" target="_blank">->订单支付-(传参 orderId)</a>'."</br>";
        echo '<a href="?act=erp&op=orderget" target="_blank">->订单查询-(传参 orderId)</a>'."</br>";
        echo '<a href="?act=erp&op=orderrefund" target="_blank">->订单退款-(传参 orderId)</a>'."</br>";
        echo '<a href="?act=erp&op=ordercancell" target="_blank">->取消订单-(传参 orderId)</a>'."</br>";
        
        echo "</br>";
        echo '*******库存模块*******'."</br>";
        echo '<a href="?act=erp&op=goodsinfo" target="_blank">->查询某一商品档案信息</a>'."</br>";
        echo '<a href="?act=erp&op=goodsgetstocks" target="_blank">->查询某一商品在各仓的库存情况</a>'."</br>";
        echo '<a href="?act=erp&op=goodsgetstocksinfo" target="_blank">->查询某一商品在指定仓库(一个或多个)的商品信息及库存</a>'."</br>";
        echo '<a href="?act=erp&op=goodsgetstocksid" target="_blank">->查询指定仓库(一个或多个)所有商品及库存</a>'."</br>";
        
        echo "</br>";
        echo '*******医院模块*******'."</br>";
        echo '<a href="?act=erp&op=hospitalareaall" target="_blank">->查询所有区域</a>'."</br>";
        echo '<a href="?act=erp&op=hospitalarea" target="_blank">->查询某一区域的所有医院列表</a>'."</br>";
        echo '<a href="?act=erp&op=areaorbrand" target="_blank">->查询某一区域的指定品牌(一个或多个)的医院列表</a>'."</br>";
        echo '<a href="?act=erp&op=areabase" target="_blank">->查询指定医院(一个或多个)基本信息接口</a>'."</br>";
        echo '<a href="?act=erp&op=areaextend" target="_blank">->查询某一医院的详细信息(基本信息+扩展信息)</a>'."</br>";
      
    }
    
    //查询所有区域
    public function hospitalareaallOp(){
    	require_once SCRIPT_ROOT.'/base/'.'hospital.php';    	
    	$param=[];
    	$hospital = new Hospital();
    	$result=json_decode($hospital->areaall($param),1); 
    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);    		
    		if(isset($message['code'])&&$message['code']=='200'){ //判断接口返回状态
    			$data=$message['data'];
    			var_dump($data);
    			if(is_array($data)){
    				return $data;
    			}else{
    				return [];
    			}
    			
    		}
    	} 
    }
    
    //查询某一区域的所有医院列表
    public function hospitalareaOp(){
    	require_once SCRIPT_ROOT.'/base/'.'hospital.php';    
    	$param=[];
    	$param['areaId']='13'; //区域ID
    
    	$hospital = new Hospital();
    	$result=json_decode($hospital->area($param),1);
    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		print_r($message);
    	}
    	
    	
    }
    
    //查询某一区域的指定品牌(一个或多个)的医院列表
    public function areaorbrandOp(){
    	require_once SCRIPT_ROOT.'/base/'.'hospital.php';
    	$param=[];
    	$param['brandids']=''; //brandids
    	$param['areaId']='123'; //区域ID
    
    	$hospital = new Hospital();
    	$hospital->areaorbrand($param);
    	 
    }
    
    //查询指定医院(一个或多个)基本信息接口
    public function areabaseOp(){
    	require_once SCRIPT_ROOT.'/base/'.'hospital.php';
    	$param=[];
    	$param['hospitalIds']='123'; //医院ID    	
    
    	$hospital = new Hospital();
    	$result=$hospital->base($param);
    	var_dump($result);
    
    }
    
    //查询某一医院的详细信息(基本信息+扩展信息)
    public function areaextendOp(){
    	require_once SCRIPT_ROOT.'/base/'.'hospital.php';
    	$param=[];
    	$param['hospitalIds']='123'; //医院ID
    
    	$hospital = new Hospital();
    	$hospital->extend($param);
    
    }
    
    //查询某一商品档案信息
    public function goodsinfoOp(){
    	require_once SCRIPT_ROOT.'/order/'.'stock.php';
    	 
    	$param=[];
    	$param['GoodsId']='120100001'; //商品货号Id    	
    	 
    	$Stock = new Stock();
    	$result=json_decode($Stock->Goodsinfo($param),1);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		var_dump($message);die;
    		$data=$message;  
    		var_dump($data)  	;	
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    	
    }
    
    //查询某一商品在各仓的库存情况
    public function goodsgetstocksOp(){
    	require_once SCRIPT_ROOT.'/order/'.'stock.php';
    	
    	$param=[];
    	$param['GoodsId']='120100001'; //商品货号Id
    	
    
    	$Stock = new Stock();
    	$result=json_decode($Stock->Goodsgetstocks($param),1);   
    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态        		
    		$message=json_decode($result['msg'],1);    		
    		$data=$message; 
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		} 
    	}
    	
    }
    
    //查询某一商品在指定仓库(一个或多个)的商品信息及库存
//    public function goodsgetstocksinfoOp(){
//    	require_once SCRIPT_ROOT.'/order/'.'stock.php';
//    	$erp=Logic('erp_stock');
//    	$erp->syncGoodsByChainStock('100192');die;
//
//    	$param=[];
//    	$param['GoodsId']='120100001'; //商品货号Id
//    	$param['deptId']='123'; //部门Id,多个部门用逗号隔开
//
//    	$Stock = new Stock();
//    	$result=json_decode($Stock->GoodsgetStocksInfo($param),1);
//    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
//    		$message=json_decode($result['msg'],1);
//    		$data=$message;
//    		var_dump($data)	;
//    		if(is_array($data)){
//    			return $data;
//    		}else{
//    			return [];
//    		}
//    	}
//    }
    
    //查询指定仓库(一个或多个)所有商品及库存
    public function goodsgetstocksidOp(){
    	require_once SCRIPT_ROOT.'/order/'.'stock.php';
    
    	$param=[];
    	$param['StockId']='123'; //仓库Id    	
    	
    	$Stock = new Stock();
    	$result=json_decode($Stock->GoodsgetStocksId($param),1);    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		$data=$message;      		
    		print_r($data);die;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    }
    
   
    
    
    
    //会员添加
    public function memberaddOp(){    	  	
    	require_once SCRIPT_ROOT.'/base/'.'member.php';   	
    	
    	$param=[];
    	$param['Mobile']='18688981257';
    	if(isset($_GET['Mobile'])){
    		$param['Mobile']=$_GET['Mobile'];
    	}
    	/* $param['Membername']='测试用户名称';
    	$param['Nickname']='测试昵称';
    	$param['BrandName']='瑞鹏'; */
    	$param['Membersource']='4';    

    	$member = new Member();
    	$result=$member->baseMemberadd($param);   
    	var_dump($result) ;
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		return  true;    	
    	}else{    		
    		return false;
    	}
    	   
    }
    
    //会员编辑
    public function membereditOp(){
    	
    	require_once SCRIPT_ROOT.'/base/'.'member.php';
    	 
    	$param=[];
    	$param['MemberId']='fa88ea22483424b2';
    	if(isset($_GET['MemberId'])){
    		$param['MemberId']=$_GET['MemberId'];
    	}
    	$param['Membername']='编辑测试用户名称';
    	$param['Nickname']='编辑测试昵称';
    	$param['BrandName']='瑞鹏';
    	$param['Membersource']='3';
    
    	$member = new Member();
    	$result=$member->baseMemberedit($param);   
    	var_dump($result) ;
    }

    
     //会员基本信息查询
    public function memberinfosOp(){
    	
    	require_once SCRIPT_ROOT.'/base/'.'member.php';
    	 
    	$param=[];
    	$param['memberIdStr']='fa88ea22483424b2'; //用户Id,逗号分隔
    	//$param['isvalid']=true;  //true 只显示有效用户,false 显示所有用户
    	
    	if(isset($_GET['memberIdStr'])){
    		$param['memberIdStr']=$_GET['memberIdStr'];
    	}
    	$member = new Member();
    	$result=$member->basicmemberinfos($param);
    	var_dump($result);die;
    	
    }
    //会员详细信息查询
    public function detailmemberinfosOp(){
    	 
    	require_once SCRIPT_ROOT.'/base/'.'member.php';
    
    	$param=[];
    	$param['memberIdStr']='fa88ea22483424b2'; //用户Id,逗号分隔
    	//$param['isvalid']=true;  //true 只显示有效用户,false 显示所有用户
    	if(isset($_GET['memberIdStr'])){
    		$param['memberIdStr']=$_GET['memberIdStr'];
    	}
    
    	$member = new Member();
    	$result=$member->detailmemberinfos($param);    
    	var_dump($result);die;
    }
    
    //查询某一用户的账户信息
    public function accountmemberinfosOp(){
    
    	require_once SCRIPT_ROOT.'/base/'.'member.php';
    
    	$param=[];
    	$param['memberIdStr']='fa88ea22483424b2'; //用户Id,逗号分隔
    	//$param['isvalid']=true;  //true 只显示有效用户,false 显示所有用户
    	if(isset($_GET['memberIdStr'])){
    		$param['memberIdStr']=$_GET['memberIdStr'];
    	}
    	$member = new Member();
    	$member->accountmemberinfos($param);
    }
    
    //用户登陆
    public function memberloginOp(){
    
    	require_once SCRIPT_ROOT.'/base/'.'member.php';
    	$rsa=new Rsa();
    	$param=[];
    	/* $param['serialNumber']=''; //流水号, 唯一, 不可重复
    	$param['mobile']=''; //手机号
    	$param['sourceId']=''; //平台编码 */
    	$param['mobile']='***********'; //签名值 待签格式 rp_mobile_sourceId
    	if(isset($_GET['mobile'])){
    		$param['mobile']=$_GET['mobile'];
    	}
    	
    	$member = new Member();
    	$result=$member->login($param);    
    	print_r($result);	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$jwt=$result['msg'];
	    	/* $token = substr($jwt, strpos($jwt,".")+1, strrpos($jwt,".")- strlen($jwt));
			$arr = json_decode(base64_decode($token),true); */
    		$jwt=str_replace('"',"",$jwt);
    		    		
			wkcache("***********", $jwt,7*24*60);    	
    	}
    	
    }   
    
    //我的积分
    public function myOp(){
    
    	require_once SCRIPT_ROOT.'/order/'.'points.php';
    
    	$param=[];
    	$param['memberId']='fa88ea22483424b2'; //会员ID    	
    
    	$points = new Points();
    	$points->my($param);
    }
    
    //积分消耗
    public function consumeOp(){
    
    	require_once SCRIPT_ROOT.'/order/'.'points.php';
    
    	$param=[];
    	$param['memberId']='fa88ea22483424b2'; //会员ID
    	$param['score']='3'; //积分
    	$param['remark']='消耗积分'; //备注    	
    
    	$points = new Points();
    	$points->consume($param);
    }
    
    
    
    
    //订单添加
    public function orderaddOp(){
    
    	require_once SCRIPT_ROOT.'/order/'.'order.php';
    
    	$param=[];
    	//$param['petid']=''; //主键
    	//$param['cancelltime']=''; //取消时间
    	//$param['paytime']=time(); //支付时间
    	//$param['refundtime']=''; //退款时间
    	//$param['platfrom']=''; //平台名称
    	//$param['orderid']=''; //主键
    	$param['memberid']='792fed1115fca189'; //用户编号
    	if(isset($_GET['memberid'])){
    		$param['memberid']=$_GET['memberid'];
    	}
    	//$param['orderstate']=2; //订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
    	//$param['payway']=3; //支付方式(1-现金，2.银联卡,3-微信,4-支付宝,5-积分等)
    	
    	$param['orderdetail']='订单明细'; //订单明细，暂用 json存数据
    	$param['platformid']=1; //平台ID 电商送 1
    	$param['belonghospitalid']=0; //订单所属分院  默认送0    	
    	//$param['createtime']=''; //创建时间
    	//$param['lasttime']=''; //最后操作时间    	
    	
    	$goods_arr[]=array(
    			//'id'=>'', //商品明细订单id
    			//'orderid'=>'1000000000003701', //订单编号(guid)
    			'goodsid'=>'120300007', //商品编号
    			//'barcode'=>'', //条形码
    			'name'=>'商品名称一', //商品名称
    			'univalence'=>'260', //商品单价
    			'sellprice'=>'260', //商品售价
    			'quantity'=>2, //商品数量
    			'unit'=>1, //商品单位
    			//'applyhospitalid'=>'', //商品适用商品(默认值0，所有分院都可以适用)
    			'chargeoff'=>2, //订单商品核销状态： 1-不用核销 2-需要核销 3-已核销
    			//'chargeoffobject'=>'', //商品核销对象 – 分院编号
    			//'chargeoffobjectname'=>'', //商品核销对象 – 分院名称
    			//'lasttime'=>'', //最后操作时间
    			//'createtime'=>'', //创建时间
    			//'chargeofftime'=>'', //核销时间
    			//'chargeoffhospitalid'=>'' //核销对象-分院编号    			
    			
    	);  
  
    	$param['goods']=$goods_arr;     	
    
    	$order = new Order();
    	$result=$order->Orderadd($param);
    	print_r($result);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		
    		var_dump($message);die;
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    	var_dump($result);
    }
    
    //订单支付
    public function orderpayOp(){
    	require_once SCRIPT_ROOT.'/order/'.'order.php';
    	$orderid=$_GET['orderid'];
    	$param=[];
    	$param['orderId']='53A8C3ED4CA68BBB';//'3F53D83FC318D426'; //订单号
    	if(isset($_GET['orderId'])){
    		$param['orderId']=$_GET['orderId'];
    	}
    	$param['payWay']=3; //支付方式(1-现金，2.银联卡,3-微信,4-支付宝,5-积分等)  	
    	
    	$order = new Order();
    	$result=$order->Orderpay($param);
    	var_dump($result);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		var_dump($message);die;
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    	
    }
    //订单查询
    public function ordergetOp(){
    	require_once SCRIPT_ROOT.'/order/'.'order.php';
    	$orderid=$_GET['orderid'];
    	$param=[];
    	$param['id']='53A8C3ED4CA68BBB';//'10E8DED561BF3CE6'; //订单号ID    	
    	if(isset($_GET['id'])){
    		$param['id']=$_GET['id'];
    	}
    	$order = new Order();    	
    	$result=json_decode($order->Orderget($param),1);

    	var_dump($result);die;
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态    	
    		$message=json_decode($result['msg'],1);    	
    		var_dump($message);die;
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    }
    
    //根据手机号获取订单列表
    public function ordergetbymobileOp(){
    	require_once SCRIPT_ROOT.'/order/'.'order.php';
    	 
    	$param=[];
    	$param['mobile']='CA015F079D090B1F'; //手机号码
    	$param['beginTime']=''; //开始时间
    	$param['endTime']=''; //结束时间
    
    	$order = new Order();
    	$order->OrdergetByMobile($param);
    }
    //订单退款
    public function orderrefundOp(){
    	require_once SCRIPT_ROOT.'/order/'.'order.php';
    	$orderid=$_GET['orderid'];
    	$param=[];
    	$param['orderId']='526E541C6FD21666';//'CA015F079D090B1F'; //订单号
    
    	if(isset($_GET['orderId'])){
    		$param['orderId']=$_GET['orderId'];
    	}
    	$order = new Order();
    	$result=$order->Orderrefund($param);
    	var_dump($result);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		var_dump($message);die;
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    }

    //取消订单
    public function ordercancellOp(){
    
    	require_once SCRIPT_ROOT.'/order/'.'order.php';
    	$orderid=$_GET['orderid'];
    	$param=[];
    	$param['orderId']='D12A267B0BBE33F6';//'CA015F079D090B1F'; //订单号    	
    	if(isset($_GET['orderId'])){
    		$param['orderId']=$_GET['orderId'];
    	}
    	$order = new Order();
    	$result=$order->Ordercancell($param);    
   		var_dump($result);die;
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    	
    		$message=json_decode($result['msg'],1);    	
    		$data=$message;    		
    		if($data){    		
    			return $data;
    		}else{
    			return [];
    		}
    	}
    	
    }
    
    public function pushnoticeOp(){
    	define('SCRIPT_ROOT_REVT',  BASE_DATA_PATH.'/api/rvet');
    	/* require_once SCRIPT_ROOT_REVT.'/lib/rsa.php'; */
    	require_once SCRIPT_ROOT_REVT.'/msg.php';
    	//调用demo
    	$msg = new Msg();
    	 $rlt = $msg->psuh([
    	 		'object' => 1,   //1用户 2 医生(必填 用户填 1)
    	 		'mobile' => '18124688120',  //用户或医生id/手机号 (必填)
    	 		'title' => '测试测试',    //标题 (必填)
    	 		'content' => 'xsxxx',  //内容必填
    	 		//    'data_id' => 0,// 数据ID (选填 默认为0)
    	 		//    'url' => '',  //外链接(选填填)
    	 		'type' => 8  //type 通知类型 (选填 电商填 8)
    	 		]);
    	
    	echo '<pre>';
    	print_r($rlt);die;
    	
    	/* $param=array();
    	$param['object']=1;
    	$param['object_id']=1;
    	$param['title']="标题";
    	$param['content']="内容";
    	$param['data_id']=0;
    	//$param['URL']=1;
    	$param['type']=8;
    /* 	$login=new Login();    	 
    	$login->pushnotice($param); */   
    	
    
    }
    
    public function logintestOp(){
    	//登录/注册ERP
    	$member_mobile='18319034355';
    	$erp=Logic('erp_member');
    	echo ($erp->loginERP($member_mobile)) ;
    }
    
    /**
     * 批量替换手机
     */
    public  function memberlistOp(){
    	die("已经执行");
    	$list=Model('member')->getMemberList(array("member_id",array("gt",0)),"member_id,member_name",0,'',10000000000);
    	foreach ($list as $v){
    		if(preg_match("/^1[345678]{1}\d{9}$/",$v['member_name'])){    
    			$phone =$v['member_name'];
    			$num = substr($phone,-4);
    			$logic_connect_api = Logic('connect_api');
    			$member_name = $logic_connect_api->getMemberName('upet', $num);
    
    			Model('member')->editMember(array('member_id'=> $v['member_id']),array('member_name'=> $member_name));
    
    		}
    	}
    	var_dump($list);
    }
    
    public function testOp(){
    	
    	//$vr_order_list=Model('vr_order_code')->where(array("order_id"=>217))->select();
    	$order_id=2962;
    	
    	$model_v_order=Model("vr_order");
    	$vr_goodsinfo=$model_v_order->getOrderInfo(array("order_id"=>$order_id));    	
    	
    
    	wkcache("test2018_3", $vr_goodsinfo);
    	var_dump($vr_goodsinfo);die;
    	
    	
    	
    }
}
