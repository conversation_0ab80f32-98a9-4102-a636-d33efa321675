<?php
/**
 * 操作ERP门店类
 * <AUTHOR>
 * @date 2018.10.23
 */

use Upet\Queues\UploadLocalFileQueue;

defined('InShopNC') or exit('Access Invalid!');
class erp_chainLogic {	
	
 	public function __construct(){
 	   header("Content-Type: text/html;charset=utf-8");
         /*define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
       require_once SCRIPT_ROOT.'/base/'.'hospital.php'; */
    }
   
	/**
	 * 查询门店所有区域
	 */
	public function hospitalareaall(){		
		$param=[];	
		$hospital = new Hospital();		
		$result=json_decode($hospital->areaall($param),1);	
		if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
			$message=json_decode($result['msg'],1);
			if(isset($message['code'])&&$message['code']=='200'){ //判断接口返回状态
				$data=$message['data'];
				if(is_array($data)){
					return $data;
				}else{
					return [];
				}
				 
			}
		}
	}
	
	/**
	 * 查询某一区域的所有医院列表
	 * @param int $areaId 区域ID
	 */
	public function hospitalarea($areaId){		
		$param=[];
		$param['areaId']=$areaId; //区域ID
	
		$hospital = new Hospital();
		$result=json_decode($hospital->area($param),1);
		 
		if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
			$message=json_decode($result['msg'],1);
			if(is_array($message)){
					return $message;
			}else{
					return [];
			}
		}
	}
	
	/**
	 * 同步门店
	 */
	public function syncChain(){
        return false;//关闭原ERP同步
        $erp_area_arr = $this->hospitalareaall();
        $data = [];
        $paths = [];
        foreach ($erp_area_arr as $v) {
            $hospital_arr = $this->hospitalarea($v['id']);
            foreach ($hospital_arr as $vv) {
                if ($vv['hospitalid'] <= 0) {
                    continue;
                }
				
				$model_chain=Model('chain')->where(array('chain_erp_id'=>$vv['hospitalid']))->select();				
				if(!$model_chain){
					$arr_img=explode("/", $vv['img']);
					$img=array_pop($arr_img);
					$upload_img=@copy($vv['img'], BASE_UPLOAD_PATH.DS.ATTACH_CHAIN.DS."1".DS.$img);
					$paths[] = ATTACH_CHAIN.DS."1".DS.$img;

					$arr_thumbnail=explode("/", $vv['thumbnail']);
					$thumbnail=array_pop($arr_thumbnail);
					$upload_thumbnail=@copy($vv['thumbnail'], BASE_UPLOAD_PATH.DS.ATTACH_CHAIN.DS."1".DS.$thumbnail);
					$paths[] = ATTACH_CHAIN.DS."1".DS.$thumbnail;

					$insert = array();
					$insert['store_id']     = $_SESSION['store_id']?$_SESSION['store_id']:1;
					$insert['store_name']   = $_SESSION['store_name'];
					$insert['chain_user']   = 'upet_e'.$vv['hospitalid'];
					$insert['chain_pwd']    = md5("123456");
					$insert['chain_name']   = $vv['name'];
					if($upload_img){
						$insert['chain_img']    = $this->_check_img($img);
						$insert['chain_banner']    = $this->_check_img($img);
					}

					if($upload_thumbnail){
						$insert['chain_logo']    = $this->_check_img($thumbnail);
					}
					/*  $insert['area_id_1']    = intval($_POST['area_id_1']);
					 $insert['area_id_2']    = intval($_POST['area_id_2']);
					$insert['area_id_3']    = intval($_POST['area_id_3']);
					$insert['area_id_4']    = intval($_POST['area_id_4']);
					$insert['area_id']      = intval($_POST['area_id']); */
					/*  $insert['area_info']    = $vv['areatext']; */
					$insert['chain_address']= $vv['address'];
					/* $insert['chain_erp_id']= $vv['hospitalid']; */
					$insert['chain_phone']  = $vv['fixphone'];
					$insert['chain_opening_hours']  = "0:00-24:00";
					/*  $insert['chain_traffic_line']   = $_POST['chain_traffic_line']; */
					$insert['chain_apply_time'] = TIMESTAMP;
					$insert['chain_cycle'] = 31;
					$insert['transport_areas'] = serialize(array());
					$insert['express_city'] = serialize(array());
					$insert['lat']   =$vv['lat'];
					$insert['lng']   = $vv['longitude'];
					$insert['jxaddress']   = $vv['address'];
					$insert['is_own'] = 1;
					$insert['chain_state'] = 2; //待审核
					$insert['chain_time'] = TIMESTAMP;
					$insert['chain_lat'] =$vv['lat'] ;
					$insert['chain_lng'] =$vv['longitude'] ;
					$insert['chain_erp_id'] =$vv['hospitalid'] ; 
					$insert['chain_erp_name']  = $vv['name'];
					$insert['chain_erp_status'] =1 ; //ERP同步
					$insert['chain_erp_time'] =TIMESTAMP;					
					array_push($data, $insert);										
					
				}else{	
					$updata = array();		
					
					if($model_chain['chain_state']==1){	
						//$updata['chain_state']=$vv['isenabled'];
					}else{
						//$updata['chain_state']=2;//待审核
					}
					if(strpos($model_chain['chain_img'],"http://")!==false || strpos($model_chain['chain_img'],"https://")!==false){
						$arr_img=explode("/", $vv['img']);
						$img=array_pop($arr_img);
						$upload_img=@copy($vv['img'], BASE_UPLOAD_PATH.DS.ATTACH_CHAIN.DS."1".DS.$img);
						$paths[] = ATTACH_CHAIN.DS."1".DS.$img;
						
						$arr_thumbnail=explode("/", $vv['thumbnail']);
						$thumbnail=array_pop($arr_thumbnail);
						$upload_thumbnail=@copy($vv['thumbnail'], BASE_UPLOAD_PATH.DS.ATTACH_CHAIN.DS."1".DS.$thumbnail);
						$paths[] = ATTACH_CHAIN.DS."1".DS.$thumbnail;
						
						if($upload_img){
							$updata['chain_img']    = $this->_check_img($img);
							$updata['chain_banner']    = $this->_check_img($img);
						}
						
						if($upload_thumbnail){
							$updata['chain_logo']    = $this->_check_img($thumbnail);
						}
					}
					/* $updata['chain_img']    = $this->_check_img($vv['img']);
					$updata['chain_banner']    = $this->_check_img($vv['img']);
					$updata['chain_logo']    = $this->_check_img($vv['thumbnail']); */


					$updata['store_id']     = $_SESSION['store_id']?$_SESSION['store_id']:1;
					/* $updata['chain_phone']  = $vv['fixphone']; */
					/* $updata['chain_name']  = $vv['name']; */
					$updata['chain_erp_name']  = $vv['name'];					
					/* $updata['chain_opening_hours']  = "0:00-24:00";		 */		
					$updata['chain_erp_time'] =TIMESTAMP;
					
					Model('chain')->where(array('chain_erp_id'=>$vv['hospitalid']))->update($updata);
					
				}							                    
			}
		}
		
		if(!empty($data)){
			Model('chain')->insertAll($data);
		}

		// 如果存在图片，上传至OSS
		if($paths){
		    UploadLocalFileQueue::dispatch($paths);
        }

		return callback(true, '同步完成', 'chain');		
				
	}
	
	/**
	 * 检查文件名是否为图片
	 * @param unknown $filename
	 */
	private function _check_img($fileurl){
		$imgArr = array('gif','bmp','png','ico','jpg','jepg','jpeg');			
		$arr=[];
		$arr=explode('.', $fileurl);
		$ext=strtolower(array_pop($arr));
		if(!in_array($ext,$imgArr)){
			return '';
		} else{
			return $fileurl;
		}
	}

    /**
     * 获取门店医生
     * lihaobin
     * 2019-05-14
     *
     */
	public function getChianHisgetdoctors($chain_erp_id='',$page=100){

        $hospital = new Hospital();
        if(!$chain_erp_id){
            $data= Model('chain')->field('chain_id,store_id,chain_erp_id')->where(array('chain_erp_id'=>array('gt',0),'chain_erp_status'=>1))->page($page)->select();
            if($data){
                $param=[];
                foreach ($data as $val){
                    $param['hospitalId'] = $val['chain_erp_id'];
                    $result=json_decode($hospital->hisgetdoctors($param),1);

                    if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
                        $message=json_decode($result['msg'],1);
                        if(is_array($message)){
                            $this->_addChainUser($message,$val['chain_id'],$val['store_id'],$val['chain_erp_id']);
                        }
                    }
                }
            }
        }else{
            $data= Model('chain')->field('chain_id,store_id,chain_erp_id')->where(array('chain_erp_id'=>$chain_erp_id,'chain_erp_status'=>1))->find();

            $param=[
                'hospitalId'=>$data['chain_erp_id']
            ];
            $result=json_decode($hospital->hisgetdoctors($param),1);

            if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
                $message=json_decode($result['msg'],1);
                if(is_array($message)){
                    $this->_addChainUser($message,$data['chain_id'],$data['store_id'],$data['chain_erp_id']);
                }
            }
        }
        $page_count = Model()->gettotalpage();

       return array('page_count'=>$page_count,'data'=>($data[0]));

    }

    /**
     * 增加门店用户信息
     */
    private function _addChainUser($data,$chain_id,$store_id,$chain_erp_id){

        $hospital = new Hospital();
        $data_list = [];

        foreach ($data['data'] as $key=>$val){

            $doctorId = $val['doctorid'];
            $result=json_decode($hospital->hisgetdoctorposition($doctorId),1);

            if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
                $message=json_decode($result['msg'],1);

                if ($message) {
                    $docter = Model('chain_user')->getChainUserInfo(array('chain_id'=>$chain_id,'doctor_id'=>$val['doctorid']));
                    if(!$docter){
                        $arr = [];
                        $arr_img=explode("/", $message['img']);
				        $str= array_pop($arr_img);	
				        $arr_img2=explode("?", $str); 
				        $img=array_shift($arr_img2);	             	                    
				        $img='user_'.$img;	          
				        $upload_img=@copy($message['img'], BASE_UPLOAD_PATH.DS.ATTACH_CHAIN.DS."1".DS.$img);        

                        $arr['user_name'] = $message['name'];
                        $arr['erp_name'] = $message['user_name'];
                        $arr['chain_id'] = $chain_id;
                        $arr['store_id'] = $store_id;
                        $arr['chain_erp_id'] = $chain_erp_id;
                        $arr['doctor_id'] = $val['doctorid'];
                        $arr['doctor_position_id'] = $message['positionid'];
                        $arr['doctor_position_name'] = empty($message['positionname'])?'住院医生':$message['positionname'];
                        $arr['create_time'] = time();
                        if($upload_img){
                        	$arr['user_img'] = $this->_check_img($img);
                        }                        
                        $arr['selfintroduction'] = $message['selfintroduction'];
                        $arr['skills'] = $message['skills'];
                        $arr['registerno'] = $message['registerno'];
                        $arr['license'] = $message['license'];
                        $arr['sectionoffice'] = $message['sectionoffice'];
                        $arr['worktime'] = $message['worktime'];
                        $arr['workingmode'] = $message['workingmode'];
                        $arr['remark'] = $message['remark'];
                        $arr['profession'] = $message['profession'];
                        $arr['isjudge'] = $message['isjudge'];
                        $data_list[] = $arr;
                    }else{
                        $arr=[];
                        $arr['user_name'] = $message['name'];
                        $arr['doctor_position_id'] = $message['positionid'];
                       /*  $arr['doctor_position_name'] = empty($message['positionname'])?'住院医生':$message['positionname'];
                        $arr['user_img'] = $message['img'];
                        $arr['selfintroduction'] = $message['selfintroduction'];
                        $arr['skills'] = $message['skills']; */

                        if(strpos($docter['user_img'],"https://")!==false || strpos($docter['user_img'],"http://")!==false){
			            $arr_img=explode("/", $message['img']);
			            $str= array_pop($arr_img);	             
            	        $arr_img2=explode("?", $str); 
	                    $img=array_shift($arr_img2);	
	                    if($this->_check_img($img)){
			          	    $img='user_'.$img;
			                $upload_img=@copy($message['img'], BASE_UPLOAD_PATH.DS.ATTACH_CHAIN.DS."1".DS.$img); 
	
						    if($upload_img){
						     $arr['user_img']    = $this->_check_img($img);			
						    }
		            	 }else{
		             		$arr['user_img']    ="";	
		            	 } 
	           			}
                        $arr['registerno'] = $message['registerno'];
                        $arr['license'] = $message['license'];
                        $arr['sectionoffice'] = $message['sectionoffice'];
                        $arr['worktime'] = $message['worktime'];
                        $arr['workingmode'] = $message['workingmode'];
                        $arr['remark'] = $message['remark'];
                        $arr['profession'] = $message['profession'];
                        $arr['isjudge'] = $message['isjudge'];

                         Model('chain_user')->updateChainUser(array('chain_id'=>$chain_id,'doctor_id'=>$val['doctorid']),$arr);

                    }
                }
            }else{
                $docter = Model('chain_user')->getChainUserInfo(array('chain_id'=>$chain_id,'doctor_id'=>$doctorId));
                if (!$docter) {
                    $arr=[];
                    $arr['user_name'] = $val['doctorname'];
                    $arr['doctor_id'] = $val['doctorid'];
                    $arr['doctor_position_name'] ='住院医生';
                    $arr['chain_id'] = $chain_id;
                    $arr['store_id'] = $store_id;
                    $arr['chain_erp_id'] = $chain_erp_id;
                    $arr['create_time'] = time();
                    $data_ow_list[]=$arr;
                }
            }
        }

        if($data_list){
            Model('chain_user')->addChainUserAll($data_list);
        }
        if($data_ow_list){
            Model('chain_user')->addChainUserAll($data_ow_list);
        }

    }
    
}
