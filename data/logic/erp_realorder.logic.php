<?php
/**
 * 操作ERP订单类
 * <AUTHOR>
 * @date 2018.10.23
 */

use Upet\Integrates\Redis\RedisManager;

defined('InShopNC') or exit('Access Invalid!');

class erp_realorderLogic
{

      	public function __construct(){
            header("Content-Type: text/html;charset=utf-8");
           define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
           require_once SCRIPT_ROOT.'/order/'.'order.php';
        }


    /*
     * 订单添加
     */
    public function orderadd($param)
    {

        $order  = new Order();
        $result = $order->Orderadd($param);

        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return true;
        } else {
            return false;
        }
    }

    /**
     * 订单支付
     * @param string $orderId 订单号
     * @param int $payWay 支付方式(1-现金，2.银联卡,3-微信,4-支付宝,5-积分等)
     */
    public function orderpay($orderId, $goodsinfo)
    {

        $param                 = [];
        $param['orderId']      = $orderId;
        $param['serialnumber'] = $goodsinfo['order_sn'];   //流水号,建议用UUID

        $param['realityamount'] = $goodsinfo['order_amount'] * 100;

        $payways_arr[] = array(
            "payway" => 1, //支付方式(1-现金，2.积分,3-优惠券,4-线下优惠券等)
            "amount" => $goodsinfo['order_amount'] * 100 //integer支付 如为 货币 单位 是分
            //"operationcode"=>  //string 操作码 积分支付 传 操作码 优惠券支付 传 优惠券ID
        );

        $param['payways'] = $payways_arr;

        $order  = new Order();
        $result = $order->Orderpay($param);

        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return true;
        } else {
            return false;
        }
    }

    /**
     * 订单查询
     * @param int $id 订单号ID
     * @return mixed|multitype:
     */
    public function orderget($id)
    {

        $param             = [];
        $param['id']       = $id; //订单号ID
        $param['sourceId'] = 1;//来源ID 电商1

        $order  = new Order();
        $result = json_decode($order->Orderget($param), 1);

        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            $message = json_decode($result['msg'], 1);
            $data    = $message;
            if (is_array($data)) {
                return $data;//true\false
            } else {
                return [];
            }
        }
    }

    /**
     * 订单退款
     * @param string $orderId ERP订单号
     */
    public function orderrefund($orderId)
    {

        $param            = [];
        $param['orderId'] = $orderId; //订单号

        $order  = new Order();
        $result = $order->Orderrefund($param);

        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            $message = json_decode($result['msg'], 1);
            $data    = $message;
            if ($data) {
                return $data; //true\false
            } else {
                return [];
            }
        }


    }

    /**
     * 申请退货退款
     * @param string $orderId ERP订单号
     */
    public function orderrefunding($orderId, $refund_sn)
    {

        $param                 = [];
        $param['orderId']      = $orderId; //订单号
        $param['serialNumber'] = $refund_sn;   //流水号,建议用UUID

        $order  = new Order();
        $result = $order->Orderrefunding($param);
        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态

            return true;
        } else {

            return false;
        }
    }

    /**
     * 取消订单
     * @param string $orderId ERP订单号
     */
    //public function ordercancell($orderId,$refund_sn,$refundAmount = 0){ 旧的取消方法
    public function ordercancell($orderId, $detail)
    {
        $param = [];
        $param['order_id'] = $orderId; //订单号
        $param['detail']   = $detail;
        $order             = new Order();
        $result            = $order->orderReturnRelease($param);
        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return true;
        } else {
            log_error('order_timeout_cancel,取消订单失败', ['time' => date('Y-m-d H:i:s'), 'order_sn' => $orderId, 'result' => '取消ERP已支付订单异常失败']);
            return false;
        }
    }

    /**
     * 未支付 取消订单释放库存
     * @param $order_sn 订单编号
     * @return bool
     */
    public function noPayCancelOrder($order_sn)
    {
        $param            = [];
        $param['orderId'] = $order_sn;
        $order            = new Order();
        $result           = $order->orderCancelReturnRelease($param);
        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return true;
        } else {
            log_error('erp_orderRealCancell_Err', ['time' => date('Y-m-d H:i:s'), 'order_sn' => $order_sn, 'result' => '取消ERP未支付订单异常失败']);
            return false;
        }
    }

    /**
     * @param $orderId 订单编号
     * @param $detail   释放库存商品信息
     * @return bool
     */
    public function orderReturnRelease($orderId, $detail)
    {
        $param             = [];
        $param['order_id'] = $orderId; //订单号
        $param['detail']   = $detail;
        $order             = new Order();
        $result            = $order->orderReturnRelease($param);
        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return true;
        } else {
            return false;
        }
    }


    //同步添加实物订单
    public function syncOrderAdd($order_id)
    {

        $model_order = Model("order");

        $order_info = $model_order->getOrderInfo(array("order_id" => $order_id), array('order_common', 'order_goods'));
        if (!$order_info) {
            return false;
        }

        //获取ERP用户ID
        $member_model  = Model('member')->getMemberInfoByID($order_info['buyer_id'], "member_mobile");
        $member_mobile = $member_model['member_mobile'] ? $member_model['member_mobile'] : $order_info['buyer_phone'];
        //登录ERP
        $member_logic = Logic("erp_member");
        $member_id    = $member_logic->loginERP($member_mobile);

        if (!$member_id) {
            return false;
        }

        $erp_order_id = $order_info['order_sn'] . "_r1";

        $param = [];
        //$param['petid']=''; //主键
        //$param['cancelltime']=''; //取消时间
        //$param['paytime']=time(); //支付时间
        //$param['refundtime']=''; //退款时间
        //$param['platfrom']=''; //平台名称

        $param['orderid']   = $erp_order_id; //主键
        $param['ordertype'] = 4; //1 咨询订单 ; 2 门店订单 ; 3 预约订单;  4 电商订单;  5 积分订单
        /* $param['memberid']='fa88ea22483424b2'; //用户编号
        if(isset($_GET['memberid'])){
        $param['memberid']=$_GET['memberid'];
        } */
        //$param['orderstate']=2; //订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
        //$param['payway']=3; //支付方式(1-现金，2.银联卡,3-微信,4-支付宝,5-积分等)

        $param['orderdetail']      = "实物订单"; //订单明细，暂用 json存数据
        $param['platformid']       = 1; //平台ID 电商送 1
        $param['belonghospitalid'] = 0; //订单所属分院  默认送0
        //$param['createtime']=''; //创建时间
        //$param['lasttime']=''; //最后操作时间
        $param['isneedpost'] = 1; //是否需要邮寄
        $param['recipient']  = $order_info['extend_order_common']['reciver_name'];  //收件人
        $param['address']    = $order_info['extend_order_common']['reciver_info']['address'];   //收件地址
        $param['mobile']     = $order_info['extend_order_common']['reciver_info']['mob_phone'];    //联系电话

        $goods_arr = array();
        foreach ($order_info['extend_order_goods'] as $v) {

            $goods_arr[] = array(
                //'id'=>'', //商品明细订单id
                //'orderid'=>'1000000000003701', //订单编号(guid)
                'goodsid' => "", //商品编号 $v['goods_serial']
                'goodsimage' => cthumb($v['goods_image'], 360, $v['store_id']),
                'barcode' => "", //条形码 $v['goods_barcode']
                'name' => $v['goods_name'], //商品名称
                'univalence' => $v['goods_price'], //商品单价
                'sellprice' => $v['goods_price'], //商品售价 //$goodsinfo['order_amount']
                'quantity' => $v['goods_num'], //商品数量 $goodsinfo['goods_num']
                'unit' => 1, //商品单位
                //'applyhospitalid'=>'', //商品适用商品(默认值0，所有分院都可以适用)
                'chargeoff' => 1, //订单商品核销状态： 1-不用核销 2-需要核销 3-已核销
                //'chargeoffobject'=>'', //商品核销对象 – 分院编号
                //'chargeoffobjectname'=>'', //商品核销对象 – 分院名称
                //'lasttime'=>'', //最后操作时间
                //'createtime'=>'', //创建时间
                //'chargeofftime'=>'', //核销时间
                //'chargeoffhospitalid'=>'' //核销对象-分院编号

            );
        }


        $param['goods'] = $goods_arr;

        $push = $this->orderadd($param);

        if ($push) {
            $updata                     = array();
            $updata['erp_status']       = 1;
            $updata['erp_order_status'] = 1; //ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
            $updata['erp_order_id']     = $erp_order_id;
            $updata['erp_mobile']       = $member_mobile;
            $updata['erp_time']         = time();

            $model_order->editOrder($updata, array('order_id' => $order_info['order_id']));

            return $erp_order_id;

        } else {
            //异常订单处理
            $this->_error_log($order_info['order_id'], $order_info, "添加ERP订单异常失败", "syns_orderaddReal_error_list");
            return false;
        }

    }


    //同步取消实物订单
    public function syncOrderCancell($order_id)
    {
        $model_order = Model("order");

        $order_info = $model_order->getOrderInfo(array("order_id" => $order_id));
        if (!$order_info) {
            return false;
        }
        $refund_goodsinfo = $model_order->getOrderGoodsListToGjp(['order_goods.order_id' => $order_info['order_id']], "order_goods.sku sku,order_goods.goods_num num");
        if (is_array($refund_goodsinfo) && !empty($refund_goodsinfo)) {
            foreach ($refund_goodsinfo as $key => $value) {
                $refund_goodsinfo[$key]['num'] = intval($value['num']);
            }
        }
        $cancell = $this->ordercancell($order_info['order_sn'], $refund_goodsinfo);//todo 修改传值订单号

        if ($cancell) {
            $updata                     = array();
            $updata['erp_order_status'] = 4;
            $updata['erp_status']       = 1;
            $updata['erp_time']         = time();
            $model_order->editOrder($updata, array('order_id' => $order_info['order_id']));
            return true;
        } else {
            //异常订单处理
            log_error('order_timeout_cancel,同步取消实物订单异常', ['time' => date('Y-m-d H:i:s'), 'order_sn' => $order_info['order_sn'], 'result' => '取消ERP已支付订单异常失败']);
            return false;
        }

    }

    /**
     * ERP 未支付订单 库存释放
     * @param $order_info 订单信息
     * @return bool
     */
    public function syncCancelNoPayOrder($order_info)
    {
        $model_order = Model("order");
        $member_mobile = rc4(base64_decode($order_info['encrypt_mobile']));
        //登录ERP
        /** @var erp_memberLogic $member_logic */
        $member_logic = Logic("erp_member");
        $member_id    = $member_logic->loginERP($member_mobile);
        if (!$member_id) {
            log_error('order_timeout_cancel,未支付订单库存释放', ['time' => date('Y-m-d H:i:s'), 'order_sn' => $order_info['order_sn'], 'member_id' => $member_id]);
            return false;
        }
        $cancell = $this->noPayCancelOrder($order_info['order_sn']);
        if ($cancell) {
            $updata                     = array();
            $updata['erp_order_status'] = 4;
            $updata['erp_status']       = 1;
            $updata['erp_time']         = time();
            $model_order->editOrder($updata, array('order_id' => $order_info['order_id']));
            return true;
        } else {
            //异常订单处理
            return false;
        }
    }


    //同步退款实物订单
    public function syncOrderRefund($refund)
    {
        //不同意退款
        /* if($refund['admin_state'] == '3'){
            return false;
        } */
        $model_order = Model("order");
        $order_info  = $model_order->getOrderInfo(array("order_id" => $refund["order_id"]));
        if (!$order_info) {
            return false;
        }
        //获取ERP用户ID
        /* $member_model=Model('member')->getMemberInfoByID($goodsinfo['buyer_id'],"member_mobile");
        $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$goodsinfo['buyer_phone']; */

        $member_mobile = $order_info['erp_mobile'];
        //登录ERP
        $member_logic = Logic("erp_member");
        $member_id    = $member_logic->loginERP($member_mobile);

        /*if(!$order_info['erp_order_id']){
            return false;
        }*/

        /* if($refund['refund_type']==2){
            //退货订单
            $orderrefund=$this->orderrefunding($order_info['erp_order_id'],$refund['refund_sn']);
        }else{ */
        //退款订单
        /* $orderrefund=$this->ordercancell($order_info['erp_order_id'],$refund['refund_sn'],$refund['refund_amount']); */
        /* } */
        if ($refund['goods_id'] == "0") {
            $refund_goodsinfo = $model_order->getOrderGoodsListToGjp(['order_goods.order_id' => $order_info['order_id']], "order_goods.sku sku,order_goods.goods_num num");
            if (is_array($refund_goodsinfo) && !empty($refund_goodsinfo)) {
                foreach ($refund_goodsinfo as $key => $value) {
                    $refund_goodsinfo[$key]['num'] = intval($value['num']);
                }
            }
        } else {
            $ginfo              = Model('goods')->getGoodsInfo(array('goods_id' => $refund['goods_id']), 'goods_serial,goods_barcode,goods_price');
            $refund_goodsinfo[] = array(
                'sku' => $ginfo['goods_serial'],
                'num' => intval($refund['goods_num']),
            );
        }
        $orderrefund = $this->orderReturnRelease($refund['order_sn'], $refund_goodsinfo);

        if ($orderrefund) {
            $updata                     = array();
            $updata['erp_order_status'] = 3;
            $updata['erp_status']       = 1;
            $updata['erp_time']         = time();

            $model_order->editOrder($updata, array('order_id' => $order_info['order_id']));
            return true;
        } else {
            //异常订单处理
            $this->_error_log($refund['order_id'], $refund, "退款/退货ERP订单异常失败", "syns_orderRealRefund_error_list");
        }


    }

    //同步支付虚拟订单
    public function syncOrderpay($order_id, $update_order = array())
    {

        $model_order = Model("order");

        $order_info = $model_order->getOrderInfo(array("order_id" => $order_id));

        if (!$order_info) {
            return false;
        }

        $member_mobile = $order_info['erp_mobile'];

        $order_info['erp_order_id'] = $order_info['erp_order_id'] ? $order_info['erp_order_id'] : $update_order["erp_order_id"];

        if (!$order_info['erp_order_id']) {
            return false;
        }
        //登录ERP
        $member_logic = Logic("erp_member");
        $member_id    = $member_logic->loginERP($member_mobile);

        //支付订单
        $orderpay = $this->orderpay($order_info['erp_order_id'], $order_info);

        if ($orderpay) {
            $updata                     = array();
            $updata['erp_order_status'] = 2;
            $updata['erp_status']       = 1;
            $updata['erp_time']         = time();

            $model_order->editOrder($updata, array('order_id' => $order_info['order_id']));

        } else {
            //异常订单处理
            $this->_error_log($order_id, $update_order, "支付ERP订单异常失败", "syns_orderpayReal_error_list");
        }


    }

    //更新物流状态
    public function updateExpressState($order_id, $state = 2)
    {

        $model_order = Model("order");

        $order_info = $model_order->getOrderInfo(array("order_id" => $order_id), array('order_common'));
        if (!$order_info) {
            return false;
        }
        if (!$order_info['erp_order_id']) {
            return false;
        }

        $member_mobile = $order_info['erp_mobile'];

        //登录ERP
        $member_logic = Logic("erp_member");
        $member_id    = $member_logic->loginERP($member_mobile);


        $param            = [];
        $param['orderId'] = $order_info['erp_order_id'];

        //$pointOrder = Logic('erp_order')->orderget($param['orderId']);
        //$param['memeberId'] = $pointOrder['memberid'];//$member_info['info']['nameid'];
        $param['state']     = $state; //1--待发货，2-待收货，3-已完成
        $param['expressno'] = $order_info['shipping_code']; //物流单号
        //取得配送公司代码
        $express = rkcache('express', true);
        if (C('express_api') == '2') {//快递鸟
            $e_code = $express[$order_info['extend_order_common']['shipping_express_id']]['e_code_kdniao'];
        } else { //快递100
            $e_code = $express[$order_info['extend_order_common']['shipping_express_id']]['e_code'];
        }
        $param['expresscode'] = $e_code; //物流公司编码

        $order  = new Order();
        $result = $order->updateExpressInfo($param);

        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return true;
        } else {
            $this->_error_log($order_id, $state, '更新ERP实物订单物流状态失败', 'update_erp_realorderexpress_fail');
            return false;
        }
    }

    /**
     * 异常订单记录/任务计划执行
     * @param int $order_id
     * @param array $info
     * @param string $msg
     * @param string $logname
     */
    public function _error_log($order_id, $info, $msg, $log_name)
    {
        //异常订单处理
        $arr              = [];
        $arr["order_id"]  = $order_id;
        $arr["info"]      = $info;
        $arr['msg']       = $msg;
        $arr['date_time'] = date("Y-m-d H:i:s", time());

        $push = rkcache($log_name);
        if (!$push) {
            $push = [];
        }
        $push[$order_id] = $arr;

        wkcache($log_name, $push);
    }


    /**
     * 添加子订单(临时表)
     */
    public function add_d_order($split_order)
    {

        try {
            $model = Model('order');
            $model->beginTransaction();
            /* $order_id="5055";  //父订单号 */
            $order_d_id = $this->_createOrder($split_order);
            $this->_createOrderCommon($split_order, $order_d_id);
            $this->_createOrderGoods($split_order, $order_d_id);
            //$this->_updateOrder($split_order["order"]['order_id']);
            $model->commit();
            //var_dump('asda');
            return callback(true, '', $order_d_id);
        } catch (Exception $e) {
            $model->rollback();
            return callback(false, $e->getMessage());
        }
    }

    /**
     * 创建子订单(临时表)
     * @param array $split_order
     * @throws Exception
     */
    private function _createOrder($split_order)
    {
        $order_demolition_model         = Model('order_demolition');
        $split_order['order']['source'] = $split_order['order_goods'][0]['source'];
        if (C('dianyin_pay')) {
            $split_order['order']['payment_from'] = 1;
        }
        $order_id = $order_demolition_model->addOrder($split_order['order']);
        if (!$order_id) {
            throw new Exception('临时订单保存失败[未生成订单数据]');
        }
        return $order_id;
    }

    /**
     * 创建订单扩展表数据(临时表)
     * @param array $split_order
     * @throws Exception
     */
    private function _createOrderCommon($split_order, $order_d_id)
    {
        $order_demolition_common_model         = Model('order_demolition');
        $split_order['common']['reciver_info'] = $split_order['common']['reciver_info_serialize'];
        $split_order['common']['d_order_id']   = $order_d_id;
        unset($split_order['common']['reciver_info_serialize']);
        $id = $order_demolition_common_model->addOrderCommon($split_order['common']);
        if (!$id) {
            throw new Exception('临时订单保存失败[未生成订单扩展数据]');
        }
    }

    /**
     * 创建子订单商品数据(临时表)
     * @param array $split_order
     * @throws Exception
     */
    private function _createOrderGoods($split_order, $order_d_id)
    {
        $order_demolition_model = Model('order_demolition');

        //$goods_list 等于拆单返回的订单信息
        $order_goods = array();
        foreach ($split_order['order_goods'] as $goods_info) {
            $goods_info['d_order_id'] = $order_d_id;
            unset($goods_info['rec_id']);
            unset($goods_info['youzan_item_id']);
            unset($goods_info['youzan_oid']);
            unset($goods_info['youzan_pro_typeid']);
            unset($goods_info['invite_commis_rate']);
            unset($goods_info['source']);
            array_push($order_goods, $goods_info);
        }

        foreach ($order_goods as $k => $goods) {
            $insert = $order_demolition_model->addOrderGoods(array($order_goods[$k]));
            if (empty($insert)) {
                throw new Exception('临时订单保存失败[未生成商品数据]');
            }
        }
    }

    /**
     * 更新父订单状态(临时表)
     * @param unknown $order_id
     * @throws Exception
     */
    private function _updateOrder($order_id)
    {
        $model_order              = Model('order');
        $data                     = array();
        $data['order_demolition'] = 0; //'拆单状态 默认0，1为拆单'
        $condition                = array();
        $condition['order_id']    = $order_id;
        $update                   = $model_order->editOrder($data, $condition);
        if (!$update) {
            throw new Exception('订单更新失败[保存失败]');
        }
    }

    /**
     * 添加子订单
     */
    public function add_order($order_d_id)
    {
        try {
            $model = Model('order');
            $model->beginTransaction();

            $order_id = $this->_create_r_Order($order_d_id);
            $this->_create_r_OrderGoods($order_d_id, $order_id);
            $this->_update_r_Order($order_d_id);
            $model->commit();
            return callback(true, '', $order_d_id);


        } catch (Exception $e) {
            $model->rollback();
            return callback(false, $e->getMessage());
        }
    }

    /**
     * 创建子订单
     * @param string $order_id
     * @throws Exception
     */
    private function _create_r_Order($d_order_id)
    {

        $model_order            = Model('order');
        $order_demolition_model = Model('order_demolition');

        $find = $model_order->getOrderInfo(array("d_order_id" => array('eq', $d_order_id)));

        if ($find) {
            throw new Exception('订单保存失败[数据已经存在]');
        }
        $condition               = array();
        $condition['d_order_id'] = array('eq', $d_order_id);
        $order_info              = $order_demolition_model->getOrder($condition);

        $pay_sn                     = Logic('buy_1')->makePaySn($order_info['buyer_id']);
        $order_pay                  = array();
        $order_pay['pay_sn']        = $pay_sn;
        $order_pay['buyer_id']      = $order_info['buyer_id'];
        $order_pay['api_pay_state'] = 1; //默认已支付
        $order_pay_id               = $model_order->addOrderPay($order_pay);
        if (!$order_pay_id) {
            throw new Exception('订单保存失败[未生成支付单]');
        }
        $order_info['pay_sn']       = $pay_sn;
        $order_info['order_sn']     = Logic('buy_1')->makeOrderSn($order_pay_id);
        $order_info['order_father'] = $order_info['order_id'];

        unset($order_info['order_id']);
        unset($order_info['order_demolition_status']);

        $order_id = $model_order->addOrder($order_info);

        if (!$order_id) {
            throw new Exception('订单保存失败[未生成订单数据]');
        }
        return $order_id;

    }

    /**
     * 创建子订单商品数据
     * @param string $order_id
     * @throws Exception
     */
    private function _create_r_OrderGoods($d_order_id, $order_id)
    {
        $model_order             = Model('order');
        $order_demolition_model  = Model('order_demolition');
        $condition               = array();
        $condition['d_order_id'] = array('eq', $d_order_id);
        $goods_list              = $order_demolition_model->getOrderGoodsList($condition);

        $order_goods = array();
        foreach ($goods_list as $goods_info) {
            $goods_info['order_id'] = $order_id;
            unset($goods_info['d_rec_id']);
            unset($goods_info['d_order_id']);
            array_push($order_goods, $goods_info);
        }

        $insert = $model_order->addOrderGoods($order_goods);

        if (!$insert) {
            throw new Exception('订单保存失败[未生成商品数据]');
        }
    }

    /**
     * 更新父订单状态
     * @param unknown $order_id
     * @throws Exception
     */
    private function _update_r_Order($d_order_id)
    {
        $order_demolition_model          = Model('order_demolition');
        $data                            = array();
        $data['order_demolition_status'] = 1; //'最终拆单状态 默认0，1为拆单完成'
        $condition                       = array();
        $condition['d_order_id']         = $d_order_id;
        $update                          = $order_demolition_model->editOrder($data, $condition);
        if (!$update) {
            throw new Exception('订单更新失败[保存失败]');
        }
    }

    /**已废弃
     * 售后订单申请
     * update 2021-4-12
     */
//    public function syncOrderApplayAfter($refund)
//    {
//        $model_order = Model("order");
//        $order_info  = $model_order->getOrderInfo(array("order_id" => $refund["order_id"]), array('order_common'), "buyer_phone,demolition_from,order_id,payment_time,payment_code,order_amount");
//        if (!$order_info) {
//            return false;
//        }
//        $member_mobile = $order_info['buyer_phone'];
//        //登录ERP
//        $member_logic = Logic("erp_member");
//        $member_id    = $member_logic->loginERP($member_mobile);
//        if (!$member_id) {
//            return false;
//        }
//
//        //退款订单
//        $param                    = [];
//        $promotion_total          = $order_info['extend_order_common']['promotion_total'] ? $order_info['extend_order_common']['promotion_total'] : "0";
//        $param['order_source']    = intval($order_info['demolition_from']);//仓库所属1:(a8 or 全渠道) 2:管易 3:门店
//        $param['create_time']     = date("Y-m-d H:i:s", $refund['add_time']);//创建时间
//        $param['discount_amount'] = $promotion_total;//优惠金额
//        $param['express_name']    = "";//退货快递名称 (管易)
//        $param['express_num']     = "";//退货快递单号 (管易)
//        $param['order_sn']        = $refund['order_sn']; //订单号
//        //全渠道：01=无理由退换货 02=质量问题 03=损坏 04=错发 05=漏发
//        //管易：01=快递问题 0002=赝品 0003=有色差 0004=尺码不对 0005=布料有瑕疵 0006=商品质量有问题 0007=其它 0009=不想要 9999=其它平台发货
//        $param['refund_amount'] = $refund['refund_amount'];//退款金额
//        $param['refund_sn']     = $refund['refund_sn'];//退款单号
//        $param['refund_type']   = intval($refund['refund_type']);//申请类型:1为退款,2为退货,默认为1
//        if ($param['order_source'] == 1) {
//            $param['refund_type_sn'] = str_replace(array("1", "2"), array('JustRefund', 'RefundAndGoods'), $param['refund_type']);//JustRefund 仅退款 RefundAndGoods 退款退货
//            $param['reason_code']    = "01";
//        } else {
//            $param['reason_code'] = "0007";
//        }
//        $param['refund_reason'] = $refund['reason_info'];//退款原因
//        $param['status']        = "waitAgree";
//        $param['refund_remark'] = $refund['buyer_message'];
//        if ($refund['goods_id'] == "0") {
//            $refund_goodsinfo = $model_order->getOrderGoodsListToGjp(['order_goods.order_id' => $order_info['order_id']], "order_goods.oc_id,order_goods.sku goods_id,order_goods.goods_num quantity,order_goods.goods_price refund_amount,goods.goods_barcode barcode");
//            if (is_array($refund_goodsinfo) && !empty($refund_goodsinfo)) {
//                foreach ($refund_goodsinfo as $key => $value) {
//                    $refund_goodsinfo[$key]['quantity']      = intval($value['quantity']);
//                    $refund_amount                           = $value['quantity'] * $value['refund_amount'];
//                    $refund_goodsinfo[$key]['refund_amount'] = "$refund_amount";
//                }
//            }
//        } else {
//            $ginfo              = Model('goods')->getGoodsInfo(array('goods_id' => $refund['goods_id']), 'goods_serial,goods_barcode,goods_price');
//            $order_goods        = $model_order->getOrderGoodsInfo(['order_id' => $refund["order_id"], 'goods_id' => $refund['goods_id']], 'oc_id');
//            $refund_goodsinfo[] = array(
//                'barcode' => $ginfo['goods_barcode'],
//                'oc_id' => $order_goods['oc_id'],
//                'goods_id' => $ginfo['goods_serial'],
//                'quantity' => intval($refund['goods_num']),
//                'refund_amount' => $refund['refund_amount'],//$refund['refund_amount'],$ginfo['goods_price']
//            );
//        }
//        $param['refundGoodsOrders'] = $refund_goodsinfo;
//        $refundPayOrder[]           = array(
//            'account' => "",
//            'pay_time' => $order_info['payment_time'] ? date('Y-m-d H:i:s', $order_info['payment_time']) : "",
//            'pay_type_code' => $order_info['payment_code'],
//            'payment' => $order_info['order_amount'],
//        );
//        $param['refundPayOrder']    = $refundPayOrder;
//        $order                      = new Order();
//        $result                     = $order->orderApplayAfter($param);
//        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
//            return true;
//        } else {
//            $this->_error_log($refund['order_id'], $param, "ERP退款订单异常失败", "syns_applay_after_real_order_refund_error_list");
//            return false;
//        }
//    }
}
