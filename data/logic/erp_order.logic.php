<?php
/**
 * 操作ERP订单类 服务套餐订单
 * <AUTHOR>
 * @date 2018.10.23
 */

use Upet\Integrates\Redis\RedisManager;

defined('InShopNC') or exit('Access Invalid!');
class erp_orderLogic {	
	
/*  	public function __construct(){
 	   header("Content-Type: text/html;charset=utf-8");
       define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');     
       require_once SCRIPT_ROOT.'/order/'.'order.php';
    }
     */
    
    /*
     * 订单添加
     */
    public function orderadd($param){
    	
    	$order = new Order();
    	$result=$order->Orderadd($param);  
    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态    		
    		return true;
    	}else{
    		return false;
    	}
    }

    /*
    * 订单批量添加
    */
    public function order_batch_add($param){
        $order = new Order();//echo json_encode($param);echo '<pre>';print_r($param);
        $result=$order->OrderBatchAdd($param);//echo '<pre>';print_r($result);die;
        wkcache("test_order_" . $param['masterorderid'], $result, 7200);
        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
            return true;
        }else{
            return false;
        }
    }

    /**
     * 退款申请

     */
    public function applyRefund($param) {
        $order = new Order();
        $result=$order->applyErpRefund($param);

        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
            return $result['msg'];
        }else{
            return false;
        }
    }

    /**
     * 批量退款申请
     */
    public function batchApplyRefund($param) {
        $order = new Order();
        $result=$order->batchApplyErpRefund($param);
        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
            return $result['msg'];
        }else{
            return false;
        }
    }

    /**
     * 订单支付
     * @param string $orderId 订单号
     * @param int $payWay 支付方式(1-现金，2.银联卡,3-微信,4-支付宝,5-积分等)
     */
    public function orderpay($orderId,$vr_goodsinfo){
         
        $param=[];
        $param['orderId']=$orderId;
        $param['serialnumber']=$vr_goodsinfo['order_sn']."_v".$vr_goodsinfo['rec_id'];   //流水号,建议用UUID    
            
        $param['realityamount'] = $vr_goodsinfo['ordertype'] ? 0 : $vr_goodsinfo['pay_price']*100;
        
        $payways_arr[]=array(                                               
                "payway"=> $vr_goodsinfo['ordertype'] ? $vr_goodsinfo['ordertype'] : 1 , //支付方式(1-现金，2.积分,3-优惠券,4-线下优惠券等)
                "amount"=>$vr_goodsinfo['goods_price']*100 //integer支付 如为 货币 单位 是分
                //"operationcode"=>  //string 操作码 积分支付 传 操作码 优惠券支付 传 优惠券ID              
        );
        
        $param['payways']=$payways_arr;  
            
        $extendinfo_arr=array(
                "WrittenOffCode"=>$vr_goodsinfo['goods_serial']."-".$vr_goodsinfo['vr_code']."-".$vr_goodsinfo['vr_indate'] //商品id-核销码-有效期
        );
        
        $param['extendinfo']=$extendinfo_arr;
        
        $order = new Order();
        $result=$order->Orderpay($param);
        
        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
            return  true;           
        }else{
            return  false;
        }
    }

    /**
     * 订单支付 批量支付
     * @param string $param 支付参数
     */
    public function orderpayBatch($param){
        $order = new Order();
        $result=$order->OrderpayBatch($param);
        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
            return  true;
        }else{
            return  false;
        }
    }
    
 	/**
 	 * 订单查询
 	 * @param int $id 订单号ID   
 	 * @return mixed|multitype:
 	 */
    public function orderget($id){  
    	
    	$param=[];
    	$param['id']=$id; //订单号ID    	
    	$param['sourceId']=1;//来源ID 电商1
    	
    	$order = new Order();    	
    	$result=json_decode($order->Orderget($param),1);
    
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态    	
    		$message=json_decode($result['msg'],1);      		
    		$data=$message;
    		if(is_array($data)){
    			return $data;//true\false
    		}else{
    			return [];
    		}
    	}
    }
    
    /**
     * 订单退款
     * @param string $orderId ERP订单号
     */
    public function orderrefund($orderId){
    
    	$param=[];
    	$param['orderId']=$orderId; //订单号    
    
    	$order = new Order();
   		$result=$order->Orderrefund($param);
    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);    		
    		$data=$message;
    		if($data){
    			return $data; //true\false
    		}else{
    			return [];
    		}
    	}   	
    	
    	
    }
    
    /**
     * 取消订单
     * @param string $orderId ERP订单号 
     */
    public function ordercancell($orderId,$refund_sn=""){
        //$vr_refundinfo = Model("vr_refund")->getRefundInfo(array("erp_order_id" => $orderId),"refund_amount",true);
        $vr_refundinfo = Model("vr_order")->getOrderCodeInfo(array("erp_order_id" => $orderId),"pay_price",true);
        $refund_amount = $vr_refundinfo['pay_price']*100;
    	$param=[];
    	$param['orderId'] = $orderId; //订单号
//    	$param['serialnumber'] = $refund_sn;   //流水号,建议用UUID
//    	$param['returnAmount'] =  $refund_amount;   //退款金额

        $order = new Order();
//    	$result=$order->Ordercancell($param);
    	$result=$order->orderCancelReturnRelease($param);
        RedisManager::lPush('erp_vr_ordercancell', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => $param, 'result' => $result], JSON_UNESCAPED_UNICODE));
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		return true;
    	}else{
    		return false;
    	}
    }     
   
    
    /**
     * 申请退货退款
     * @param string $orderId ERP订单号
     */
    public function orderrefunding($orderId,$refund_sn){
    
    	$param=[];
    	$param['orderId']=$orderId; //订单号
    	$param['serialNumber']=$refund_sn;   //流水号,建议用UUID
    	 
    	$order = new Order();
    	$result=$order->Orderrefunding($param);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		
    		return true;
    	}else{
    		
    		return false;
    	}
    }
    /**
     * 确认退款--电商回调
     * @param string $orderId ERP订单号
     */
    public function orderconfirmrefund($orderId,$refund_sn,$returnAmount=0){
    
    	$param=[];
    	$param['orderId']=$orderId; //订单号
    	$param['serialnumber']=$refund_sn;   //流水号,建议用UUID
    	$param['returnAmount']= $returnAmount*100;   //退款金额

    	$order = new Order();
    	$result=$order->Orderconfirmrefund($param);

    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		return true;
    	}else{
                         return false;
        }
    }
    /**
     * 虚拟商品 批量确认退款--电商回调
     * @param string $orderId ERP订单号
     */
    public function batchorderconfirmrefund($param){
        $order = new Order();
        $result=$order->batchOrderconfirmrefund($param);
        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
            return true;
        }else{
            return false;
        }
    }
    
    /**
     * 批量过期退款
     */
    public function batchAutoRefund($param) {
        $order = new Order();
        $result = $order->batchAutoRefund($param);
        wkcache("batchAutoRefund_result_" . $param['orderid'], $result, 7200);
        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return $result['msg'];
        } else {
            return false;
        }
    }

    
    /**
     * 驳回退款--电商回调
     * @param string $orderId ERP订单号
     */
    public function orderrejectrefund($orderId,$refund_sn){
    
    	$param=[];
    	$param['orderId']=$orderId; //订单号
    	$param['serialnumber']=$refund_sn;   //流水号,建议用UUID

    	$order = new Order();
    	$result=$order->Orderrejectrefund($param);
           
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		return true;
    	}else{
    	    return false;
    	}
    }
    /**
     * 虚拟商品 批量驳回退款--电商回调
     * @param array $param 订单参数
     */
    public function batchOrderRejectRefund($param = array()) {
        $order = new Order();
        $result = $order->batchOrderrejectrefund($param);
        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
            return true;
        }else{
            return false;
        }

    }
    	
    /**
     * 批量推送虚拟订单
     */
    public function  syncpushallvrorder(){    	
    	
    	//核销码未使用没有ERP订单号的列表
    	$condition=array();
    	$condition['vr_order_code.vr_state']=array('elt',0);      //未使用
    	$condition['vr_order_code.refund_lock']=array('elt',0);   //默认
    	$condition['vr_order_code.erp_order_id']=array('eq',0);  //没有erp订单号
    	$condition['vr_order.erp_order_ids']=array('eq',0);      //没有erp订单号
    	 
    	$model = Model();
    	$field = '*';
    	$on = 'vr_order_code.order_id=vr_order.order_id';
    	 
    	$model->table('vr_order_code,vr_order')->field($field);
    	 
    	$goods_list=$model->join('left')->on($on)->where($condition)->group("vr_order_code.order_id")->select();
    	
    	
    	 
    	foreach ($goods_list as $v){
    		$order_id=$v['order_id'];
    		$vr_goodsinfo=$v;
    	
    		$result=$this->syncOrderAdd($order_id,$vr_goodsinfo);
    	
    		if($result){    			
    			$vr_goodsinfo['erp_order_ids']=$result;
    			$this->syncOrderpay($order_id,$vr_goodsinfo);    
    		}
    	
    	}
    	return callback(true, '同步完成', 'order');
    	
    }
    
    //同步添加虚拟订单
    public function syncOrderAdd($order_id,$vr_goodsinfo=array()){
    	
    	
    	$model_v_order=Model("vr_order");    	    	
    	//$vr_goodsinfo=$model_v_order->getOrderInfo(array("order_id"=>$order_id));
    	//判断是否存在
    	if($vr_goodsinfo){	    		
    		
    		//获取ERP用户ID
    		$member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
    		$member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone'];
    		//登录ERP
    		$member_logic=Logic("erp_member");
    		$member_id=$member_logic->loginERP($member_mobile);
    		    		
    		if(!$member_id){
    			return  false;
    		}

    		$ginfo=Model('goods')->getGoodsInfo(array('goods_id'=>$vr_goodsinfo['goods_id']),'*');//,false    	
    		
    		//如果没有货号
    		if(!$ginfo['goods_serial']){
    			return false;
    		}
    		$orderadd=array();
    		for ($i=1;$i<=$vr_goodsinfo['goods_num'];$i++){
    			
    			$erp_order_id=$vr_goodsinfo['order_sn']."_v".$i;
    			    			
    			$param=[];
    			//$param['petid']=''; //主键
    			//$param['cancelltime']=''; //取消时间
    			//$param['paytime']=time(); //支付时间
    			//$param['refundtime']=''; //退款时间
    			//$param['platfrom']=''; //平台名称
    			 
    			$param['orderid']=$erp_order_id; //主键
    			$param['ordertype'] = $vr_goodsinfo['ordertype'] ? $vr_goodsinfo['ordertype'] : 4; //1 咨询订单 ; 2 门店订单 ; 3 预约订单;  4 电商订单;  5 积分订单
    			/* $param['memberid']='fa88ea22483424b2'; //用户编号
    			 if(isset($_GET['memberid'])){
    			$param['memberid']=$_GET['memberid'];
    			} */
    			//$param['orderstate']=2; //订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
    			//$param['payway']=3; //支付方式(1-现金，2.银联卡,3-微信,4-支付宝,5-积分等)
    			 
    			$param['orderdetail']=$vr_goodsinfo['goods_name']; //订单明细，暂用 json存数据
    			$param['platformid']=1; //平台ID 电商送 1
    			$param['belonghospitalid']=0; //订单所属分院  默认送0
    			//$param['createtime']=''; //创建时间
    			//$param['lasttime']=''; //最后操作时间
    			
    			$goods_arr=array();
    			    			    			
    			$goods_arr[]=array(
    					//'id'=>'', //商品明细订单id
    					//'orderid'=>'1000000000003701', //订单编号(guid)
    					'goodsid'=>trim($ginfo['goods_serial']), //商品编号
    					'goodsimage'=>cthumb($ginfo['goods_image'], 360, $ginfo['store_id']),
    					'barcode'=>$ginfo['goods_barcode'], //条形码
    					'name'=>$vr_goodsinfo['goods_name'], //商品名称
    					'univalence'=>$ginfo['goods_price'], //商品单价
    					'sellprice'=>$vr_goodsinfo['goods_price'], //商品售价 //$vr_goodsinfo['order_amount']
    					'quantity'=>1, //商品数量 $vr_goodsinfo['goods_num']
    					'unit'=>1, //商品单位
    					//'applyhospitalid'=>'', //商品适用商品(默认值0，所有分院都可以适用)
    					'chargeoff'=>2, //订单商品核销状态： 1-不用核销 2-需要核销 3-已核销
    					//'chargeoffobject'=>'', //商品核销对象 – 分院编号
    					//'chargeoffobjectname'=>'', //商品核销对象 – 分院名称
    					//'lasttime'=>'', //最后操作时间
    					//'createtime'=>'', //创建时间
    					//'chargeofftime'=>'', //核销时间
    					//'chargeoffhospitalid'=>'' //核销对象-分院编号
    			    	
    			);
    			
    			$param['goods']=$goods_arr;
    			
    			$push=$this->orderadd($param);  
    			
    			if($push){
    				array_push($orderadd, $erp_order_id);    				
    				
    			}else{
    				//异常订单处理 
		    		$this->_error_log($order_id, $vr_goodsinfo, "添加ERP订单异常失败", "syns_orderadd_error_list");
    			}
    		}
    		
    	
    		//如果成功 添加订单 更新状态
    		if(!empty($orderadd)){   
    			$updata=array();
    			$updata['erp_status']   = 1;
    			$updata['erp_order_status']   = 1; //ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
    			$updata['erp_order_ids']   = serialize($orderadd);
    			$updata['erp_mobile']   = $member_mobile;
    			$updata['erp_time']=time();    			    			  		
    				    			
    			$model_v_order->editOrder($updata,array('order_id'=>$order_id));    
    								
    			return serialize($orderadd);
    		}
    	}
    }

    //同步添加虚拟订单 新接口 批量添加订单
    public function syncOrderBatchAdd($order_id,$vr_goodsinfo=array()){
        $model_v_order=Model("vr_order");
        //判断是否存在
        if($vr_goodsinfo){
            //获取ERP用户ID
            $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
            $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone'];
            //登录ERP
            $member_logic=Logic("erp_member");
            $member_id=$member_logic->loginERP($member_mobile);
            if(!$member_id){
                return  false;
            }
            $ginfo=Model('goods')->getGoodsInfo(array('goods_id'=>$vr_goodsinfo['goods_id']),'*');//,false
            //如果没有货号
            if(!$ginfo['goods_serial']){
                return false;
            }
            $order_amount = $vr_goodsinfo['order_amount'];
            $univalence = $ginfo['goods_price'];
            $goods_price = $vr_goodsinfo['goods_price'];
            $model_gift_order = Model('gift_order');
            $gift_order_info = $model_gift_order->getOrderInfo(['order_id_from'=>$order_id],'order_id,order_state,goods_price,payment_time');
            if (!empty($gift_order_info) && is_array($gift_order_info)) {
                $order_amount = $vr_goodsinfo['order_amount'] - $gift_order_info['goods_price'];
                /*$toubao_starttime = date('Y-m-d',($vr_goodsinfo['payment_time'] + 1*24*60*60));
                $toubao_starttime = strtotime($toubao_starttime);
                $toubao_endtime = $toubao_starttime + 7*24*60*60;
                $now_time = time();
                if ($toubao_endtime < $now_time && $gift_order_info['order_state'] == ORDER_STATE_SUCCESS) {
                    $univalence = $ginfo['goods_price'] - $gift_order_info['goods_price'];
                    $goods_price = $vr_goodsinfo['goods_price'] - $gift_order_info['goods_price'];
                }*/
                $univalence = $ginfo['goods_price'] - $gift_order_info['goods_price'];
                $goods_price = $vr_goodsinfo['goods_price'] - $gift_order_info['goods_price'];
            }
            $univalence = ncPriceFormat($univalence);
            $goods_price = ncPriceFormat($goods_price);
            $order_amount = ncPriceFormat($order_amount);
            $orderadd=array();
            $erp_order_data = [];
            $erp_order_data['masterorderid'] = $vr_goodsinfo['order_sn'];
            $goods_arr = array(
                //'id'=>'', //商品明细订单id
                //'orderid'=>'1000000000003701', //订单编号(guid)
                'skuid'=> $vr_goodsinfo['goods_id'], //电商商品skuid
                'goodsid' => trim($ginfo['goods_serial']), //商品编号
                'goodsimage' => cthumb($ginfo['goods_image'], 360, $ginfo['store_id']),
                'barcode' => $ginfo['goods_barcode'], //条形码
                'name' => $vr_goodsinfo['goods_name'], //商品名称
                'univalence' => $univalence,//$ginfo['goods_price'], //商品单价
                'sellprice' => $goods_price,//$vr_goodsinfo['goods_price'], //商品售价 //$vr_goodsinfo['order_amount']
                'quantity' => $vr_goodsinfo['goods_num'], //商品数量
                'unit' => 1, //商品单位
                //'applyhospitalid'=>'', //商品适用商品(默认值0，所有分院都可以适用)
                'chargeoff' => 2, //订单商品核销状态： 1-不用核销 2-需要核销 3-已核销
                'IsNeedPush' => 1,// 默认1，0赠品
                'expiredate' => date('Y-m-d H:i:s',$vr_goodsinfo['vr_indate']), //过期时间
                //'chargeoffobject'=>'', //商品核销对象 – 分院编号
                //'chargeoffobjectname'=>'', //商品核销对象 – 分院名称
                //'lasttime'=>'', //最后操作时间
                //'createtime'=>'', //创建时间
                //'chargeofftime'=>'', //核销时间
                //'chargeoffhospitalid'=>'' //核销对象-分院编号

            );
            $erp_order_data['goods'] = $goods_arr;
            $erp_order_data['paytime'] = date('Y-m-d H:i:s',$vr_goodsinfo['payment_time']);
            $erp_order_data['payway'] = array(
                'payway' => 1,
                "amount" => $order_amount*100 //integer支付 如为 货币 单位 是分
            );
            $erp_code_data = [];
            $where = array();
            $where['order_id'] = $order_id;
            //$where['vr_state'] = 0;
            $where['refund_lock'] = 0;
            $code_list = $model_v_order->getCodeList($where,"vr_code,rec_id",2000,"rec_id asc");
            $model_v_order_code = Model('vr_order_code');
            try {
                $model_v_order_code->beginTransaction();
                if (is_array($code_list) && !empty($code_list)) {
                    foreach ($code_list as $key => $value) {
                        $number = $key + 1;
                        $updata=array();
                        $updata['erp_order_status']    =2;
                        $updata['erp_status']   = 1;
                        $updata['erp_time'] = time();
                        $updata['erp_order_id'] = $vr_goodsinfo['order_sn']."_v".$number;
                        $updata['erp_mobile'] = $member_mobile;
                        $model_v_order_code->editOrder($updata,array('rec_id'=>$value['rec_id']));
                        $erp_code_data[$number] = $value['vr_code'];
                        array_push($orderadd, $updata['erp_order_id']);
                    }
                }
                $erp_order_data['childorderinfo'] = $erp_code_data;
                $push = $this->order_batch_add($erp_order_data);
                if($push){
                    //如果成功 添加订单 更新状态
                    if(!empty($orderadd)){
                        $updata=array();
                        $updata['erp_status']   = 1;
                        $updata['erp_order_status']   = 2; //ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
                        $updata['erp_order_ids']   = serialize($orderadd);
                        $updata['erp_mobile']   = $member_mobile;
                        $updata['erp_time'] = time();
                        $model_v_order->editOrder($updata,array('order_id'=>$order_id));
                        $model_v_order_code->commit();
                        return serialize($orderadd);
                    }
                }else{
                    $batch_error_list=rkcache("syns_batch_erp_addorder_error_list");
                    if ($batch_error_list[$order_id]['times']) {
                        $batch_error_list[$order_id]['times'] = $batch_error_list[$order_id]['times']+1;
                        wkcache("syns_batch_erp_addorder_error_list", $batch_error_list);
                    }else{
                        //异常订单处理
                        $this->_error_log($order_id, $vr_goodsinfo, "批量添加ERP订单异常失败", "syns_batch_erp_addorder_error_list");
                    }
                    $model_v_order_code->rollback();
                }

            }catch (Exception $e) {
                $model_v_order_code->rollback();
                $this->_error_log($order_id, $vr_goodsinfo, "批量添加ERP订单异常失败", "syns_batch_erp_addorder_error_list");
            }
        }
    }

    //同步添加虚拟赠品订单 批量新增
    public function syncGiftOrderBatchAdd($order_id,$vr_goodsinfo=array()){
        $model_gift_order = Model("gift_order");
        //判断是否存在
        if($vr_goodsinfo){
            //获取ERP用户ID
            $member_model = Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'], "member_mobile");
            $member_mobile = $member_model['member_mobile'] ? $member_model['member_mobile'] : $vr_goodsinfo['buyer_phone'];
            //登录ERP
            $member_logic = Logic("erp_member");
            $member_id = $member_logic->loginERP($member_mobile);
            if (!$member_id) {
                return false;
            }
            $ginfo = Model('goods')->getGoodsInfo(array('goods_id' => $vr_goodsinfo['goods_id']), '*');//,false
            //如果没有货号
            if (!$ginfo['goods_serial']) {
                return false;
            }
            $orderadd = array();
            $erp_order_data = [];
            $erp_order_data['masterorderid'] = $vr_goodsinfo['order_sn'];
            $goods_arr = array(
                //'id'=>'', //商品明细订单id
                //'orderid'=>'1000000000003701', //订单编号(guid)
                'skuid'=> $vr_goodsinfo['goods_id'], //电商商品skuid
                'goodsid' => trim($ginfo['goods_serial']), //商品编号
                'goodsimage' => cthumb($ginfo['goods_image'], 360, $ginfo['store_id']),
                'barcode' => $ginfo['goods_barcode'], //条形码
                'name' => $vr_goodsinfo['goods_name'], //商品名称
                'univalence' => $ginfo['goods_price'], //商品单价
                'sellprice' => $vr_goodsinfo['goods_price'], //商品售价 //$vr_goodsinfo['order_amount']
                'quantity' => $vr_goodsinfo['goods_num'], //商品数量
                'unit' => 1, //商品单位
                //'applyhospitalid'=>'', //商品适用商品(默认值0，所有分院都可以适用)
                'chargeoff' => 2, //订单商品核销状态： 1-不用核销 2-需要核销 3-已核销
                'IsNeedPush' => 0,// 默认1，0赠品
                'expiredate' => date('Y-m-d H:i:s',$vr_goodsinfo['vr_indate']), //过期时间
                //'chargeoffobject'=>'', //商品核销对象 – 分院编号
                //'chargeoffobjectname'=>'', //商品核销对象 – 分院名称
                //'lasttime'=>'', //最后操作时间
                //'createtime'=>'', //创建时间
                //'chargeofftime'=>'', //核销时间
                //'chargeoffhospitalid'=>'' //核销对象-分院编号

            );
            $erp_order_data['goods'] = $goods_arr;
            $erp_order_data['paytime'] = date('Y-m-d H:i:s',$vr_goodsinfo['payment_time']);
            $erp_order_data['payway'] = array(
                'payway' => 1,
                "amount" => $vr_goodsinfo['order_amount']*100 //integer支付 如为 货币 单位 是分
            );
            $erp_code_data = [];
            $where = array();
            $where['order_id'] = $order_id;
            //$where['vr_state'] = 0;
            $where['refund_lock'] = 0;
            $code_list = $model_gift_order->getCodeList($where,"vr_code,rec_id",2000,"rec_id asc");
            $model_gift_order_code = Model('gift_order_code');
            try {
                $model_gift_order_code->beginTransaction();
                if (is_array($code_list) && !empty($code_list)) {
                    foreach ($code_list as $key => $value) {
                        $number = $key + 1;
                        $updata=array();
                        $updata['erp_order_status'] = 2;
                        $updata['erp_status']   = 1;
                        $updata['erp_time'] = time();
                        $updata['erp_order_id'] = $vr_goodsinfo['order_sn']."_v".$number;
                        $updata['erp_mobile'] = $member_mobile;
                        $model_gift_order_code->editOrder($updata,array('rec_id'=>$value['rec_id']));
                        $erp_code_data[$number] = $value['vr_code'];
                        array_push($orderadd, $updata['erp_order_id']);
                    }
                }
                $erp_order_data['childorderinfo'] = $erp_code_data;//wkcache("gift_order_".$order_id,$erp_order_data,2*60*60);die;
                $push = $this->order_batch_add($erp_order_data);
                if($push){
                    //如果成功 添加订单 更新状态
                    $model_gift_order->editOrder(['erp_pushstate'=>1],['order_id'=>$order_id]);
                    if(!empty($orderadd)){
                        $model_gift_order_code->commit();
                        return serialize($orderadd);
                    }
                }else{
                    $batch_error_list=rkcache("syns_batch_erp_add_gift_order_error_list");
                    if ($batch_error_list[$order_id]['times']) {
                        $batch_error_list[$order_id]['times'] = $batch_error_list[$order_id]['times']+1;
                        wkcache("syns_batch_erp_add_gift_order_error_list", $batch_error_list);
                    }else{
                        //异常订单处理
                        $this->_error_log($order_id, $vr_goodsinfo, "批量添加ERP赠品订单异常失败", "syns_batch_erp_add_gift_order_error_list01");
                    }
                    $model_gift_order_code->rollback();
                }

            }catch (Exception $e) {
                $model_gift_order_code->rollback();
                $this->_error_log($order_id, $vr_goodsinfo, "批量添加ERP赠品订单异常失败", "syns_batch_erp_add_gift_order_error_list02");
            }
        }
    }

    //同步取消虚拟订单
    public function syncOrderCancell($order_id){
    	    	
    	$model_v_order = Model("vr_order");
    	$vr_goodsinfo = $model_v_order->getOrderInfo(array("order_id"=>$order_id),"*",true);
    	
    	//获取ERP用户ID
    	/* $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");    		
    	$member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone']; */
    	
    	$member_mobile=$vr_goodsinfo['erp_mobile'];
    	
    	//登录ERP
    	$member_logic=Logic("erp_member");
    	$member_id=$member_logic->loginERP($member_mobile);
    	if(!$member_id){
    		return  false;
    	}
        $cancell=$this->noPayCancelOrder($vr_goodsinfo['order_sn']);
        //如果成功 取消订单 更新状态
        if(!empty($cancell)){
            $updata=array();
            $updata['erp_order_status']    =4;
            $updata['erp_status']   = 1;
            $updata['erp_time']=time();

            $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
            //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
            return true;
        }else{
            //异常订单处理
            $this->_error_log($order_id, $order_id, "取消ERP订单异常失败", "syns_orderCancell_error_list");
        }
    	//判断是否存在
//    	if($vr_goodsinfo){
//    		$arr=unserialize($vr_goodsinfo['erp_order_ids']);
//
//    		if(empty($arr)){
//    			return  false;
//    		}
//    		foreach ($arr as $v){
//    			//取消订单
//    			$cancell=$this->ordercancell($v,$v);
//
//    			//如果成功 取消订单 更新状态
//    			if(!empty($cancell)){
//    				$updata=array();
//    				$updata['erp_order_status']    =4;
//    				$updata['erp_status']   = 1;
//    				$updata['erp_time']=time();
//
//    				$model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
//    				//Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
//    				return true;
//    			}else{
//    				//异常订单处理
//    				$this->_error_log($order_id, $order_id, "取消ERP订单异常失败", "syns_orderCancell_error_list");
//    			}
//    		}
//    	}
    }


    /**
     * 未支付 取消订单释放库存
     * @param $order_sn 订单编号
     * @return bool
     */
    public function noPayCancelOrder($order_sn)
    {
        $param            = [];
        $param['orderId'] = $order_sn;
        $order            = new Order();
        $result           = $order->orderCancelReturnRelease($param);
        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            return true;
        } else {
            RedisManager::lPush('erp_orderRealCancell_KucunErr', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => $param, 'result' => $result], JSON_UNESCAPED_UNICODE));
            return false;
        }
    }
       

    //同步退款虚拟订单
    //作废 2021-4-14 by zyw
//    public function syncOrderRefund($refund){
//
//        $model_v_order=Model("vr_order");
//        $vr_goodsinfo=$model_v_order->getOrderInfo(array("order_id"=>$refund['order_id']),"*",true);
//
//        if(!$vr_goodsinfo){
//            return false;
//        }
//
//        //获取ERP用户ID
//        /* $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
//        $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone']; */
//
//        $member_mobile = $vr_goodsinfo['erp_mobile'] ? $vr_goodsinfo['erp_mobile'] : $vr_goodsinfo['buyer_phone'];
//        //登录ERP
//        $member_logic=Logic("erp_member");
//        $member_id=$member_logic->loginERP($member_mobile);
//        if(!$member_id){
//            return  false;
//        }
//
//        $refund_lock = '2';//退款锁定状态:0为正常,1为锁定,2为同意
//        $code_array = explode(',', $refund['code_sn']);
//
//        $condition = array();
//        //$condition['refund_lock']=$refund_lock;
//        $condition['vr_code']=array('in',$code_array);
//
//        $vr_order_list=$model_v_order->getCodeList($condition);
//
//        //虚拟码列表
//        foreach ($vr_order_list as $k=>$v){
//            if(!$v['erp_order_id']){
//                continue;
//            }
//            //$erp_refund_id=$refund['erp_refund_id']?$refund['erp_refund_id']:($refund['refund_sn'] ."_v".$v['rec_id']);
//            $erp_refund_id=$v['erp_refund_id']?$v['erp_refund_id']:$refund['erp_refund_id'];
//            //同意退款，ERP发起回调 //审核状态:1为待审核,2为同意,3为不同意
//            if($refund['admin_state'] == '2'){// && $refund['erp_order_id']!=0
//
//                $orderrefund=$this->orderconfirmrefund($v['erp_order_id'],$erp_refund_id,$v['pay_price']);//$refund['refund_sn'] ."_v".$v['rec_id']
//                if(!empty($orderrefund)){
//                    $updata=array();
//                    $updata['erp_order_status']    =4;
//                    $updata['erp_status']   = 1;
//                    $updata['erp_time']=time();
//                    $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
//                    //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
//                   // return true;
//                    continue;
//                }else{
//                    //异常订单处理
//                    $this->_error_log($refund['order_id'], $refund, "退款ERP订单异常失败001", "syns_orderRefund_error_list");
//                   // return false;
//                    continue;
//                }
//
//            }
//            //不同意退款，ERP发起驳回
//            if($refund['admin_state'] == '3'){// && $refund['erp_order_id']!=0
//                $orderrefund=$this->orderrejectrefund($v['erp_order_id'],$erp_refund_id);
//                if(!empty($orderrefund)){
//                    $updata=array();
//                    $updata['erp_order_status']    =5;
//                    $updata['erp_status']   = 1;
//                    $updata['erp_time']=time();
//                    $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
//                    //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
//                     //return true;
//                     continue;
//                }else{
//                    //异常订单处理
//                    $this->_error_log($refund['order_id'], $refund, "退款ERP订单异常失败002", "syns_orderRefund_error_list");
//                   // return false;
//                    continue;
//                }
//
//            }
//            //如果不同意则返回
//            if($refund['admin_state'] != '2' ){
//                return false;
//                break;
//            }
//            //退款订单
//            $orderrefund=$this->ordercancell($v['erp_order_id'],$erp_refund_id);
//
//            if(!empty($orderrefund)){
//                $updata=array();
//                $updata['erp_order_status']    =3;
//                $updata['erp_status']   = 1;
//                $updata['erp_time']=time();
//
//                $model_v_order_code=Model('vr_order_code');
//                $model_v_order_code->editOrder($updata,array('rec_id'=>$v['rec_id']));
//                //Model('vr_order_code')->where(array('rec_id'=>$v['rec_id']))->update($updata);
//            }else{
//                //异常订单处理
//                $this->_error_log($refund['order_id'], $refund, "退款ERP订单异常失败003", "syns_orderRefund_error_list");
//            }
//    }
//        //如果成功 退款总订单 更新状态
//        if(!empty($orderrefund)){
//            $updata=array();
//            $updata['erp_order_status']    =3;
//            $updata['erp_status']   = 1;
//            $updata['erp_time']=time();
//            $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
//            //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
//            return true;
//        }
//
//    }
    
    //同步过期退款虚拟订单  已作废 2021-4-14 by zyw
//    public function syncbatchAutoRefund($refund){
//
//        $model_v_order = Model("vr_order");
//        $vr_order_info = $model_v_order->getOrderInfo(array("order_id" => $refund['order_id']), "erp_mobile,goods_id,buyer_phone", true);
//
//        if (!$vr_order_info) {
//            return false;
//        }
//
//        //获取ERP用户ID
//        /* $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
//         $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone']; */
//
//        $member_mobile = $vr_order_info['erp_mobile'] ? $vr_order_info['erp_mobile'] : $vr_order_info['buyer_phone'];
//        //登录ERP
//        $member_logic = Logic("erp_member");
//        $member_id = $member_logic->loginERP($member_mobile);
//        wkcache("loginERP_" . $member_mobile, $member_id, 7200);
//        if (!$member_id) {
//            return false;
//        }
//
//        $refund_lock = '2';//退款锁定状态:0为正常,1为锁定,2为同意
//        $code_array = explode(',', $refund['code_sn']);
//
//        $condition = array();
//        //$condition['refund_lock']=$refund_lock;
//        $condition['vr_code'] = array('in', $code_array);
//
//	    $vr_order_list=$model_v_order->getCodeList($condition);
//
//   		$post_data=[];
//            foreach ($vr_order_list as $k=>$v){
//                if(!$v['erp_order_id']){
//                    continue;
//                }
//                $arr=[];
//                $arr=explode("_v", $v['erp_order_id']);
//
//                $post_data["orderid"] = $arr[0];
//                $post_data["refundamount"] = $v['pay_price'] * 100;
//                $post_data["goodsid"] = $vr_order_info['goods_id'];
//                $post_data["goodsqty"] = 1;
//                $post_data['vids'][] = intval($arr[1]);
//            }
//        if (empty($post_data)) {
//            return true;
//        }
//
//        //退款订单
//        $orderrefund = $this->batchAutoRefund($post_data);
//        wkcache("batchAutoRefund_" . $refund['order_id'], $orderrefund, 7200);
//        if ($orderrefund) {
//            return true;
//        } else {
//            //异常订单处理
//            $this->_error_log($refund['order_id'], $refund, "过期退款ERP订单异常失败", "syns_batchAutoRefund_error_list");
//            return false;
//        }
//
//    }

    //同步批量处理 退款虚拟订单
//    public function syncBatchOrderRefund($refund)
//    {
//    	//如果是过期自动退款跳过
//    	if($refund['auto_status']){
//    		return true;
//    	}
//        $model_v_order = Model("vr_order");
//        $vr_goodsinfo = $model_v_order->getOrderInfo(array("order_id" => $refund['order_id']), "order_id,erp_mobile,buyer_id,buyer_phone", true);
//        if(!$vr_goodsinfo){
//            return false;
//        }
//        //获取ERP用户ID
//        /* $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
//        $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone']; */
//        $member_mobile = $vr_goodsinfo['erp_mobile'] ? $vr_goodsinfo['erp_mobile'] : $vr_goodsinfo['buyer_phone'];
//        //登录ERP
//        $member_logic=Logic("erp_member");
//        $member_id=$member_logic->loginERP($member_mobile);
//        if(!$member_id){
//        	return  false;
//        }
//        $refund_lock = '2';//退款锁定状态:0为正常,1为锁定,2为同意
//        $code_array = explode(',', $refund['code_sn']);
//
//        $condition = array();
//        //$condition['refund_lock']=$refund_lock;
//        $condition['vr_code']=array('in',$code_array);
//        $vr_order_list=$model_v_order->getCodeList($condition,"rec_id,erp_order_id,erp_refund_id,pay_price",1000,"rec_id asc");
//
//        $post_data = [];
//        //虚拟码列表
//        foreach ($vr_order_list as $k=>$v){
//        	if(!$v['erp_order_id']){
//        		continue;
//        	}
//        	$arr=explode("_v", $v['erp_order_id']);
//        	$post_data['orderid']=$arr[0];//订单号
//        	$post_data['returnAmount']=strval($v['pay_price']*100);//退款金额
//        	$post_data['list'][$k]['id']=intval($arr[1]);
//        	$post_data['list'][$k]['serialNumber']=$v['erp_refund_id'] ? $v['erp_refund_id'] :$refund['erp_refund_id'];//流水号
//        }
//
//        //同意退款，ERP发起回调 //审核状态:1为待审核,2为同意,3为不同意
//        if($refund['admin_state'] == '2'){// && $refund['erp_order_id']!=0
//            $orderrefund = $this->batchorderconfirmrefund($post_data);
//            if($orderrefund){
//                $updata=array();
//                $updata['erp_order_status'] = 3;
//                $updata['erp_status']   = 1;
//                $updata['erp_time']=time();
//                $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
//                //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
//                // return true;
//            }else{
//                //异常订单处理
//                $this->_error_log($refund['order_id'], $post_data, "批量退款ERP订单异常失败001", "syns_batch_orderRefund_agree_error_list");
//                // return false;
//
//            }
//        }
//        //不同意退款，ERP发起驳回
//        if($refund['admin_state'] == '3'){// && $refund['erp_order_id']!=0
//            $orderrefund = $this->batchOrderRejectRefund($post_data);
//            if($orderrefund){
//                $updata=array();
//                $updata['erp_order_status'] = 4;
//                $updata['erp_status'] = 1;
//                $updata['erp_time'] = time();
//                $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
//                //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
//                //return true;
//            }else{
//                //异常订单处理
//                $this->_error_log($refund['order_id'], $post_data, "批量退款ERP订单异常失败002", "syns_batch_orderRefund_refuse_error_list");
//                // return false;
//            }
//
//        }
//        //如果不同意则返回
//        if($refund['admin_state'] != '2' ){
//            return false;
//
//        }
//        //退款订单
//        /*$orderrefund=$this->ordercancell($v['erp_order_id'],$erp_refund_id);
//        if(!empty($orderrefund)){
//            $updata=array();
//            $updata['erp_order_status']    =3;
//            $updata['erp_status']   = 1;
//            $updata['erp_time']=time();
//
//            $model_v_order_code=Model('vr_order_code');
//            $model_v_order_code->editOrder($updata,array('rec_id'=>$v['rec_id']));
//            //Model('vr_order_code')->where(array('rec_id'=>$v['rec_id']))->update($updata);
//        }else{
//            //异常订单处理
//            $this->_error_log($refund['order_id'], $refund, "退款ERP订单异常失败003", "syns_orderRefund_error_list");
//        }
//        //如果成功 退款总订单 更新状态
//        if(!empty($orderrefund)){
//            $updata=array();
//            $updata['erp_order_status']    =3;
//            $updata['erp_status']   = 1;
//            $updata['erp_time']=time();
//            $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
//            //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
//            return true;
//        }*/
//
//    }

    //同步支付虚拟订单
    public function syncOrderpay($order_id,$update_order=array()){
    	$model_v_order=Model("vr_order");
    	$vr_goodsinfo=$model_v_order->getOrderInfo(array("order_id"=>$order_id),"*",true);
    	//判断是否存在
    	if($vr_goodsinfo) {
            //获取ERP用户ID
            /* $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
            $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone']; */
            $member_mobile = $vr_goodsinfo['erp_mobile'] ? $vr_goodsinfo['erp_mobile'] : $vr_goodsinfo['buyer_phone'];

            //登录ERP
            $member_logic = Logic("erp_member");
            $member_id = $member_logic->loginERP($member_mobile);
            if (!$member_id) {
                return false;
            }
            //ERP对应订单列表
            $arr = unserialize($vr_goodsinfo['erp_order_ids']);
            if (empty($arr)) {
    		    return false;
    		}

    		$vr_order_list=Model('vr_order_code')->where(array("order_id"=>$vr_goodsinfo['order_id']))->master(true)->select();

    		$ginfo=Model('goods')->getGoodsInfo(array('goods_id'=>$vr_goodsinfo['goods_id']),'goods_serial');
    		
    		//虚拟码列表
    		foreach ($vr_order_list as $k=>$v){
    			$updata=array();
    			$updata['erp_order_status']    =2;
    			$updata['erp_status']   = 1;
    			$updata['erp_time']=time();
    			$updata['erp_order_id']=$arr[$k]; 
    			
    			$updata['erp_mobile']=$member_mobile; //$member_model['member_mobile']
    			
    			$vr_goodsinfo["pay_price"] = $v['pay_price'];
    			$vr_goodsinfo["rec_id"] = $v['rec_id'];
    			$vr_goodsinfo["goods_serial"] = $ginfo['goods_serial'];
    			$vr_goodsinfo["vr_code"] = $v['vr_code'];
    			$vr_goodsinfo["vr_indate"] = date('Y/m/d H:i:s',$v['vr_indate']);
    			$vr_goodsinfo["ordertype"] = $update_order['ordertype'] ? $update_order['ordertype'] : 1;

    			//支付订单
    			$orderpay=$this->orderpay($arr[$k],$vr_goodsinfo);
    			
    			if(empty($orderpay)){    				    			
    				//异常订单处理    				
    				$this->_error_log($order_id, $update_order, "支付ERP订单异常失败", "syns_orderpay_error_list");    				
    			}
    			
    			$model_v_order_code=Model('vr_order_code');    			
    			$model_v_order_code->editOrder($updata,array('rec_id'=>$v['rec_id']));
    			//Model('vr_order_code')->where(array('rec_id'=>$v['rec_id']))->update($updata);
    		}
    		
    		//如果成功 支付订单 更新状态
    		if(!empty($orderpay)){
    			$updata=array();
    			$updata['erp_order_status']    =2;
    			$updata['erp_status']   = 1;
    			$updata['erp_time']=time();
    			$model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
    			//Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
    			return true;
    		}
    		
    	}
    }

    //同步支付虚拟订单 批量支付
    public function syncOrderpayBatch($order_id,$update_order=array()){
        $model_v_order = Model("vr_order");
        $paydata_field = "order_id,order_sn,erp_mobile,erp_order_ids,goods_id,goods_price,buyer_phone";
        $vr_goodsinfo = $model_v_order->getOrderInfo(array("order_id" => $order_id), $paydata_field, true);
        //判断是否存在
        if($vr_goodsinfo){
            //获取ERP用户ID
            /* $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
            $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone']; */
            $member_mobile = $vr_goodsinfo['erp_mobile'] ? $vr_goodsinfo['erp_mobile'] : $vr_goodsinfo['buyer_phone'];
            //登录ERP
            $member_logic=Logic("erp_member");
            $member_id=$member_logic->loginERP($member_mobile);
            if(!$member_id){
            	return  false;
            }
            //ERP对应订单列表
            $arr=unserialize($vr_goodsinfo['erp_order_ids']);
            if(empty($arr)){
                return false;
            }

            $vr_order_list=Model('vr_order_code')->where(array("order_id"=>$vr_goodsinfo['order_id']))->master(true)->field('rec_id,vr_indate,pay_price,vr_code')->select();
            $ginfo = Model('goods')->getGoodsInfo(array('goods_id'=>$vr_goodsinfo['goods_id']),'goods_serial');

            //虚拟码列表
            $model_v_order_code = Model('vr_order_code');
            $pay_data = [];
            foreach ($vr_order_list as $k=>$v){
                $updata=array();
                $updata['erp_order_status']    =2;
                $updata['erp_status']   = 1;
                $updata['erp_time'] = time();
                $updata['erp_order_id'] = $arr[$k];
                $updata['erp_mobile'] = $member_mobile;

                $model_v_order_code->editOrder($updata,array('rec_id'=>$v['rec_id']));
                /*$vr_goodsinfo["orderId"] = $arr[$k];
                $vr_goodsinfo["pay_price"] = $v['pay_price'];
                $vr_goodsinfo["rec_id"] = $v['rec_id'];
                $vr_goodsinfo["goods_serial"] = $ginfo['goods_serial'];
                $vr_goodsinfo["vr_code"] = $v['vr_code'];
                $vr_goodsinfo["ordertype"] = $update_order['ordertype'] ? $update_order['ordertype'] : 1;*/
                $vr_indate = date('Y/m/d H:i:s',$v['vr_indate']);
                $ordertype = $update_order['ordertype'] ? $update_order['ordertype'] : 1;
                $param=[];
                $param['orderId'] = $arr[$k];
                $param['serialnumber'] = $vr_goodsinfo['order_sn']."_v".$v['rec_id'];   //流水号,建议用UUID
                $param['realityamount'] = $ordertype ? 0 : $v['pay_price']*100;

                $payways_arr=array();
                $payways_arr[]=array(
                    "payway" => $ordertype , //支付方式(1-现金，2.积分,3-优惠券,4-线下优惠券等)
                    "amount" => $vr_goodsinfo['goods_price']*100 //integer支付 如为 货币 单位 是分
                    //"operationcode"=>  //string 操作码 积分支付 传 操作码 优惠券支付 传 优惠券ID
                );
                $param['payways']=$payways_arr;
                $extendinfo_arr=array(
                    "WrittenOffCode" => $ginfo['goods_serial']."-".$v['vr_code']."-".$vr_indate //商品id-核销码-过期时间
                );
                $param['extendinfo']=$extendinfo_arr;
                array_push($pay_data,$param);
            }
            //支付订单
            $orderpay=$this->orderpayBatch($pay_data);
            if(empty($orderpay)){
                //异常支付订单处理
                $this->_error_log($order_id, $update_order, "批量支付ERP订单异常失败", "syns_order_batch_pay_error_list");
            }
            //如果成功 支付订单 更新状态
            if(!empty($orderpay)){
                $updata=array();
                $updata['erp_order_status']    =2;
                $updata['erp_status']   = 1;
                $updata['erp_time']=time();
                $model_v_order->editOrder($updata,array('order_id'=>$vr_goodsinfo['order_id']));
                //Model('vr_order')->where(array('order_id'=>$vr_goodsinfo['order_id']))->update($updata);
                return true;
            }

        }
    }

    //erp 虚拟订单退款申请冻结
	public function syncApplyErpRefund($refund_id,$vr_code ="") {
        $model_v_order=Model("vr_order");
        $model_vr_fund = Model('vr_refund');
        $model_snapshot = Model('vr_order_snapshot');
        $field = "erp_mobile,erp_order_ids,order_id,goods_id,goods_price,buyer_phone";
        //如果传递的是数组，则是订单信息
        if(is_array($refund_id)&&!empty($refund_id)){
            $refund_info=$refund_id;
        }else{
            $refund_info = $model_vr_fund->getRefundInfo(['refund_id'=>$refund_id],"order_id,refund_amount,code_sn");
        } 
        $order_id = $refund_info['order_id'];
        //有赠品订单
        $gift_order = false;
        $model_gift_order = Model('gift_order');
        $gift_order_info = $model_gift_order->getOrderInfo(['order_id_from'=>$order_id],'order_id,goods_price,order_state,payment_time');
        if (is_array($gift_order_info) && !empty($gift_order_info)) {
            $gift_order = true;
            //$toubao_starttime = date('Y-m-d',($gift_order_info['payment_time'] + 1*24*60*60));
           // $toubao_starttime = strtotime($toubao_starttime);
            //$toubao_endtime = $toubao_starttime + 7*24*60*60;
            //$now_time = time();

        }

        $vr_order_info = $model_v_order->getOrderInfo(array("order_id"=>$order_id),$field,true);

        $good_info = Model('goods')->getGoodsInfo(array('goods_id'=>$vr_order_info['goods_id']),'goods_serial');
        $shot_order_info = $model_snapshot->getSnapshotInfo($order_id,"goods_attr");
        $order_goods_attr = unserialize($shot_order_info['goods_attr']);
        $shot_goods_serial = $order_goods_attr['货号'];
        $goods_serial = $shot_goods_serial ? $shot_goods_serial : $good_info['goods_serial'];
        //如果没有货号
        if(!$goods_serial || empty($goods_serial)){
            return true;
        }
        if (in_array($order_id,array(344515))) {
            return true;
        }
        $push_status=true;
        //判断是否存在
        if(!empty($vr_order_info) && is_array($vr_order_info)){
            //获取ERP用户ID
            /* $member_model=Model('member')->getMemberInfoByID($vr_goodsinfo['buyer_id'],"member_mobile");
            $member_mobile=$member_model['member_mobile']?$member_model['member_mobile']:$vr_goodsinfo['buyer_phone']; */
            $member_mobile = $vr_order_info['erp_mobile'] ? $vr_order_info['erp_mobile'] : $vr_order_info['buyer_phone'];
            //登录ERP
            $member_logic=Logic("erp_member");
            $member_id=$member_logic->loginERP($member_mobile);
            if(!$member_id){
            	return  false;
            }
            //ERP对应订单列表
            /* $arr=unserialize($vr_order_info['erp_order_ids']);
            if(empty($arr)){
                return false;
            } */
            $vr_code = explode(',', $refund_info['code_sn']);
            if (!empty($vr_code)) {
                $vr_order_list=Model('vr_order_code')->where(array("vr_code"=>array('in',$vr_code)))->field("rec_id,erp_order_id,pay_price")->master(true)->select();
            } else {
                $vr_order_list=Model('vr_order_code')->where(array("order_id"=>$vr_order_info['order_id']))->field("rec_id,erp_order_id,pay_price")->master(true)->select();
            }
            
            $model_v_order_code=Model('vr_order_code');
            
            $post_data=[];
            foreach ($vr_order_list as $k=>$v){
            	if(!$v['erp_order_id']){
            		continue;
            	}
            	$arr=[];
            	$arr=explode("_v", $v['erp_order_id']);
            	                     	
            	$post_data["orderid"] = $arr[0];
                if ($gift_order) {
                    $refund_amount = $v['pay_price'] - ncPriceFormat($gift_order_info['goods_price']);
                    $post_data["refundamount"] = $refund_amount*100;
                }else{
                    $post_data["refundamount"] = $v['pay_price']*100;
                }
                $post_data["goodsid"] = $vr_order_info['goods_id'];
                $post_data["goodsqty"] = 1;                
                $post_data['vids'][]=intval($arr[1]);
            }            
            
            try {
                $model_v_order_code->beginTransaction();
                $refundapply = $this->batchApplyRefund($post_data);
                if (!empty($refundapply)) {
                    $erp_data = json_decode($refundapply,true);
                    if (is_array($erp_data) && !empty($erp_data)) {
                        foreach($vr_order_list as $key=> $item) {
                            $model_v_order_code->editOrder(['erp_refund_id'=>$erp_data[$key]],array('rec_id'=>$item['rec_id']));
                        }
                        $push_status = true;
                        $model_v_order_code->commit();
                    }
                }else{
                    $this->_error_log($order_id, $post_data, "批量ERP退款申请提交失败", "syns_batch_apply_refund_error_list");
                    $push_status = false;
                }
            }catch (Exception $e) {
                $model_v_order_code->rollback();
                $this->_error_log($order_id, $post_data, "批量ERP退款申请提交失败", "syns_batch_apply_refund_error_list");
                $push_status = false;
            }
            //如果成功 支付订单 更新状态
            /*if(!empty($refundapply) && $push_status && !is_array($refund_id)){
                $model_vr_fund->editRefund(['refund_id'=>$refund_id],['erp_refund_id'=>$refundapply]);
                return true;
            }else{
                return $refundapply;
            } */

        }
        return $push_status;
    }

    /**
     * 异常订单记录/任务计划执行
     * @param int $order_id
     * @param array $info
     * @param string $msg
     * @param string $logname
     */
    public function _error_log($order_id,$info,$msg,$log_name){
    	//异常订单处理
    	$arr=[];
    	$arr["order_id"] = $order_id;
    	$arr["info"] = $info;
    	$arr['msg'] = $msg;
    	$arr['date_time'] = date("Y-m-d H:i:s",time());
    	
    	$push=rkcache($log_name);
    	if(!$push){
    		$push=[];
    	}
    	$push[$order_id]=$arr;
    	    	
    	wkcache($log_name, $push);
    }
    
    
    /*
     * 判断支付类型
    * @param   $type string
    * @return   int
    * */
    public function _getPayCode($type=""){   
    	
    	switch($type){
    		case 'wx_saoma':
    			$pay_code=3;
    			break;
    		case 'wx_jsapi':
    			$pay_code=3;
    			break;
    		case 'wxpay':
    			$pay_code=3;
    			break;
    		case 'ali_native':
    			$pay_code=4;
                break;
            case 'alipay':
                $pay_code = 4;
                break;
            default:
                $pay_code = 1;
        }

        return $pay_code;
    }

}
