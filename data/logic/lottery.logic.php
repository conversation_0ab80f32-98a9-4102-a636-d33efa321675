
<?php
/**
 * 购买行为
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
defined('InShopNC') or exit('Access Invalid!');

class lotteryLogic
{
    public function addLotteryNumber($member_info, $lottery_info, $invite_member_info = []){

        if(empty($invite_member_info)){
            return $this->addLotteryMember($member_info, $lottery_info);
        }else{
            $result = $this->addLotteryMember($member_info, $lottery_info, $invite_member_info);
            if($result['state']){

                $this->addLotteryInviteMember($member_info, $lottery_info, $invite_member_info);
            }
            return $result;

        }

    }

    /**
     * 系统赠送
     * @param $member_info
     * @param $lottery_info
     * @return mixed
     */
    public function addLotteryMember($member_info, $lottery_info){
        $result = ['state'=>false,'发放幸运码失败'];
        $lottery_model = Model('lottery_draw');
        //系统赠送
        $condition = [
            'lottery_id'=>$lottery_info['id'],
            'member_id' => $member_info['member_id'],
            'state'=>1
        ];
        $number_info = $lottery_model->getlotteryNumberInfo($condition,'*',true);
        if($number_info){
            $result['msg']='已赠送过1个幸运码了，快去分享获得更多幸运码吧！';
            return $result;
        }
        $lottery_count = $lottery_model->getlotteryNumberCount($condition);
        if($lottery_count >=10){
            $result['msg']='每期活动最多可获得10个幸运码';
            return $result;
        }
        $number = $this->getNumber($lottery_info['id'], $lottery_info['lottery_end_time']);
        $number_data = [
            'member_id'=>$member_info['member_id'],
            'lottery_id'=>$lottery_info['id'],
            'member_name'=>$member_info['member_truename']?$member_info['member_truename']:$member_info['member_name'],
            'add_time'=>time(),
            'lottery_number'=>$number
        ];

        $lottrey_result = $lottery_model->addlotteryNumber($number_data);//插入幸运码
        if($lottrey_result){
            $result['state'] = true;
            $result['number'] = $number;
        }
        return $result;
    }

    /**
     * 邀请人获取幸运码
     * @param $member_info
     * @param $lottery_info
     * @param $invite_member_info
     */
    public function addLotteryInviteMember($member_info, $lottery_info, $invite_member_info){
        $lottery_model = Model('lottery_draw');
        //系统赠送
        $condition = [
            'lottery_id'=>$lottery_info['id'],
            'member_id' => $member_info['member_id'],
            'invite_member_id' => $invite_member_info['member_id']
        ];
        //如果是自己邀请自己
        if($member_info['member_id'] == $invite_member_info['member_id']){
          return ;
        }
        $number_info = $lottery_model->getlotteryNumberInfo($condition);
        if ($number_info) {
            return ;
        }

        $condition = [
            'lottery_id'=>$lottery_info['id'],
            'member_id' => $invite_member_info['member_id'],
        ];
        $lottery_count = $lottery_model->getlotteryNumberCount($condition);
        if($lottery_count >=10){
            return ;
        }
        $invite_data = [
            'member_id' => $member_info['member_id'],
            'member_invite_id' => $invite_member_info['member_id'],
            'add_time' => time(),
            'lottery_id' => $lottery_info['id']
        ];
        $lottery_model->addlotteryMember($invite_data);//插入邀请记录

        $number = $this->getNumber($lottery_info['id'], $lottery_info['lottery_end_time']);
        $number_data = [
            'member_id'=>$invite_member_info['member_id'],
            'lottery_id'=>$lottery_info['id'],
            'member_name'=>$invite_member_info['member_truename']?$invite_member_info['member_truename']:$invite_member_info['member_name'],
            'add_time'=>time(),
            'lottery_number'=>$number
        ];
        if($invite_member_info){
            $number_data['state']=2;
            $number_data['invite_member_id']=$member_info['member_id'];
            $number_data['invite_member_name']=$member_info['member_truename']?$member_info['member_truename']:$member_info['member_name'];
        }
        $lottrey_result = $lottery_model->addlotteryNumber($number_data);//插入幸运码
        if($lottrey_result){
            $result['state'] = true;
            $result['number'] = $number;
        }else{
            error_log(print_r($invite_data,true),3,'lottery_invite.log');
        }
        return ;
    }

    /**
     * 获取码
     * @param $lottery_id
     * @param $time
     * @param string $key
     * @return string
     */
    public function getNumber($lottery_id, $time, $key='lottery_'){
        $lottery_model = Model('lottery_draw');
        $redis_logic = Logic('redis');
        $redis_key = $key.$lottery_id;
        $lottery_key = $redis_logic->getRedis()->get($redis_key);
        if(!$lottery_key){
            $condition = ['lottery_id'=>$lottery_id];
            $lottery_numbers_info = $lottery_model->table('lottery_numbers')->field('lottery_number')->where($condition)->order('id desc')->master(true)->find();
            if($lottery_numbers_info){
                $value = intval($lottery_numbers_info['lottery_number']);
                $redis_result = $redis_logic->incrby($redis_key,$value+1);
            }else{
                $redis_result = $redis_logic->incrby($redis_key,1);
            }
        }else{
            $redis_result = $redis_logic->incrby($redis_key,1);
        }
        /*if($redis_result ==1){
            $redis_logic->expire($redis_key, $time-time());
        }*/
        $number = str_pad($redis_result,6,"0",STR_PAD_LEFT);
        return $number;

    }

    /**
     * 修改key有效时间
     *
     */
    public function editLottreyKey($lottery_id, $time, $key='lottery_'){
        $redis_logic = Logic('redis');
        $redis_key = $key.$lottery_id;
        $lottery_key = $redis_logic->getRedis()->get($redis_key);
        if($lottery_key){
            $redis_logic->expire($redis_key, $time-time());
        }
        return ;
    }

    /**
     * 获取当前发放码
     *
     */
    public function getLotteryKey($lottery_id, $key='lottery_'){
        $redis_logic = Logic('redis');
        $redis_key = (string)$key.$lottery_id;

        $lottery_key = $redis_logic->getRedis()->get($redis_key);
        $number = '';
        if($lottery_key){
            $number = str_pad($lottery_key,6,"0",STR_PAD_LEFT);
        }
        return $number;
    }


}