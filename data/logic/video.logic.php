<?php
/**
 * 接口文档：http://127.0.0.1:11008/swagger/index.html
 * 获取快递公司列表 /mall/wxshop/delivery/companylist
 * 订单发货 /mall/wxshop/delivery/send
 * 订单确认收货 /mall/wxshop/delivery/recieve
 * 创建售后 /mall/wxshop/aftersale/add
 * 更新售后 /mall/wxshop/aftersale/update
 * @desc 视频号相关操作
 * <AUTHOR> zyw
 * @date 2021-8-25
 */

use Upet\Integrates\Redis\RedisManager as Redis;

class videoLogic
{
    public function getOpenId($order_info)
    {
        $member = Model('member')->getMemberInfo([
            'member_id' => $order_info['buyer_id']
        ],'weixin_mini_openid');

        if ($member && $member['weixin_mini_openid']) {
            return $member['weixin_mini_openid'];
        }

        Redis::lPush('video_openid_err', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => $order_info,'res'=>$open_id], JSON_UNESCAPED_UNICODE));
        return false;
    }

    /**
     * Notes:请求数据数据中心发货推送
     * @param $order_info
     * @param $express_info
     * @param finish_all_delivery 0: 未发完, 1:已发完
     * @return false|void
     * @url 请求参数说明:https://developers.weixin.qq.com/miniprogram/dev/framework/ministore/minishopopencomponent2/API/delivery/send.html
     * User: rocky
     * DateTime: 2021/8/27 14:04
     */
    public function send($order_info, $express_info)
    {
        $open_id = $this->getOpenId($order_info);
        if (!$open_id) {
            return false;
        }
        //获取父订单号
        $send_data = [];
        $send_data['out_order_id'] = $order_info['pay_sn'];
        $send_data['openid'] = $open_id;
        $send_data['finish_all_delivery'] = 1;

        if($express_info){
            $send_data['delivery_list'] = [[
                'delivery_id' => $express_info['delivery_id'],
                'waybill_id' => $express_info['waybill_id'],
            ]];
        }

        $apiURL = MALL_CENTER_ADDR.'/mall/wxshop/delivery/send';
        $result = fun_curl($apiURL, $send_data);
        if ($result['errcode'] != 200) {
            Redis::lPush('video_send_err', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => ['url'=>$apiURL,'order_sn' => $order_info['order_sn'], 'send_data' => $send_data, 'express' => $express_info], 'result' => $result], JSON_UNESCAPED_UNICODE));
        }
        Redis::lPush('video_send', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => ['url'=>$apiURL,'order_sn' => $order_info['order_sn'], 'send_data' => $send_data], 'result' => $result], JSON_UNESCAPED_UNICODE));
        return $result;
    }

    /**
     * Notes:订单确认收货
     * @param $order_info
     * @return false|void
     * @url 请求参数说明:https://developers.weixin.qq.com/miniprogram/dev/framework/ministore/minishopopencomponent2/API/delivery/recieve.html
     * User: rocky
     * DateTime: 2021/8/27 14:05
     */
    public function recieve($order_info)
    {
        $open_id = $this->getOpenId($order_info);
        if (!$open_id) {
            return false;
        }
        $send_data = [];
        $send_data['out_order_id'] = $order_info['pay_sn'];
        $send_data['openid'] = $open_id;

        $apiURL = MALL_CENTER_ADDR.'/mall/wxshop/delivery/recieve';
        $result = fun_curl($apiURL, $send_data);
        if ($result['errcode'] != 200) {
            Redis::lPush('video_recieve_err', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => ['url'=>$apiURL,'order_sn' => $order_info, 'send_data' => $send_data,], 'result' => $result], JSON_UNESCAPED_UNICODE));
        }
        return $result;
    }

    /**
     * Notes:创建视频号售后订单
     * @param $order_info
     * @param $param
     * @param $is_virtual 是否为虚拟订单 1是 0否
     * @param type 1:退款,2:退款退货,3:换货
     * @return false|void
     * @url https://developers.weixin.qq.com/miniprogram/dev/framework/ministore/minishopopencomponent2/API/aftersale/add.html
     * User: rocky
     * DateTime: 2021/8/27 19:53
     */
    public function addAftersale($order_info, $param, $is_virtual = 0)
    {
        $open_id = $this->getOpenId($order_info);
        if (!$open_id) {
            return false;
        }

        $send_data = [];
        $send_data['out_order_id'] = $is_virtual ? $order_info['order_sn'] : $order_info['pay_sn'];
        $send_data['out_aftersale_id'] = $param['refund_sn'];
        $send_data['openid'] = $open_id;
        $send_data['type'] = intval($param['refund_type']);
        $send_data['create_time'] = date('Y-m-d H:i:s');
        $send_data['status'] = 2;//2:商家受理中
        $send_data['finish_all_aftersale'] = 0;
        $send_data['path'] = '/app/mall/refund/list';
        $send_data['refund'] = $param['refund_amount'] * 100;
        $send_data['product_infos'] = $param['product_infos'];

        $apiURL = MALL_CENTER_ADDR.'/mall/wxshop/aftersale/add';
        $result = fun_curl($apiURL, $send_data);
        if ($result['errcode'] != 200) {
            Redis::lPush('video_addAftersale_err', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => ['url'=>$apiURL,'send_data'=>$send_data,'param'=>$param], 'result' => $result], JSON_UNESCAPED_UNICODE));
        }
        return $result;
    }

    /**
     * Notes:更新售后
     * @param $order_info
     * @param $is_virtual 是否为虚拟订单 1是 0否
     * @return false|void
     * User: rocky
     * DateTime: 2021/8/27 20:24
     */
    public function updateAftersale($order_info, $param, $is_virtual = 0)
    {
        $open_id = $this->getOpenId($order_info);
        if (!$open_id) {
            return false;
        }

        $send_data = [];
        $send_data['out_order_id'] = $is_virtual ? $order_info['order_sn'] : $order_info['pay_sn'];;
        $send_data['openid'] = $open_id;
        $send_data['out_aftersale_id'] = $param['refundsn'];
        $send_data['status'] = intval($param['status']);
        $finish_all_aftersale=0;
        //13:退款完成,14:退货退款完
        if(in_array(intval($param['status']),[13,14])){
            $finish_all_aftersale = 1;
        }
        $send_data['finish_all_aftersale'] = $finish_all_aftersale;

        $apiURL = MALL_CENTER_ADDR.'/mall/wxshop/aftersale/update';
        $result = fun_curl($apiURL, $send_data);
        if ($result['errcode'] != 200) {
            Redis::lPush('video_updateAftersale_err', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => ['url'=>$apiURL,'order_info'=>$order_info,'send_data'=>$send_data,'param'=>$param], 'result' => $result], JSON_UNESCAPED_UNICODE));
        }
        return $result;
    }
}