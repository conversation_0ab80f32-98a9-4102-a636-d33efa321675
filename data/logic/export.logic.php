<?php
/**
 * 购买行为
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
defined('InShopNC') or exit('Access Invalid!');

class exportLogic
{

    /**
     * 导出
     *
     */
    public function exportDisOrder()
    {

        $model_dis_order = Model('dis_order');

        $condition = array();
        $condition['is_dis'] = 1;
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',', trim($_GET['order_id'], ','));
            $condition['order_id'] = array('in', $_GET['order_id']);
        }
        $endtime = strtotime(date('Y-m-d 00:00:00'));
        $condition['payment_time'] = array('lt', $endtime);

        $order = 'payment_time desc';

        $data_order = $model_dis_order->getDisOrderList($condition, '', false);
        $this->DisOrderExcel($data_order);


    }

    /**
     * 导出
     */
    private function DisOrderExcel($data_order)
    {

        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';


        $indexKey = [
            'order_sn' => '订单号',
            'add_time' => '下单时间',
            'order_amount' => '订单金额(元)',
            'shipping_fee' => '订单运费(元)',
            'order_state' => '订单状态',
            'refund_amount' => '退款金额(元)',
            'finnshed_time' => '订单完成时间',
            'store_name' => '店铺名称',
            'buyer_name' => '买家账号',
            'dis_order_amount' => '订单分销金额',
            'chain_name' => '门店名称',
            'chain_id' => '门店ID',
            'dis_member_name' => '分销人',
            'dis_pay_state_text' => '分销状态',


        ];
        if (empty($filename)) $filename = 'dis_order-' . date('Y-m-d');
        if (!is_array($indexKey)) return false;
        $excel2007 = true;
        $header_arr = [
            'order_sn' => 'A',
            'add_time' => 'B',
            'order_amount' => 'C',
            'shipping_fee' => 'D',
            'order_state' => 'E',
            'refund_amount' => 'F',
            'finnshed_time' => 'G',
            'store_name' => 'H',
            'buyer_name' => 'I',
            'dis_order_amount' => 'J',
            'chain_name' => 'K',
            'chain_id' => 'L',
            'dis_member_name' => 'M',
            'dis_pay_state_text' => 'N',

        ];
        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();
        //设置保存版本格式
        if ($excel2007) {
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename . '.xlsx';
        } else {
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename . '.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;
        foreach ($indexKey as $key => $val) {

            $objActSheet->setCellValue($header_arr[$key] . $startRow, $val);
        }

        $data = array();


        foreach ($data_order as $k => $order_info) {

            $order_info['state_desc'] = orderState($order_info);
            $list = array();
            $list['order_sn'] = $order_info['order_sn'] . str_replace(array(1, 2, 3, 4), array(null, ' [预定]', '[门店自提]', ' [拼团]'), $order_info['order_type']);
            $list['add_time'] = date('Y-m-d H:i:s', $order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list['shipping_fee'] = ncPriceFormat($order_info['shipping_fee']);
            if ($order_info['shipping_fee']) {
                //$list['order_amount'] .= '(含运费'.ncPriceFormat($order_info['shipping_fee']).')';
            }
            $list['order_state'] = $order_info['state_desc'];
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s', $order_info['finnshed_time']) : '';
            $list['store_name'] = $order_info['store_name'];
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['dis_order_amount'] = ncPriceFormat($order_info['dis_order_amount']);
            $list['dis_pay_state_text'] = $order_info['dis_pay_state_text'];
            if ($order_info['chain_id'] > 0) {
                $chain_info = Model('chain')->getChainInfo(['chain_id' => $order_info['chain_id']], 'chain_name');
                $list['chain_name'] = $chain_info['chain_name'];
            }

            foreach ($order_info['extend_order_goods'] as $val) {
                $list['dis_member_name'] = $val['dis_member_name'];
            }
            $list['chain_id'] = $order_info['chain_id'];

            $data[] = $list;
        }
        foreach ($data as $k => $row) {

            foreach ($indexKey as $key => $value) {

                //这里是设置单元格的内容

                if (in_array($header_arr[$key], ['A', 'E', 'G', 'N'])) {
                    $objPHPExcel->setActiveSheetIndex(0);
                    $objActSheet->setTitle('Simple');

                    $objActSheet->setCellValueExplicit($header_arr[$key] . ($k + 2), $row[$key], PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $objActSheet->setCellValue($header_arr[$key] . ($k + 2), $row[$key]);
                }
            }
            $startRow++;
        }
        $user_path = BASE_ROOT_PATH . DS . DIR_UPLOAD . "/excel/";
        if (!is_dir($user_path)) {
            mkdir($user_path, 0777, true);
        }

        //ob_end_clean();//清除缓冲区,避免乱码
        // 下载这个表格，在浏览器输出
        /*  header("Pragma: public");
          header("Expires: 0");
          header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
          header("Content-Type:application/force-download");
          header("Content-Type:application/vnd.ms-execl");
          header("Content-Type:application/octet-stream");
          header("Content-Type:application/download");;
          header('Content-Disposition:attachment;filename='.$filename.'');
          header("Content-Transfer-Encoding:binary");*/
        $filename = iconv("utf-8", "gb2312", $filename);
        $objWriter->save($user_path . $filename);

    }

    /**
     * 导出
     *
     */
    public function exportDisVrOrder()
    {

        $model_dis_order = Model('dis_order');

        $condition = array();
        $condition['is_dis'] = 1;
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',', trim($_GET['order_id'], ','));
            $condition['order_id'] = array('in', $_GET['order_id']);
        }
        $endtime = strtotime(date('Y-m-d 00:00:00'));
        $condition['payment_time'] = array('lt', $endtime);

        $order = 'payment_time desc';

        $data_order = $model_dis_order->getDisVrOrderList($condition, '', false);
        $this->DisVrOrderExcel($data_order);


    }

    /**
     * 导出
     */
    private function DisVrOrderExcel($data_order)
    {

        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';


        $indexKey = [
            'order_sn' => '订单号',
            'add_time' => '下单时间',
            'order_amount' => '订单金额(元)',

            'order_state' => '订单状态',
            'refund_amount' => '退款金额(元)',
            'finnshed_time' => '订单完成时间',
            'store_name' => '店铺名称',
            'buyer_name' => '买家账号',
            'dis_order_amount' => '订单分销金额',
            'chain_name' => '门店名称',
            'chain_id' => '门店ID',
            'dis_member_name' => '分销人',
            'dis_pay_state_text' => '分销状态',


        ];
        if (empty($filename)) $filename = 'dis_vr_order-' . date('Y-m-d');
        if (!is_array($indexKey)) return false;
        $excel2007 = true;
        $header_arr = [
            'order_sn' => 'A',
            'add_time' => 'B',
            'order_amount' => 'C',
            'order_state' => 'D',
            'refund_amount' => 'E',
            'finnshed_time' => 'F',
            'store_name' => 'G',
            'buyer_name' => 'H',
            'dis_order_amount' => 'I',
            'chain_name' => 'J',
            'chain_id' => 'K',
            'dis_member_name' => 'L',
            'dis_pay_state_text' => 'M',

        ];
        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();
        //设置保存版本格式
        if ($excel2007) {
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename . '.xlsx';
        } else {
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename . '.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;
        foreach ($indexKey as $key => $val) {

            $objActSheet->setCellValue($header_arr[$key] . $startRow, $val);
        }

        $data = array();


        foreach ($data_order as $k => $order_info) {

            $order_info['state_desc'] = orderState($order_info);
            $list = array();
            $list['order_sn'] = $order_info['order_sn'] . str_replace(array(1, 2, 3, 4), array(null, ' [预定]', '[门店自提]', ' [拼团]'), $order_info['order_type']);
            $list['add_time'] = date('Y-m-d H:i:s', $order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);

            $list['order_state'] = $order_info['state_desc'];
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s', $order_info['finnshed_time']) : '';
            $list['store_name'] = $order_info['store_name'];
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['dis_order_amount'] = ncPriceFormat($order_info['dis_order_amount']);
            $list['dis_pay_state_text'] = $order_info['dis_pay_state_text'];
            if ($order_info['chain_id'] > 0) {
                $chain_info = Model('chain')->getChainInfo(['chain_id' => $order_info['chain_id']], 'chain_name');
                $list['chain_name'] = $chain_info['chain_name'];
            }
            $list['dis_member_name'] = $order_info['dis_member_name'];
            $list['chain_id'] = $order_info['chain_id'];

            $data[] = $list;
        }
        foreach ($data as $k => $row) {

            foreach ($indexKey as $key => $value) {

                //这里是设置单元格的内容

                if (in_array($header_arr[$key], ['A', 'E', 'G', 'N'])) {
                    $objPHPExcel->setActiveSheetIndex(0);
                    $objActSheet->setTitle('Simple');

                    $objActSheet->setCellValueExplicit($header_arr[$key] . ($k + 2), $row[$key], PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $objActSheet->setCellValue($header_arr[$key] . ($k + 2), $row[$key]);
                }
            }
            $startRow++;
        }
        $user_path = BASE_ROOT_PATH . DS . DIR_UPLOAD . "/excel/";
        if (!is_dir($user_path)) {
            mkdir($user_path, 0777, true);
        }

        //ob_end_clean();//清除缓冲区,避免乱码
        // 下载这个表格，在浏览器输出
        /*  header("Pragma: public");
          header("Expires: 0");
          header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
          header("Content-Type:application/force-download");
          header("Content-Type:application/vnd.ms-execl");
          header("Content-Type:application/octet-stream");
          header("Content-Type:application/download");;
          header('Content-Disposition:attachment;filename='.$filename.'');
          header("Content-Transfer-Encoding:binary");*/
        $filename = iconv("utf-8", "gb2312", $filename);
        $objWriter->save($user_path . $filename);

    }

    /**
     * 导出商品SKU数据
     */
    public function exportGoodsSku()
    {
        $model_goods = Model('goods');
        $condition = array();
        $limit = false;
        $condition['goods_state'] = ['in',[0,1]];
        $condition['is_virtual']  = 0;
        $goods_list = $model_goods->getGoodsCommonSkuList($condition, '', null, 'goods_state desc', $limit);
        $this->goodsSkuExcel($goods_list);
    }

    private function goodsSkuExcel($goods_list)
    {
        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';


        $indexKey = array(
            'goods_id' => 'SKU',
            'goods_commonid' => 'SPU',
            'goods_serial' => '商品货号',
            'goods_price' => '商品价格(元)',
            'goods_marketprice' => '市场价格(元)',
            'goods_storage' => '库存',
            'goods_freight' => '运费(元)',
            'goods_name' => '商品名称',
            'is_virtual' => '虚拟商品',
            'goods_url' => '商品链接',
            'goods_state' => '商品状态',
            'goods_verify' => '审核状态',
            'gc_id' => '分类ID',
            'gc_names' => '分类名称',
            'store_name' => '店铺名称',
            'is_own_shop' => '店铺类型',
            'brand_name' => '品牌名称',
            'goods_addtime' => '发布时间',
            'goods_salenum' => '已售数量'

        );
        if (empty($filename)) $filename = 'goodsSku-' . date('Y-m-d');
        if (!is_array($indexKey)) return false;
        $excel2007 = true;
        $header_arr = [
            'goods_id' => 'A',
            'goods_commonid' => 'B',
            'goods_serial' => 'C',
            'goods_price' => 'D',
            'goods_marketprice' => 'E',
            'goods_storage' => 'F',
            'goods_freight' => 'G',
            'goods_name' => 'H',
            'is_virtual' => 'I',
            'goods_url' => 'J',
            'goods_state' => 'K',
            'goods_verify' => 'L',
            'gc_id' => 'M',
            'gc_names' => 'N',
            'store_name' => 'O',
            'is_own_shop' => 'P',
            'brand_name' => 'Q',
            'goods_addtime' => 'R',
            'goods_salenum' => 'S'
        ];
        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();
        //设置保存版本格式
        if ($excel2007) {
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename . '.xlsx';
        } else {
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename . '.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;
        foreach ($indexKey as $key => $val) {

            $objActSheet->setCellValue($header_arr[$key] . $startRow, $val);
        }

        // 商品状态
        $goods_state = $this->getGoodsState();

        // 审核状态
        $verify_state = $this->getGoodsVerify();
        $data = array();
        $class = Model('goods_class')->limit(1000)->select();
        $fenlei = '';
        foreach ($class as $v) {
            $fenlei[$v['gc_id']] = $v['gc_name'];
        }


        foreach ($goods_list as $value) {
            $param = array();
            $param['goods_id'] = $value['goods_id'];
            $param['goods_commonid'] = $value['goods_commonid'];
            $param['goods_serial'] = $value['goods_serial'];
            $param['goods_price'] = ncPriceFormat($value['goods_price']);
            $param['goods_marketprice'] = ncPriceFormat($value['goods_marketprice']);
            $param['goods_storage'] = $value['goods_storage'];
            $param['goods_freight'] = $value['goods_freight'] == 0 ? '免运费' : ncPriceFormat($value['goods_freight']);
            $param['goods_name'] = $value['goods_name'];
            $param['is_virtual'] = $value['is_virtual'] == '1' ? '是' : '否';
            $param['goods_url'] = urlShop('goods', 'index', array('goods_id' => $value['goods_id']));
            $param['goods_state'] = $goods_state[$value['goods_state']];
            $param['goods_verify'] = $verify_state[$value['goods_verify']];

            $param['gc_id'] = $value['gc_id'];
            $param['gc_names'] = $fenlei[$value['gc_id_1']] . ' > ' . $fenlei[$value['gc_id_2']] . ' > ' . $fenlei[$value['gc_id_3']];

            $param['store_name'] = $value['store_name'];
            $param['is_own_shop'] = $value['is_own_shop'] == 1 ? '平台自营' : '入驻商户';
            $param['brand_name'] = $value['brand_name'];
            $param['goods_addtime'] = date('Y-m-d', $value['goods_addtime']);
            //已售数量
            $param['goods_salenum'] = $value['goods_salenum'];

            $data[] = $param;
        }
        foreach ($data as $k => $row) {
            foreach ($indexKey as $key => $value) {
                $objActSheet->setCellValue($header_arr[$key] . ($k + 2), $row[$key]);
            }
            $startRow++;
        }
        $user_path = BASE_ROOT_PATH . DS . DIR_UPLOAD . "/excel/";
        if (!is_dir($user_path)) {
            mkdir($user_path, 0777, true);
        }

        ob_end_clean();//清除缓冲区,避免乱码

        $filename = iconv("utf-8", "gb2312", $filename);
        $objWriter->save($user_path . $filename);
    }

    /**
     * 商品状态
     * @return multitype:string
     */
    private function getGoodsState() {
        return array('1' => '出售中', '0' => '仓库中', '10' => '违规下架');
    }

    private function getGoodsVerify() {
        return array('1' => '通过', '0' => '未通过', '10' => '等待审核');
    }
}