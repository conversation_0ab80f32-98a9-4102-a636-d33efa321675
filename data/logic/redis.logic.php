<?php
/**
 * redis相关操作封装
 *
 *
 * Created by PhpStorm.
 * User: lih<PERSON>bin
 * Date: 2020/3/20
 * Time: 10:39
 */

defined('InShopNC') or exit('Access Invalid!');

class redisLogic
{
    //定义对象
    private $_redis;

    //存储前缀
    private $_tb_prefix = 'QUEUE_';

    //存定义存储表的数量,系统会随机分配存储
    private $_tb_num = 2;

    //临时存储表
    private $_tb_tmp = 'TMP_TABLE';

    //锁存在的秒
    private $ttl = 300;


    /**
     * 获取单例redis对象，一般用此方法实例化
     * @return Redis|null
     */
    public function __construct()
    {

        if (!extension_loaded('redis')) {
            throw_exception('redis failed to load');
        }
        $this->_redis = new Redis();
        $this->_redis->connect(C('queue.host'), C('queue.port'));

        if (trim(C('queue.auth')) != '') {
            $this->_redis->setOption(Redis::OPT_SERIALIZER, Redis::SERIALIZER_PHP);
            $this->_redis->auth(trim(C('queue.auth')));
        }

        $this->_tb_prefix = C('redis.prefix') . $this->_tb_prefix;
    }

    /**
     * 锁机制
     * @param $key
     *
     * @param $value
     */
    public function redisLock($key, $value=1)
    {
        return $this->_redis->set($this->_tb_prefix . $key, $value, array('nx', 'ex' => $this->ttl));  //ex表示秒
    }

    /**
     * 删除锁
     * @param $key
     */
    public function delRedisLock($key)
    {
        return $this->_redis->del($this->_tb_prefix . $key);
    }

    /**
     * 设置key
     * @param $key
     *
     * @param $value
     */
    public function redisSet($key, $value)
    {
        return $this->_redis->set($this->_tb_prefix . $key, $value);
    }

    /**
     * 获取key
     * @param $key
     *
     * @param $value
     */
    public function redisGet($key)
    {
        return $this->_redis->get($this->_tb_prefix . $key);
    }

    /**
     * 删除指定的key
     * @param $key
     */
    public function redisDel($key)
    {
        return $this->_redis->del($this->_tb_prefix . $key);
    }

    /**
     * 读缓存
     * @param null $key
     * @param string $prefix
     * @param string $fields
     * @return array
     */
    public function rcache($key = null, $prefix = '')
    {

        $cache_info = $this->_redis->hGet($key, $prefix);
        if ($cache_info === false) {
            //取单个字段且未被缓存
            $data = array();
        } else {
            //string 取单个字段且被缓存
            $data = unserialize($cache_info);
        }
        // 验证缓存是否过期
        if (isset($data['cache_expiration_time']) && $data['cache_expiration_time'] < TIMESTAMP) {
            $data = array();
        }
        return $data;
    }


    /**
     * 写入缓存
     *
     * @param string $key 缓存键值
     * @param array $data 缓存数据
     * @param string $prefix 键值前缀
     * @param int $period 缓存周期  单位分，0为永久缓存
     * @return bool 返回值
     */
    function wcache($key = null, $data = array(), $prefix, $period = 0)
    {

        $period = intval($period);
        if ($period != 0) {
            $data['cache_expiration_time'] = TIMESTAMP + $period * 60;
        }
        $data = serialize($data);
        $this->_redis->hSet($key, $prefix, $data);
        $cache_info = $this->_redis->hGet($key, $prefix);
        return $cache_info;
    }

    /**
     * KV缓存 删
     *
     * @param string $key 缓存名称
     * @return boolean
     */
    function dcache($key)
    {


        return $this->_redis->hDel($key);
    }

    /**
     * 入列
     * @param unknown $value
     */
    public function push($key, $value)
    {
        try {
            return $this->_redis->lPush($this->_tb_prefix . $key, $value);
        } catch (Exception $e) {
            throw_exception($e->getMessage());
        }

    }


    /**
     * 出列
     * @param unknown $key
     */
    public function pop($key, $time)
    {
        try {
            if ($result = $this->_redis->brPop($this->_tb_prefix . $key, $time)) {
                return $result[1];
            }
        } catch (Exception $e) {
            exit($e->getMessage());
        }
    }

    /**
     * 清空,暂时无用
     */
    public function clear()
    {
        return $this->_redis->flushAll();
    }

    /**
     * 获取list区间
     * @param $key
     * @param $start
     * @param $end
     * @return array
     */
    public function lrang($key, $start = 0, $end = -1)
    {
        return $this->_redis->lRange($this->_tb_prefix . $key, $start, $end);
    }

    /**
     * 获取自增数
     * @param $key
     * @param $value
     * @return int
     */
    public function incrby($key, $value){
        return $this->_redis->incrby($key, $value);
    }

    /**
     * 获取自减属
     * @param $key
     * @param $value
     * @return int
     */
    public function decrby($key, $value){
        return $this->_redis->decrby($key, $value);
    }

    /**
     * @param $key
     * @param $time
     * @return bool
     */
    public function expire($key, $time){
        return $this->_redis->expire($key, $time);
    }
    public function  getRedis(){
        return $this->_redis;
    }

    /**
     * 获取key
     * @param $key
     *
     * @param $value
     */
    public function get($key)
    {
        return $this->_redis->get($key);
    }
    /**
     * 设置key
     * @param $key
     *
     * @param $value
     */
    public function set($key, $value)
    {
        return $this->_redis->set($key, $value);
    }
}