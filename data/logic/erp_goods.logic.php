<?php
/**
 * 操作商品类
 * <AUTHOR>
 * @date 2019.05.15
 */

use Upet\Integrates\Http\DatacenterHttp;

defined('InShopNC') or exit('Access Invalid!');
class erp_goodsLogic {
    private  $dataOrderUrl; //URL地址
    private  $path;   //接口名
 	public function __construct(){
 	    header("Content-Type: text/html;charset=utf-8");
        $this->path='goods/Goods';
        $this->dataOrderUrl=C('erp_url_host').'/'.$this->path;
    }	

    /**
     * 获取ERP商品套餐价格
     * @param string $serial 商品货号
     * @return int
     */
    public function getGoodsPrice($serial){
    	$param = [];
    	$param['CombinationNumber'] = $serial; //商品货号Id
        $url = $this->dataOrderUrl.'/getprice';
        $response = doCurlGetRequest($url,$param);
    	$result = json_decode($response,1);
    	if(isset($result['http_code']) && $result['http_code']=='200'){ //判断http状态
    		$data=$result['msg'];
    		if($data){
    			return $data;
    		}else{
    			return 0;
    		}
    	}elseif($result['http_code']=='400'){
            return -1;
        }
    }

    /**
     * 通知商品改变信息
     * @param $goods_ids
     * @return bool
     * @throws Exception
     */
    public function goodsChangeNotify($goods_ids,$store_id,$shop_id=0){
        try{
            $rs = DatacenterHttp::post(BOSS_CENTER_ADDR . "/boss/product/bbc/change-notify",[
                'ids' => $goods_ids,
                'DsOrgId'=>intval($store_id),
                'ShopId'=>intval($shop_id)
            ]);
            return $rs['code'] == 200;
        }catch (Exception $e){
            return false;
        }
    }

    /**
     * 通知运营中心优惠券兑换信息
     * @param $voucher_id
     * @return bool
     * @throws Exception
     */
    public function voucherChangeNotify($voucher_id)
    {
        $param = [];
        $param['id'] = $voucher_id; //优惠券ID
        $url = BOSS_CENTER_ADDR . "/boss/market/activity/voucher/add";
        $result = doCurlPostRequest($url, $param);
        wkcache('voucher_add_log', $result, 2 * 60 * 60);
        if (isset($result['http_code']) && $result['http_code'] == '200') { //判断http状态
            $data = json_decode($result['msg'], true);
            wkcache('voucher_add_log_data', $data, 2 * 60 * 60);
            if ($data['code'] == 200) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

}
