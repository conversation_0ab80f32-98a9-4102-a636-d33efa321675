<?php
/**
 * Created by PhpStorm.
 * User: lihaobin
 * Date: 2020/5/15
 * Time: 10:58
 */
defined('InShopNC') or exit('Access Invalid!');
class distributeLogic
{

    /**
     * dis_member_fans 模型 对象
     * @var obj
     */
    private $_dis_member_model;

    public function __construct() {
        $this->_dis_member_model = Model('dis_member_fans');
    }
    /**
     * 支付成功修改粉丝关系
     * @param $member_id
     */
    public function editDisMemberFans($member_id) {
        $distribute_logic = Logic('distribute');
        $temproary_member_info = $this->_dis_member_model->getDisMemberTemporaryFansInfo(['member_id' => $member_id]);
        if($temproary_member_info){
            //判断是否是内扫外部，外部扫外部
            $distri_result = $distribute_logic->isDisMember($member_id, $temproary_member_info['dis_member_id']);
            if($distri_result){
                //删除临时绑定粉丝
                return $this->_dis_member_model->delDisMemberTemFans($member_id);
            }
            $dis_member_info = $this->_dis_member_model->getDisMemberFansInfo(['member_id' => $member_id, 'state' => 1]);
            //如果不存在有效绑定关系，那么就建立绑定关系
            if (!$dis_member_info) {
                $dis_member_id = $temproary_member_info['dis_member_id'];
                //添加新的粉丝绑定关系
                $data = [
                    'member_id'=>$member_id,
                    'dis_member_id'=>$dis_member_id,
                    'create_time' =>TIMESTAMP,
                    'update_time'=>TIMESTAMP
                ];
                $condition = [
                    'member_id'=>$member_id,
                    'dis_member_id'=>$dis_member_id
                ];
                $distri_member_info = $this->_dis_member_model->getDisMemberFansInfo($condition);
                if($distri_member_info){
                    $data_dis_member = [
                        'state'=>1,
                        'update_time'=>TIMESTAMP,
                        'create_time'=>TIMESTAMP
                    ];
                    $this->_dis_member_model->editFans($data_dis_member,['dis_fans_id'=>$distri_member_info['dis_fans_id']]);
                }else{
                    $this->_dis_member_model->addFans($data);
                }
                //如果有关系，就延长绑定时间
                $this->editOutsideMemeberTime($dis_member_info['dis_member_id']);
                //检查外部分销员是否是可以从新绑定内部分销员
                $this->setOutsideMember($member_id, $dis_member_id);
            }
        }else{
            //客户再次下单，修改绑定时间，从新计算
            $dis_member_info = $this->_dis_member_model->getDisMemberFansInfo(['member_id'=>$member_id,'state'=>1]);
            if($dis_member_info){
                $condition=[
                    'dis_fans_id'=>$dis_member_info['dis_fans_id'],
                ];
                $data = [
                    'create_time'=>TIMESTAMP
                ];
                //如果有关系，就延迟绑定时间
                $this->editOutsideMemeberTime($dis_member_info['dis_member_id']);
                $this->_dis_member_model->editDistriMember($condition,$data);
            }
        }
    }

    /**
     * 添加实物分销业绩
     *
     */
    public function addDisOutsideOrder($goods_list, $order_list){
        $model = Model();
        $dis_outside_model = Model('dis_outside_order');

        $dis_member_id_arr = [];
        foreach ($goods_list as $value){
            $dis_member_id_arr[]=$value['out_member_id'];
        }
        $out_condition = [
            'member.member_id'=>['in',array_unique($dis_member_id_arr)],
        ];
        $field='member.member_id,member.distri_chainid,chain.chain_name,member.bill_user_name';
        $on = 'member.distri_chainid=chain.chain_id';

        $dis_member_list = $model->table('member,chain')->field($field)->join('left,left')
            ->on($on)->where($out_condition)->limit(false)->key('member_id')->select();

        if($dis_member_list){
            $dis_outside_list = array();
            foreach ($goods_list as $value){
                //如果外部代理人存在对应上级就生成业绩
                if($dis_member_list[$value['out_member_id']]){
                    $_pay = array();
                    $_pay['rec_id']=$value['rec_id'];
                    $_pay['order_id'] = $value['order_id'];
                    $_pay['order_sn'] = $order_list[$value['order_id']]['order_sn'];
                    $_pay['leader_member_id'] = $value['out_member_id'];
                    $_pay['leader_member_name'] = $dis_member_list[$value['out_member_id']]['bill_user_name'];
                    $_pay['dis_member_id'] = $value['dis_member_id'];
                    $_pay['goods_amount'] = $value['goods_pay_price'];
                    $_pay['goods_name'] = $value['goods_name'];
                    $_pay['goods_image'] = $value['goods_image'];
                    $_pay['chain_id'] = $dis_member_list[$value['out_member_id']]['distri_chainid'];
                    $_pay['chain_name'] = $dis_member_list[$value['out_member_id']]['chain_name'];
                    $_pay['is_virtual'] = 0;
                    $_pay['create_time'] = TIMESTAMP;
                    $dis_outside_list[] = $_pay;
                }
            }

            if($dis_outside_list){
                $dis_outside_model->addOutsideOrderAll($dis_outside_list);
            }
        }
    }

    /**
     * 添加虚拟分销业绩
     *
     */
    public function addDisOutsideVrOrder($vr_goods_list){

        $model = Model();

        $dis_outside_model = Model('dis_outside_order');

        $dis_member_id_arr = [];
        foreach ($vr_goods_list as $value){
            $dis_member_id_arr[]=$value['out_member_id'];
        }

        $out_condition = [
            'member.member_id'=>['in',array_unique($dis_member_id_arr)],

        ];

        $field='member.member_id,member.distri_chainid,chain.chain_name,member.bill_user_name';
        $on = 'member.distri_chainid=chain.chain_id';

        $dis_member_list = $model->table('member,chain')->field($field)->join('left,left')
            ->on($on)->where($out_condition)->limit(false)->key('member_id')->select();

        if($dis_member_list){
            $dis_outside_list = array();
            foreach ($vr_goods_list as $value){

                //如果外部代理人存在对应上级就生成业绩
                if($dis_member_list[$value['out_member_id']]){
                    $_pay = array();
                    $_pay['vr_rec_id']=$value['rec_id'];
                    $_pay['order_id'] = $value['order_id'];
                    $_pay['order_sn'] = $value['order_sn'];
                    $_pay['leader_member_id'] = $value['out_member_id'];
                    $_pay['leader_member_name'] = $dis_member_list[$value['out_member_id']]['bill_user_name'];
                    $_pay['dis_member_id'] = $value['dis_member_id'];
                    $_pay['goods_amount'] = $value['pay_price'];
                    $_pay['goods_name'] = $value['goods_name'];
                    $_pay['goods_image'] = $value['goods_image'];
                    $_pay['chain_id'] = $dis_member_list[$value['out_member_id']]['distri_chainid'];
                    $_pay['chain_name'] = $dis_member_list[$value['out_member_id']]['chain_name'];
                    $_pay['is_virtual'] = 1;
                    $_pay['create_time'] = TIMESTAMP;
                    $dis_outside_list[] = $_pay;
                }
            }

            if($dis_outside_list){
                $result = $dis_outside_model->addOutsideOrderAll($dis_outside_list);
            }

        }
    }


    /**
     * 判断是否有对应门店
     * @param $member_id
     * @return int
     */
    public function getDisMemberChain($member_id){
        $member_model = Model('member');
        $member_info =$member_model->getMemberInfo(['member_id'=>$member_id],'member_id,distri_chainid');
        $chain_id = 0;

        if($member_info['distri_chainid']){
            return  $member_info['distri_chainid'];
        }
        $outside_member_nfo = $this->_dis_member_model->outsideMemberInfo(['member_id'=>$member_id,'state'=>1],"chain_id");

        if($outside_member_nfo){

            return $outside_member_nfo['chain_id'];
        }

        return $chain_id;
    }

    /**
     * 判断内外部分销员是否要解出绑定关系
     *
     */
    public function editOutsideMemberLose(){

        $dis_member_fans_model = Model('dis_member_fans');

        $condition = [
            'state'=>1
        ];
        $dis_outside_member_list = $dis_member_fans_model->getOutsideMemberList($condition);

        if($dis_outside_member_list){
            $dm_id_arr = [];
            foreach ($dis_outside_member_list as $value){
                $condition = [
                    'dis_member_id'=>$value['member_id'],
                    'state'=>1
                ];
                //查询外部分销员，是否已完全不存对应有效粉丝关系
                $dis_member_info = $dis_member_fans_model->getDisMemberFansInfo($condition);

                //查询内部分销员和外部分销员之间的粉丝关系是否超过有效期
                $condition = [
                    'dis_member_id'=>$value['dis_member_id'],
                    'member_id' => $value['member_id'],
                    'state'=>1
                ];
                $dis_outside_member_info = $dis_member_fans_model->getDisMemberFansInfo($condition);

                //如果外部分销员下面所有粉丝已失效，并且外部分销员和内部分销员粉丝关系也失效，那么就接触推荐人绑定关系
                if(empty($dis_member_info) && empty($dis_outside_member_info)){
                    $dm_id_arr[] = $value['dm_id'];
                }
            }

            //如果外部分销员已不存在粉丝，那么判断推荐关系失效
            if($dm_id_arr){
                $condition=[
                    'dm_id'=>['in',$dm_id_arr]
                ];
                $data=[
                    'state'=>2
                ];
                $dis_member_fans_model->editOutsideMember($condition, $data);
            }
        }

    }

    /**
     * 更新内部分销员与外部分销员之间的绑定时间
     * @param $member_id int 外部分销员id
     */
    public function editOutsideMemeberTime($member_id){
        $condition = [
            'member_id'=>$member_id,
            'state'=>1
        ];
        //延长外部分销员与内部分销员之间的推荐关系
        $dis_outsie_info = $this->_dis_member_model->outsideMemberInfo($condition);
        if($dis_outsie_info){
            $data = [
                'updatetime'=>TIMESTAMP
            ];
            $this->_dis_member_model->editOutsideMember(['dm_id'=>$dis_outsie_info['dm_id']], $data);
        }
        //延长外部分销员与内部分销员之间的粉丝关系
        $dis_member_info = $this->_dis_member_model->getDisMemberFansInfo($condition);
        if($dis_member_info){
            $data = [
                'create_time'=>TIMESTAMP,
                'update_time'=>TIMESTAMP
            ];
            $condition = ['dis_fans_id'=>$dis_member_info['dis_fans_id']];
            $this->_dis_member_model->editDistriMember($condition, $data);
        }

    }

    /**
     * 判断外部代理人有没有上级分销员，如果没有，判断是否是通过扫内部分销员的分销码
     * @param $member_id int 用户id
     * @param $dis_member_id int 对应分销员id
     *
     */
    public function setOutsideMember($member_id, $dis_member_id){
        $member_model = Model('member');
        $member_info =$member_model->getMemberInfo(['member_id'=>$member_id],'member_id,distri_state,distri_chainid');

        if($member_info['distri_state'] != 2){
            return ;
        }
        if($member_info['distri_chainid']){
            return ;
        }
        $condition = [
            'member_id'=>$member_id,
            'state'=>1
        ];
        $dis_outsie_info = $this->_dis_member_model->outsideMemberInfo($condition);
        if($dis_outsie_info){
           return ;
        }
        $member_info =$member_model->getMemberInfo(['member_id'=>$dis_member_id],'member_id,distri_chainid');
        //如果是外部分销员不用处理
        if($member_info['distri_chainid'] == 0){
            return ;
        }
        $condition = [
            'member_id'=>$member_id,
            'dis_member_id'=>$dis_member_id,
        ];
        $dis_outsie_info = $this->_dis_member_model->outsideMemberInfo($condition);

        if($dis_outsie_info && $dis_outsie_info['state'] == 0){
            return ;
        }
        $dis_data = [];
        $dis_data['dis_member_id'] = $dis_member_id;
        $dis_data['member_id'] = $member_id;
        $dis_data['chain_id'] = $member_info['distri_chainid'];
        $dis_data['addtime'] = TIMESTAMP;
        $dis_data['updatetime'] = TIMESTAMP;
        $dis_data['state'] = 1;
        if($dis_outsie_info && in_array($dis_outsie_info['state'],[1,2])){
            $condition = [
                'dm_id'=>$dis_outsie_info['dm_id']
            ];
            return $this->_dis_member_model->editOutsideMember($condition, $dis_data);
        }else{
            return $this->_dis_member_model->addDisOutsideMember($dis_data);
        }


    }

    /**
     * 判断粉丝绑定关系30天是否失效
     */
    public function editDisMemberFansLose(){
        $condition = ['state'=>1];
        $time = TIMESTAMP - intval(C('distri_user_time'))*24*60*60;
        $condition['create_time'] = ['elt',$time];//小于等于
        $this->_dis_member_model->delDisMemberTemFansList($condition);//删除临时粉丝
        $dis_member_fans_list = $this->_dis_member_model->getDisMemberFansList($condition,'',false);

        if(!empty($dis_member_fans_list)){
            $dis_fans_id_arr = [];
            foreach ($dis_member_fans_list as $value){
                $dis_fans_id_arr[] = $value['dis_fans_id'];
            }

            if($dis_fans_id_arr){
                $condition=[
                    'dis_fans_id'=>['in',implode(',',$dis_fans_id_arr)]
                ];
                $data = ['state'=>0,'update_time'=>TIMESTAMP];
                $this->_dis_member_model->editDistriMember($condition,$data);

            }
        }
    }

    /**
     * 计算相关佣金和业绩
     * @param bool $flag true 后台查询添加条件
     */
    public function getStatCount($conditon, $flag = false){
        $model = Model();
        if ($flag) {
            $on = 'dis_pay.dis_member_id = member.member_id';
            $dis_pay_list = $model->table('dis_pay,member')->join('left')->on($on)->field('sum(dis_pay_amount) as dis_pay_amount')->where($conditon)->limit(false)->select();
        }else{
            $dis_pay_list = $model->table('dis_pay')->field('sum(dis_pay_amount) as dis_pay_amount')->where($conditon)->limit(false)->select();
        }
        $data = [
            'dis_pay_amount'=>$dis_pay_list[0]['dis_pay_amount'],
        ];

        return $data;

    }

    /**
     * 分销员清退操作
     */
    public function disMemberClear($member_info){
        $member_fans_model = Model('dis_member_fans');
        if($member_info['distri_chainid']){
            $condition =[
                'dis_member_id'=>$member_info['member_id'],
            ];
        }else{
            $condition =[
                'member_id'=>$member_info['member_id'],
            ];
        }
        $dis_contion=[
            'dis_member_id|member_id'=>$member_info['member_id']
        ];

        //删除内外部绑定关系
        $member_fans_model->delOutsideMember($condition);
        //删除粉丝关系
        $member_fans_model->delDisMemberFans($dis_contion);
        //删除临时粉丝关系
        $member_fans_model->table('distri_member_temporary_fans')->where(['dis_member_id'=>$member_info['member_id']])->delete();

        //删除企业微信门店登录手机记录
        Model('chain_member')->where(['mobile'=>$member_info['member_mobile']])->delete();

        //修改内部员工记录
        Model('chain_user')->updateChainUser(['user_mobile' => $member_info['member_mobile']], ['isjudge' =>3,'store_phone_state'=>0]);

    }

    /**
     *
     *
     */
    public function outsideMember($member_id, $dis_member_info){

        $dis_data = [];
        $dis_data['dis_member_id'] = $dis_member_info['member_id'];
        $dis_data['member_id'] = $member_id;
        $dis_data['chain_id'] = $dis_member_info['distri_chainid'];
        $dis_data['addtime'] = TIMESTAMP;
        $dis_data['updatetime'] = $dis_data['addtime'];

        $dis_member_model = Model('dis_member_fans');
        $dis_outdis_member = $dis_member_model->outsideMemberInfo(['member_id'=>$member_id]);
        if($dis_outdis_member){
            output_error('已存在上级推荐人，无法再次发起');
        }

        $dis_member_model->addDisOutsideMember($dis_data);

    }

    public function isDisMember($member_id, $dis_member_id){
        $member_info = Model('member')->getMemberInfo(['member_id'=>$member_id],'member_id,distri_state,distri_chainid');
        $state = false;
        if($member_info['distri_chainid']>0 && $member_info['distri_state']==2){
            $state = true;
        }
        if($member_info['distri_chainid']==0 && $member_info['distri_state']==2){
            $member_n_info = Model('member')->getMemberInfo(['member_id'=>$dis_member_id],'member_id,distri_state,distri_chainid');
            if($member_n_info['distri_chainid']==0 && $member_n_info['distri_state']==2){
                $state = true;
            }
        }

        return $state;

    }

    public function goodsMemberDis($dis_id, $member_info, $is_vr = 0, $dis_type = 0, $ds_uid =0)
    {
        $member_in = [
            'member_id' => $member_info['member_id'],
            'dis_id' => $dis_id,
        ];
        $model_dis_member_fans = Model('dis_member_fans');
        if ($dis_id) {//分销
            $model_dis_goods = Model('dis_goods');
            $condition = array();
            $condition['distri_id'] = $dis_id;
            $condition['distri_goods_state'] = 1;
            $dis_goods = $model_dis_goods->getDistriGoodsInfo($condition, 'member_id', true);
            if ($dis_goods) {
                $member_in['is_goods'] = 1;
                $model_dis_member_fans->addDisMemberFans($member_info['member_id'], $dis_goods['member_id'], $dis_type);
            }else{
                $member_in['msg'] = "无分销商品记录";
            }
        }elseif($ds_uid){
            //crm分享链接
            $model_dis_member_fans->addDisMemberFans($member_info['member_id'], $ds_uid, $dis_type);
        }
        $member_in['time'] = date('Y-m-d H:i:s', TIMESTAMP);
        $member_in['distri_user_time'] = C('distri_user_time');
        $path = BASE_ROOT_PATH . DS . DIR_UPLOAD . "/dis_log/";
        if (!is_dir($path)) {
            mkdir(iconv("UTF-8", "GBK", $path), 0777, true);
        }
        $logname = $path . date('Y-m-d') . 'dis_log.log';
        if ($is_vr) {
            $logname = $path . date('Y-m-d') . 'o_dis_log.log';
        }
        error_log(print_r($member_in, true), 3, $logname);
    }

    /**
     * @param $id
     * @param $type
     */

    public function getGoodsIds($tag_id, $gc_id)
    {
        $model = Model();

        if ($tag_id) {

           $goods_data = $model->table('tags_goods')->field(' distinct tag_goods_common')->where(['tag_id'=>$tag_id])->key('tag_goods_common')->select();

        } else {

            $goods_data = $model->table('goods')->field(' distinct goods_commonid')->where(['gc_id'=>$gc_id])->limit(false)->key('goods_commonid')->select();

        }
        return $goods_data;
    }

    /**
     * @param $id
     * @param $type
     */

    public function getGoodsIdsV2($tag_id, $gc_id){
        $model = Model();
        if ($tag_id) {
            $goods_data = $model->table('tags_goods')->field(' distinct tag_goods_id')->where(['tag_id'=>$tag_id])->key('tag_goods_id')->select();
        } else {
            $store_id = $_REQUEST['store_id']?:1;
            $goods_data = $model->table('goods')->field(' distinct goods_commonid')
                ->where(['gc_id'=>$gc_id,'store_id'=>$store_id])
                ->limit(false)
                ->key('goods_commonid')
                ->select();
        }
        return $goods_data;
    }

    /**
     * 获取分销类型描述
     *
     * @param $disType
     * @return string
     */
    public function getDisTypeText($disType)
    {
        switch ($disType) {
            case 1:
                return '通过分销员分享的链接下单';
            case 2:
                return '通过分销员海报下单';
            case 3:
                return '通过扫自己的码或链接下单';
            case 5:
                return '分销关系客户自主下单';
        }

        return "其他";
    }

    /**
     * 转移分销员
     */
    public function transferMemberDis($data){
        $res['code'] = 400;
        $new = $data['new_mobile'];
        $old = $data['old_mobile'];
        if (!preg_match('/^1\d{10}$/', $new)) {
            $res['msg'] = $new . "手机号格式不正确";
            return $res;
        }
        if (!preg_match('/^1\d{10}$/', $old)) {
            $res['msg'] = $old . "手机号格式不正确";
            return $res;
        }
        if ($new == $old) {
            $res['msg'] = "新旧账号不能一致";
            return $res;
        }
        $oldMember = Model('member')->getMemberInfo(['member_mobile' => $old]);
        if (empty($oldMember)) {
            $res['msg'] = "原账号不存在";
            return $res;
        }
        if ($oldMember['distri_state'] <> 2) {
            $res['msg'] = "旧账号非分销账号，不需要合并";
            return $res;
        }
        $newMember = Model('member')->getMemberInfo(['member_mobile' => $new]);
        $model_member = Model('member');
        if (empty($newMember)) {
            $member = array();
            $member['member_mobile'] = $new;
            $member['member_mobile_bind'] = 1;
            $member['member_time'] = $member['member_login_time'] = $member['member_old_login_time'] = time();
            $member['scrm_user_id'] = '';
            $num = substr($new, -4);
            $member['member_name'] = Logic('connect_api')->getMemberName('upet_', $num);
            $result = $model_member->insert($member);
            if (!$result) {
                $res['msg'] = "新用户添加失败";
                return $res;
            }
            $member['member_id'] = $result;
            $newMember = $member;
        }

        $model = Model();
        try {
            $model->beginTransaction();
            foreach ([
                         'dis_pay' => 'dis_member_id',
                         'dis_trad_cash' => 'tradc_member_id',
                         'dis_trad_log' => 'lg_member_id',
                         'distri_member_fans' => 'dis_member_id',
                         'distri_outside_member' => 'dis_member_id',
                         'vr_order' => 'dis_member_id',
                         'vr_order_code' => 'dis_member_id',
                         'order_goods' => 'dis_member_id',
                         'chain_user' => 'user_mobile',
                         'chain_member' => 'mobile',
                     ] as $table => $columns) {

                foreach ((array)$columns as $column) {
                    if ($table =='chain_user') {
                        $model->table($table)->where([$column => $oldMember['member_mobile']])->update([$column => $newMember['member_mobile'],'shr_state'=>1]);
                    }elseif ($table =='chain_member'){
                        $model->table($table)->where([$column => $oldMember['member_mobile']])->update([$column => $newMember['member_mobile']]);
                    }else{
                        $model->table($table)->where([$column => $oldMember['member_id']])->update([$column => $newMember['member_id']]);
                    }
                }
            }
            Model('member')->editMember(['member_id' => $newMember['member_id']], [
                'trad_amount' => $newMember['trad_amount'] + $oldMember['trad_amount'],
                'freeze_trad' => $newMember['freeze_trad'] + $oldMember['freeze_trad'],
                'dis_trad_money' => $newMember['dis_trad_money'] + $oldMember['dis_trad_money'],
                'distri_state' => $oldMember['distri_state'],
                'bill_user_name' => $oldMember['bill_user_name'],
                'bill_type_code' => $oldMember['bill_type_code'],
                'bill_type_number' => $oldMember['bill_type_number'],
                'bill_bank_name' => $oldMember['bill_bank_name'],
                'distri_chainid' => $oldMember['distri_chainid'],
                'bill_bank_branch' => $oldMember['bill_bank_branch'],
                'member_identity' => $oldMember['member_identity'],
                'distri_brandid' => $oldMember['distri_brandid'],
                'member_truename' => $oldMember['member_truename'],
            ]);

            Model('member')->editMember(['member_id' => $oldMember['member_id']], [
                'trad_amount' => 0,
                'freeze_trad' => 0,
                'dis_trad_money' => 0,
                'distri_state' => 0,
                'bill_user_name' => '',
                'bill_bank_name' => '',
                'bill_type_number' => '',
                'member_identity' => '',
                'distri_chainid' => 0,
                'distri_brandid' => 0,
            ]);
            $model->commit();
            $res['code'] = 200;
            $res['msg'] = '绑定成功';
        } catch (Exception $exception) {
            $model->rollback();
            $res['msg'] = '操作失败';
        }
        return $res;
    }
}