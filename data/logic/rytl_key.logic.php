<?php
/**
 * 操作ERP订单类 服务套餐订单
 * <AUTHOR>
 * @date 2018.10.23
 */
defined('InShopNC') or exit('Access Invalid!');

class rytl_keyLogic
{

    public function __construct()
    {
        define('SCRIPT_ROOT', BASE_DATA_PATH . '/api/ERP');
        require_once SCRIPT_ROOT . '/rytlrsa.php';
    }

    public function get_key($http_token, $mobile)
    {
        $rsa = new \Rytlrsa();
        $member_info = array();
        //解析验证数据
        $jwt_data = $rsa->decode($http_token);
        if (is_array($jwt_data) && !empty($jwt_data) && $jwt_data['status'] == true) {
            //登录获取电商用户信息
            $condition = array();
            $condition['member_mobile'] = $mobile;//$jwt_data['data']['mobile'];
            //获取登录信息
            $result = $this->_memberIsExist($condition);
            if ($result['status']) {
                $member_info = $result['data'];
            }
        } else {
            $member_info = $jwt_data['msg'];
        }
        return $member_info;
    }

    /*
     * 依据指定的条件条件会员是否存在
    * @param array $param 条件数组
    * @return boolean true:用户存在 |false:用户不存在
    */
    private function _memberIsExist($param = array())
    {
        $memberModel = Model('member');
        $data = array();
        $data['status'] = false;
        $data['msg'] = "登陆失败";
        $data['data'] = array();
        $member_info = $memberModel->getMemberInfo(array("member_mobile" => $param['member_mobile']));
        if (!$member_info) {
            $insert_array = array();
            $member_time = time();
            $phone = $param['member_mobile'];
            $num = substr($phone, -4);
            $logic_connect_api = Logic('connect_api');
            $member_name = $logic_connect_api->getMemberName('upet_rytl', $num);
            $insert_array['member_name'] = $member_name;
            $insert_array['member_passwd'] = "fe657496b1c46be00e86c05d2b190c9a";
            $insert_array['member_sex'] = null;
            $insert_array['member_qq'] = null;
            $insert_array['member_ww'] = null;
            $insert_array['member_mobile_bind'] = 1;
            $insert_array['member_mobile'] = $param['member_mobile'];
            $insert_array['member_time'] = $member_time;
            $insert_array['geval_comment_status'] = 7;
            $member_id = $memberModel->addMember($insert_array);
            if ($member_id) {
                $member_info = $memberModel->getMemberInfo(['member_id' => $member_id], "*", true);
                $data['msg'] = "登陆成功";
                $data['data'] = $member_info;
            }
        } else {
            $data['status'] = true;
            $data['data'] = $member_info;
        }
        return $data;
    }

    /**
     * 生成token
     */
    private function _get_token($member_id, $member_name, $client)
    {
        $model_mb_user_token = Model('mb_user_token');

        //查找该用户IOS登陆最近的KEY
        $token_after = $model_mb_user_token->where(array("member_id" => $member_id, "client_type" => $client))->order("login_time desc")->find();
        if ($token_after) {
            return $token_after['token'];
        }

        $mb_user_token_info = array();
        $token = md5($member_name . strval(TIMESTAMP) . strval(rand(0, 999999)));
        $mb_user_token_info['member_id'] = $member_id;
        $mb_user_token_info['member_name'] = $member_name;
        $mb_user_token_info['token'] = $token;
        $mb_user_token_info['login_time'] = TIMESTAMP;
        $mb_user_token_info['client_type'] = $client;
        $result = $model_mb_user_token->addMbUserToken($mb_user_token_info);
        if ($result) {
            return $token;
        } else {
            return null;
        }
    }
}
