<?php
/**
 * 接口文档：http://10.1.1.248:7040/swagger/index.html
 * 售后申请 10.1.1.248:7040/order-api/refund/apply
 *
 * 接口文档：http://10.1.1.248:11001/swagger/index.html
 * 驳回售后申请   10.1.1.248:11001/boss/ordercenter/order/OrderRefundReject
 * 同意售后申请   10.1.1.248:11001/boss/ordercenter/order/OrderRefundAgree
 * 商家申请售后（自动退款）10.1.1.248:11001/boss/refund/apply
 * @desc 同步ERP订单中心操作售后服务类
 * <AUTHOR> zyw
 * @date 2021-3-20
 */

use Upet\Integrates\Http\DatacenterHttp;
use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Models\Chain;
use Upet\Models\Order as OrderAlias;
use Upet\Models\OrderMain;
use Upet\Models\OrderPresale;
use Upet\Models\RefundPreOrder;
use Upet\Models\RefundReturn;
use Upet\Modules\Order\Queues\VideoOrderAddaftersaleQueue;
use Upet\Modules\Order\Queues\VideoOrderUpdateaftersaleQueue;
use Upet\Queues\SyncTaskQueue;
use Upet\Models\Datacenter\VipCardOrder;

defined('InShopNC') or exit('Access Invalid!');

class refundLogic
{
    /**
     * 用户全额退款申请
     *
     * @return array
     */
    public function memberAllApply($order_id,$member_id,$buyer_message,$refund_pic = [],$reason_id = '0',$reason_info = '取消订单，全部退款')
    {
        $model_refund = Model('refund_return');
        $order_id     = intval($order_id);
        $model_trade  = Model('trade');
        $order_paid   = $model_trade->getOrderState('order_paid');//订单状态20:已付款

        $model_order              = Model('order');
        $condition                = array();
        $condition['buyer_id']    = $member_id;
        $condition['order_id']    = $order_id;
        $condition['order_state'] = $order_paid;
        $order_info               = $model_order->getOrderInfo($condition);
        $payment_code             = $order_info['payment_code'];//支付方式

        $lock = Redis::lock("refund-all-posts:$member_id:$order_id", 60)->setAutoRelease();
        if (!$lock->get()) {
            throw new Exception('订单退款处理中');
        }

        $condition                 = array();
        $condition['buyer_id']     = $member_id;
        $condition['order_id']     = $order_id;
        $condition['goods_id']     = '0';
        $condition['seller_state'] = array('lt', '3');
        $refund                    = $model_refund->getRefundReturnInfo($condition);
        if (empty($order_info) || $payment_code == 'offline' || $refund['refund_id'] > 0) {//检查数据,防止页面刷新不及时造成数据错误
            throw new Exception('参数错误');
        } else {
            $book_amount         = Logic('order_book')->getDepositAmount($order_info);//订金金额
            $allow_refund_amount = ncPriceFormat($order_info['order_amount'] - $book_amount);//可退款金额
            //判断是周期购中途退款
            if ($order_info['order_type'] == 9 && $order_info['is_head'] == 1) {
                $cycle_info = Model('cycle_push_info')->getPushInfo(['erp_order_sn'=>$order_info['order_sn']]);
                $order['cycle_num'] = $order_info['cycle_num'];
                $order['cycle_price'] = $cycle_info['cycle_price'];
                $refund_price = Model('cycle_push_info')->getcycleRefundAmount($order_info['order_sn']);
                if ($refund_price > 0) {
                    $allow_refund_amount = ncPriceFormat($order_info['order_amount'] - $book_amount - $refund_price);//可退款金额
                }
            }

            $refund_array                   = array();
            $refund_array['refund_type']    = '1';//类型:1为退款,2为退货
            $refund_array['seller_state']   = '1';//状态:1为待审核,2为同意,3为不同意
            $refund_array['order_lock']     = '2';//锁定类型:1为不用锁定,2为需要锁定
            $refund_array['goods_id']       = '0';
            $refund_array['order_goods_id'] = '0';
            $refund_array['reason_id']      = $reason_id;
            $refund_array['reason_info']    = $reason_info;
            $refund_array['goods_name']     = '订单商品全部退款';
            $refund_array['refund_amount']  = ncPriceFormat($allow_refund_amount);
            $refund_array['buyer_message']  = $buyer_message;
            $refund_array['add_time']       = time();

            $erp_refund['order_id']      = $order_id;
            $erp_refund['add_time']      = $refund_array['add_time'];
            $erp_refund['order_sn']      = $order_info['order_sn'];
            $erp_refund['refund_amount'] = $refund_array['refund_amount'];
            $erp_refund_sn               = $model_refund->getRefundsn($order_info['store_id']);
            $erp_refund['refund_sn']     = $erp_refund_sn;
            $erp_refund['refund_type']   = $refund_array['refund_type'];
            $erp_refund['reason_info']   = $refund_array['reason_info'];
            $erp_refund['buyer_message'] = $refund_array['buyer_message'];
            $erp_refund['goods_id']      = $refund_array['goods_id'];
            $erp_refund['refund_pic']    = $refund_pic;

            //判断周期购主订单退款，不走数据中心
            if ($order_info['order_type'] == 9 && $order_info['is_head'] == 1){

            }else{
                //订单申请售后 同步ERP数据中心
                $erp_result = $this->refundOrderApply($erp_refund);
                $refund_array['refund_order_sn'] = $erp_result['refund_order_sn'];
            }

            $pic_array                = array();
            $pic_array['buyer']       = $refund_pic;//上传凭证
            $info                     = serialize($pic_array);
            $refund_array['pic_info'] = $info;
            $state                    = $model_refund->addRefundReturn($refund_array, $order_info, array(), $erp_refund_sn);
            if ($state) {
                $model_refund->editOrderLock($order_id);
                if ($order_info['order_type'] == 9) {
                    Model('cycle_push_info')->refundMainOrder($order_info);
                }
                return array('create_time' => str_pad($order_info['add_time'],13,"0",STR_PAD_RIGHT));
            } else {
                throw new Exception('退款申请保存失败');
            }
        }
    }

    /**
     * 用户申请部分售后
     *
     * @return void
     */
    public function memberPartApply($data = [
        'order_id' => '', 'order_goods_id' => '',
        'member_id' => '', 'buyer_message' => '',
        'refund_pic' => '', 'refund_amount' => 0,
        'goods_num' => 0, 'reason_id' => 0, 'refund_type' => 0,
    ])
    {
        $model_refund = Model('refund_return');

        $condition                   = array();
        $condition['buyer_id']       = $data['member_id'];
        $condition['order_id']       = $data['order_id'];
        $order_info                  = $model_refund->getRightOrderList($condition, $data['order_goods_id']);
        $refund_state                = $model_refund->getRefundState($order_info);//根据订单状态判断是否可以退款退货
        $condition                   = array();
        $condition['buyer_id']       = $data['member_id'];
        $condition['order_id']       = $data['order_id'];
        $condition['order_goods_id'] = $data['order_goods_id'];
        $condition['seller_state']   = array('lt', '3');
        $refund                      = $model_refund->getRefundReturnInfo($condition);
        if ($refund_state == 1 && $data['order_goods_id'] > 0 && empty($refund)) {
            $lock = Redis::lock("refund-posts:".$data['order_id'], 10)->setAutoRelease();
            if (!$lock->get()) {
                throw new Exception('订单退款处理中');
            }

            $refund_array        = array();
            $goods_list          = $order_info['goods_list'];
            $goods_info          = $goods_list[0];
            $goods_pay_price     = $goods_info['goods_pay_price'];//商品实际成交价
            $order_amount        = $order_info['order_amount'];//订单金额

            //实体卡已完成申请售后要上传图片
            if ($order_info['order_type'] == 21 && $order_info['order_state'] == 40 && empty($data['refund_pic'])){
                throw new Exception('请上传凭证');
            }

            //判断周期购主订单中途退款
            if ($order_info['order_type'] == 9 && $order_info['is_head'] == 1){
                $refund_price = Model('cycle_push_info')->getcycleRefundAmount($order_info['order_sn']);
                if ($refund_price > 0){
                    $order_info['refund_amount'] = $refund_price;
                }
                $goods_pay_price = $order_amount - $order_info['refund_amount'];
            }else {
                $order_refund_amount = $order_info['refund_amount'];//订单退款金额
                if ($order_amount < ($goods_pay_price + $order_refund_amount)) {
                    $goods_pay_price = $order_amount - $order_refund_amount;
                }
            }

            $refund_amount = floatval($data['refund_amount']);//退款金额
            if (($refund_amount < 0) || ($refund_amount > $goods_pay_price)) {
                $refund_amount = $goods_pay_price;
            }
            $goods_num = intval($data['goods_num']);//退货数量
            if (($goods_num < 0) || ($goods_num > $goods_info['goods_num'])) {
                $goods_num = 1;
            }
            $reason_list                 = $model_refund->getReasonList(array(), '', '', 'reason_id,reason_info');//退款退货原因
            $refund_array['reason_info'] = '';
            $reason_id                   = intval($data['reason_id']);//退货退款原因
            $refund_array['reason_id']   = $reason_id;
            $reason_array                = array();
            $reason_array['reason_info'] = '其他';
            $reason_list[0]              = $reason_array;
            if (!empty($reason_list[$reason_id])) {
                $reason_array                = $reason_list[$reason_id];
                $refund_array['reason_info'] = $reason_array['reason_info'];
            }

            $pic_array                = array();
            $pic_array['buyer']       = $data['refund_pic'];//上传凭证
            $info                     = serialize($pic_array);
            $refund_array['pic_info'] = $info;

            $model_trade   = Model('trade');
            $order_shipped = $model_trade->getOrderState('order_shipped');//订单状态30:已发货
            if ($order_info['order_state'] == $order_shipped) {
                $refund_array['order_lock'] = '2';//锁定类型:1为不用锁定,2为需要锁定
            }
            $refund_array['refund_type'] = $data['refund_type'];//类型:1为退款,2为退货
            $refund_array['return_type'] = '2';//退货类型:1为不用退货,2为需要退货
            if ($refund_array['refund_type'] != '2') {
                $refund_array['refund_type'] = '1';
                $refund_array['return_type'] = '1';
            }
            $refund_array['seller_state']  = '1';//状态:1为待审核,2为同意,3为不同意
            $refund_array['refund_amount'] = ncPriceFormat($refund_amount);
            $refund_array['goods_num']     = $goods_num;
            $refund_array['buyer_message'] = $data['buyer_message'];
            $refund_array['add_time']      = time();

            if ($order_info['order_type'] == 9 && $order_info['is_head'] == 1){

            }else {
                $erp_refund['order_id'] = $data['order_id'];
                $erp_refund['add_time'] = $refund_array['add_time'];
                $erp_refund['order_sn'] = $order_info['order_sn'];
                $erp_refund['refund_amount'] = $refund_array['refund_amount'];
                $erp_refund_sn = $model_refund->getRefundsn($order_info['store_id']);
                $erp_refund['refund_sn'] = $erp_refund_sn;
                $erp_refund['refund_type'] = $refund_array['refund_type'];
                $erp_refund['reason_info'] = $refund_array['reason_info'];
                $erp_refund['buyer_message'] = $refund_array['buyer_message'];
                $erp_refund['goods_id'] = $goods_info['goods_id'];
                $erp_refund['goods_num'] = $refund_array['goods_num'];
                $erp_refund['order_goods_id'] = $data['order_goods_id'];

                $erp_result = Logic('refund')->refundOrderApply($erp_refund);

                $refund_array['refund_order_sn'] = $erp_result['refund_order_sn'];
            }

            $state = $model_refund->addRefundReturn($refund_array, $order_info, $goods_info, $erp_refund_sn);
            if ($state) {
                if ($order_info['order_state'] == $order_shipped) {
                    $model_refund->editOrderLock($data['order_id']);
                }

                //更新周期购售后状态 如果主订单申请，则同步未发货子订单的状态
                if ($order_info['order_type'] == 9 ) {
                    Model('cycle_push_info')->refundMainOrder($order_info);
                }
                return;
            } else {
                throw new Exception('退款退货申请保存失败');
            }
        } else {
            throw new Exception('参数错误');
        }
    }

    /**
     * 商家退货审核
     *
     * @return void
     */
    public function returnSellerCheck($data = [
        'return' => [],
        'seller_state' => 0,
        'seller_message' => '',
        'return_type' => 0,
    ],$fromCallback = false)
    {
        $return = $data['return'];
        if ($return['seller_state'] != '1') {
            throw new Exception('非待商家审核状态');
        }
        $order_info = Model()->table('orders')->where(['order_id' => $return['order_id']])->find();
        if (empty($order_info)) {
            throw new Exception('找不到关联订单');
        }

        $refund_array['seller_time'] = time();
        // 卖家处理状态:1为待审核,2为同意,3为不同意
        $_POST['seller_state'] = $refund_array['seller_state'] = $data['seller_state'];
        $refund_array['seller_message'] = $data['seller_message'];
        $return['reason_info'] = $data['seller_message'];

        // 同意
        if ($refund_array['seller_state'] == '2' && empty($data['return_type'])) {
            $refund_array['return_type'] = '2';//退货类型:1为不用退货,2为需要退货
//            $refund_array['seller_message'] = '商家已同意退款，请即时将商品寄回';//状态描述

            //会员实体卡 同意 需要判断下这个实体卡健康卡对应的虚拟卡券是否兑换生成了会员
            if ($order_info['order_type'] == 21 && $order_info['order_state'] == 40){
                $info = unserialize($return['pic_info']);
                if(!is_array($info['buyer'])) {
                    showDialog('需要拒绝该申请，提示用户上传健康卡实体卡未卦涂层的照片');
                }

                $state = VipCardOrder::checkVipActivated($order_info['virtual_card_id']);
                 switch ($state){
                     case 0://没有兑换则退货提示
                         $refund_array['seller_message'] = '商家已同意退款，'.C('refund_address');//状态描述
                        break;
                     case 10:
                         showDialog('需要先注销会员身份，才能执行弃货退款');
                         break;
                     case 20:
                         showDialog('实体卡订单对应卡号已激活，售后退货退款勾选同意未选择弃货');
                         break;
                 }
            }

            $erp_result = $this->refundOrder($return, $order_info, false);
            if (!$erp_result) {
                throw new Exception('申请失败，您稍后再申请~~');
            }

            // 退货只有一个退款单，不需要联动
            if (is_wx_live($order_info) && !$fromCallback) {
                SyncTaskQueue::dispatch([
                    'refund_sn' => $return['refund_sn']
                ], 'mini_program', 'shop_ecaftersale_acceptreturn');
            }
        } elseif ($refund_array['seller_state'] == '3') {
            $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
//            $refund_array['seller_message'] = '商家不同意退款';//状态描述

            $erp_result = $this->refundOrder($return, $order_info);
            if (!$erp_result) {
                throw new Exception('申请失败，您稍后再申请~~');
            }
            // 更新周期购订单状态
            if ($order_info['order_type'] == 9) {
                Model('cycle_push_info')->refundRejectMainOrder($order_info);
            }
        } else { // 同意并放货
            if (is_wx_live($order_info)) {
                throw new Exception('视频号订单不允许弃货~~');
            }
            //会员实体卡 同意 需要判断下这个实体卡健康卡对应的虚拟卡券是否兑换生成了会员
            if ($order_info['order_type'] == 21 && $order_info['order_state'] == 40){
                $info = unserialize($return['pic_info']);
                if(!is_array($info['buyer'])) {
                    showDialog('需要拒绝该申请，提示用户上传健康卡实体卡未卦涂层的照片');
                }

                $state = VipCardOrder::checkVipActivated($order_info['virtual_card_id']);
                switch ($state){
                    case 0:
                        showDialog('不能弃货处理，需要用户寄回健康卡卡片，只能选择同意操作');
                        break;
                    case 10:
                        showDialog('需要先注销会员身份，才能执行弃货退款');
                        break;
                }
            }
            $refund_array['seller_state'] = '2';
            $refund_array['refund_state'] = '2';
            $refund_array['return_type'] = '1';//选择弃货
            $refund_array['admin_message'] = '商家已同意退款，待管理员处理';//状态描述
            $erp_result = $this->refundOrder($return, $order_info);
            if (!$erp_result) {
                throw new Exception('申请失败，您稍后再申请~~');
            }
        }

        Model('refund_return')->editRefundReturn([
            'refund_id' => $return['refund_id']
        ], $refund_array);

        if ($data['seller_state'] == '3' && $return['order_lock'] == '2') {
            Model('refund_return')->editOrderUnlock($return['order_id']); // 订单解锁
        }
    }

    /**
     * 客户退货发货
     *
     * @return void
     */
    public function memberReturnShip($data = [
        'refund_id' => 0,
        'express_id' => 0,
        'express_name' => '',
        'invoice_no' => '',
        'buyer_id' => 0,
        'delivery_id' => '',
    ],                               $fromCallback = false)
    {
        $model_refund = Model('refund_return');

        $condition = array();
        $condition['buyer_id'] = $data['buyer_id'];
        $condition['seller_state'] = 2;
        $condition['goods_state'] = ['in', [1, 2]];
        $condition['refund_id'] = intval($data['refund_id']);
        $return = $model_refund->getRefundReturnInfo($condition);

        if (empty($return)) {
            throw new Exception('参数错误');
        }
        $refund_array = array();
        $refund_array['ship_time'] = time();
        $refund_array['delay_time'] = time();
        $refund_array['express_id'] = $data['express_id'];
        $refund_array['invoice_no'] = $data['invoice_no'];
        $refund_array['goods_state'] = '2';
        $param = [];
        $param['express_company_id'] = (int)$_POST['express_id'];
        $param['express_info'] = '用户确认物流发货';
        $param['express_no'] = $_POST['invoice_no'];
        $param['order_id'] = $return['order_sn'];
        $param['refund_sn'] = $return['refund_order_sn'];
        $erp_result = $this->syncExpressinfo($data['buyer_id'], $param);
        if (!$erp_result) {
            throw new Exception('申请失败，您稍后再申请~~');
        }
        $state = $model_refund->editRefundReturn($condition, $refund_array);
        if (!$state) {
            throw new Exception('退款退货申请,发货保存失败');
        }

        $order = Model()->table('orders')->where([
            'order_id' => $return['order_id']
        ])->field('is_live,payment_code')->find();

        // 退货单只有单个，无需联动
        if (!$fromCallback && is_wx_live($order)) {
            $data['refund_sn'] = $return['refund_sn'];
            SyncTaskQueue::dispatch(
                $data, 'mini_program', 'shop_ecaftersale_uploadreturninfo'
            );
        }
    }

    /**
     * @desc 实物订单申请售后
     * @param $refund 退款信息 array
     * @return false|mixed
     * <AUTHOR>
     * @date 2021/3/29
     */
    public function refundOrderApply($refund)
    {
        $model_order = Model("order");
        $order_info = $model_order->getOrderInfo(array("order_id" => $refund["order_id"]), array('order_common'),
            "buyer_id,order_father,buyer_phone,buyer_id,demolition_from,order_id,order_sn,pay_sn,payment_time,payment_code,order_amount,refund_state,chain_id,is_live,shipping_fee");
        if (!$order_info) {
            throw new Exception("未找到订单信息");
        }

        // 拆单中不允许退款，拆单失败可以
        if ($order_info['order_father'] == 0 && !OrderMain::isSplitFail($order_info['order_sn'])) {
            throw new Exception('订单还在拆单的路上，您稍后再申请~~');
        }

        $chain = Chain::getChainColumnList();
        //退款订单数据处理
        $param = $refund_goods = $product_infos = [];

        if ($refund['goods_id'] == 0) {
            $order_goods = $model_order->getOrderGoodsList(['order_id' => $order_info['order_id']], 'rec_id,oc_id,goods_id,goods_commonid,goods_name,goods_spec,goods_price,goods_pay_price,goods_num,sku,is_live');
            if ($order_goods) {
                foreach ($order_goods as $k => $val) {
                    $refund_goods[$k]['sku_id'] = $val['goods_id'];
                    $refund_goods[$k]['order_product_id'] = (int)$val['oc_id'] ?: 0;
                    $refund_goods[$k]['goods_name'] = $val['goods_name'];
                    $refund_goods[$k]['spec'] = $val['goods_spec'] ?: '';
                    $refund_goods[$k]['quantity'] = (int)$val['goods_num'];
                    $refund_goods[$k]['refund_price'] = $order_goods['goods_price'] > 0 ? (float)bcdiv($order_goods['goods_price'], $order_goods['goods_num'], 2) : 0;
                    $refund_goods[$k]['refund_reality_price'] = $val['goods_pay_price'] > 0 ? (float)bcdiv($val['goods_pay_price'], $val['goods_num'], 2) : 0;
                    $refund_goods[$k]['refund_amount'] = (string)$val['goods_pay_price'];

                    if ($order_info['is_live'] == 2) {
                        $product_infos[$k]['out_product_id'] = $val['goods_commonid'];
                        $product_infos[$k]['out_sku_id'] = $val['goods_id'];
                        $product_infos[$k]['product_cnt'] = (int)$val['goods_num'];
                        $product_infos[$k]['pay_amount'] = (int)round($val['goods_pay_price']*100);
                        $product_infos[$k]['order_goods_id'] = $val['rec_id'];
                    }

                }
            }
        } else {
            $order_goods = $model_order->getOrderGoodsInfo(['rec_id' => $refund["order_goods_id"], 'goods_id' => $refund['goods_id']], 'rec_id,oc_id,goods_id,goods_commonid,goods_name,goods_spec,goods_price,goods_pay_price,goods_num,sku,is_live');
            $refund_goods = [[
                'sku_id' => (string)$order_goods['goods_id'],
                'order_product_id' => (int)$order_goods['oc_id'] ?: 0,
                'goods_name' => $order_goods['goods_name'],
                'spec' => $order_goods['goods_spec'] ?: '',
                'quantity' => (int)$order_goods['goods_num'],
                'refund_price' => $order_goods['goods_price'] > 0 ? (float)bcdiv($order_goods['goods_price'], $order_goods['goods_num'], 2) : 0,//单个商品的价格
                'refund_reality_price' => $order_goods['goods_pay_price'] > 0 ? (float)bcdiv($order_goods['goods_pay_price'], $order_goods['goods_num'], 2) : 0,//单个商品实际支付的价格
                'refund_amount' => (string)$refund['refund_amount'],//实际支付的价格*数量
            ]];
            //添加视频号订单售后推送 by zhuoyw
            if ($order_info['is_live'] == 2) {
                $product_infos = [[
                    'out_product_id' => $order_goods['goods_commonid'],
                    'out_sku_id' => $order_goods['goods_id'],
                    'product_cnt' => (int)$order_goods['goods_num'],
                    'pay_amount' => (int)round($order_goods['goods_pay_price']*100),
                    'order_goods_id' => $order_goods['rec_id']
                ]];
            }
        }
        $param['applyOpUserType'] = "1";//1-用户 2-商家 3-客服 4-BD 5-系统 6-开放平台
        $param['channel_id'] = 5;//来源渠道id 1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店
        $param['full_refund'] = $refund['goods_id'] == 0 ? 1 : 2;//1整单退款 2部分退款
        $param['old_refund_sn'] = $refund['refund_sn'];
        $param['order_from'] = 1;//渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家 (废弃))
        $param['order_id'] = $refund['order_sn'];
        $param['pictures'] = $refund['refund_pic'] ? implode(',', $refund['refund_pic']) : '';//退款图片，多个图片url以英文逗号隔开
        $param['reason'] = $refund['reason_info'];//因***原因部分退款
        $param['refund_amount'] = (float)$order_info['order_amount'];//订单退款总金额
        $param['refund_order_goods_data'] = $refund_goods;//部分退款商品sku数据集合的json格式数组
        $param['refund_order_sn'] = '';//售后单号
        $param['refund_remark'] = $refund['buyer_message'];//售后单备注
        $param['refund_type'] = (int)$refund['refund_type'];//申请类型:1为仅退款,2为退款退货
        $param['res_type'] = "0";//申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
        $param['shop_id'] = isset($chain[$vr_order_info['chain_id']]) ? $chain[$order_info['chain_id']] : '';//财务编码

        $res = $this->erpApplyRefundResult($order_info['buyer_id'], $param);

        // 判断视频号申请售后
        if ($res && is_wx_live($order_info)) {
            try {
                Logic('mini_program')->shop_ecaftersale_add([
                    'out_order_id' => $order_info['pay_sn'],
                    'refund_sn' => $refund['refund_sn'],
                    'buyer_id' => $order_info['buyer_id'],
                    'type' => $refund['refund_type'],
                    'refund_reason' => $refund['buyer_message'],
                    'refund_reason_type' => $refund['reason_info'],
                    'product_infos' => $product_infos,
                    'applied' => $_POST[$order_info['pay_sn']],
                    'shipping_fee' => $order_info['shipping_fee']
                ]);
            }catch (Exception $e){
                DatacenterHttp::withAuth($order_info['buyer_id'])
                    ->post( C('datacenter_orderpay_url') . '/order-api/refund/cancel',[
                        'res_type' => '7',
                        'reason' => '同步微信侧失败取消',
                        'refund_order_sn' => $res['refund_order_sn']
                    ]);
                throw $e;
            }
        }

        return $res;
    }

    /**
     * @desc 虚拟订单申请售后
     * @param $refund_id
     * @return bool
     */
    public function refundVrOrderApply($refund_id)
    {
        $model_v_order = Model("vr_order");
        $model_vr_fund = Model('vr_refund');

        //如果传递的是数组，则是订单信息
        if (is_array($refund_id) && !empty($refund_id)) {
            $refund_info = $refund_id;
        } else {
            $refund_info = $model_vr_fund->getRefundInfo(['refund_id' => $refund_id], "order_id,refund_amount,code_sn");
        }
        $order_id = $refund_info['order_id'];

        $field = "oc_id,erp_mobile,erp_order_ids,order_id,order_sn,goods_id,goods_price,goods_num,buyer_phone,buyer_id,goods_name,goods_spec,order_amount,chain_id,erp_order_sn,is_live";
        $vr_order_info = $model_v_order->getOrderInfo(array("order_id" => $order_id), $field, true);
        if (!$vr_order_info) {
            return false;
        }
        //获取门店
        $chain = Chain::getChainColumnList();

        //如果是注销会员卡不用实际退款，则推送金额都为0
        if ($refund_info['apply_type'] == 1){
            $refund_price = $refund_reality_price = 0;
        }else{
            $refund_price = $vr_order_info['goods_price'] > 0 ? (float)bcdiv($vr_order_info['goods_price'], $refund_id['goods_num'], 2) : 0;//单个商品的价格
            $refund_reality_price = $vr_order_info['order_amount'] > 0 ? (float)bcdiv($vr_order_info['order_amount'], $vr_order_info['goods_num'], 2) : 0;
        }

        //组装ERP数据
        $param = [];
        $refund_goods = [[
            'sku_id' => (string)$vr_order_info['goods_id'] ?: '',
            'order_product_id' => (int)$vr_order_info['oc_id'] ?: 0,
            'goods_name' => $vr_order_info['goods_name'],
            'spec' => $vr_order_info['goods_spec'] ?: '',
            'quantity' => (int)$refund_id['goods_num'],//核销码可退款数量
            'refund_price' => $refund_price,//单个商品的价格
            'refund_reality_price' => $refund_reality_price,
            'refund_amount' => (string)$refund_id['refund_amount'],
        ]];
        $param['applyOpUserType'] = "1";//1-用户 2-商家 3-客服 4-BD 5-系统 6-开放平台
        $param['channel_id'] = 5;//来源渠道id 1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店
        $param['full_refund'] = $refund_id['refund_state'] == 2 ? 0 : 1;//1整单退款 2部分退款
        $param['old_refund_sn'] = $refund_id['refund_sn'];
        $param['order_from'] = 1;//渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家 (废弃))
        $param['order_id'] = $vr_order_info['erp_order_sn'] ?: $vr_order_info['order_sn'];
        $param['pictures'] = '';//退款图片，多个图片url以英文逗号隔开
        $param['reason'] = $refund_id['buyer_message'];//因***原因部分退款
        $param['refund_amount'] = (float)$refund_id['refund_amount'];//退款总金额
        $param['refund_order_goods_data'] = $refund_goods;//部分退款商品sku数据集合的json格式数组
        $param['refund_order_sn'] = '';//售后单号
        $param['refund_remark'] = '';//售后单备注
        $param['refund_type'] = 1;//退款类型1为退款,2为退货 目前不支持退货
        $param['res_type'] = "0";//申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
        $param['shop_id'] = isset($chain[$vr_order_info['chain_id']]) ? $chain[$vr_order_info['chain_id']] : '';//财务编码

        try{
            $result = $this->erpApplyRefundResult($vr_order_info['buyer_id'], $param);
        }catch (Exception $e){
            return false;
        }

        $good_info = Model('goods')->getGoodsInfo(array('goods_id' => $vr_order_info['goods_id']), 'goods_id,goods_commonid,goods_serial,goods_sku');

        if ($good_info) {
            Model('vr_order_code')->editOrder(['erp_refund_id' => $result['refund_order_sn']], ['order_id' => $vr_order_info['order_id'], 'vr_code' => ['in', $refund_info['code_sn']]]);
            //判断视频号申请售后
            if ($vr_order_info['is_live'] == 2) {
                $product_infos = [[
                    'out_product_id' => $good_info['goods_commonid'],
                    'out_sku_id' => $good_info['goods_id'],
                    'product_cnt' => (int)$refund_id['goods_num'],
                ]];
                $param['refund_sn'] = $refund_id['refund_sn'];
                $param['product_infos'] = $product_infos;
                VideoOrderAddaftersaleQueue::dispatch($vr_order_info, $param, 1);
            }
        }

        return true;
    }

    /**
     * @desc erp售后申请请求结果处理
     */
    public function erpApplyRefundResult($memberId, $param)
    {
        $param['order_id'] = (string)$param['order_id'];
        $url = C('datacenter_orderpay_url') . '/order-api/refund/apply';
        return DatacenterHttp::withAuth($memberId)->setTimeout(10)->post($url, $param);
    }

    /**
     * @desc 同步数据中心|驳回售后申请
     */
    public function refundReject($memberId,$param)
    {
        try {
            $url = C('erp_after_refund_url').'/boss/ordercenter/order/OrderRefundReject';
            DatacenterHttp::post($url,$param);
            return true;
        }catch (Exception $e){
            return false;
        }
    }

    /**
     * @desc 同步数据中心|同意售后申请
     * @param $memberId
     * @param $param
     * @return bool
     */
    public function refundAgree($memberId, $param)
    {
        try {
            $url = C('erp_after_refund_url').'/boss/ordercenter/order/OrderRefundAgree';
            DatacenterHttp::post($url, $param);
            return true;
        }catch (Exception $e){
            return false;
        }
    }

    /**
     * @desc 实物订单售后数据处理
     * @param $data
     * @return bool
     */
    public function refundOrder($data, $order_info,$needRefund = true)
    {
        $param = [];
        $param['operationer'] = $data['operationer'] ?: '';
        $param['order_id'] = $data['order_sn'];
        $param['reason'] = $data['reason_info'];
        $param['refundGoodsOrders'] = [];//同意 拒绝都不用传
        $param['refundsn'] = $data['refund_order_sn'];
        if ($_POST['admin_state'] == 3 || $_POST['seller_state'] == 3 || $data['admin_state'] == 3) {
            $res = $this->refundReject($order_info['buyer_id'],$param);
            // 拒绝售后，关掉售后单
            if ($res && is_wx_live($order_info)) {
                SyncTaskQueue::dispatch([
                    'refund_sn' => $data['refund_sn'],
                    'member_id' => $order_info['buyer_id'],
                ], 'mini_program', 'shop_ecaftersale_cancel');
            }
        } else if ($_POST['admin_state'] == 2 || $_POST['seller_state'] == 2 || $data['admin_state'] == 2) {
            if ($data['mobile']) {
                $param['open_id'] = $this->getUserOpenId($data['mobile']);
            }
            $res = $this->refundAgree($order_info['buyer_id'],$param);

            if ($res && is_wx_live($order_info) && $needRefund) {
                SyncTaskQueue::dispatch([
                    'refund_sn' => $data['refund_sn'],
                ],'mini_program','shop_ecaftersale_acceptrefund');
            }
        }
        return $res;
    }

    /**
     * @desc 同步批量处理 退款虚拟订单
     * @param $refund
     * @return bool
     */
    public function syncBatchVrOrderRefund($refund, $order_info)
    {
        $model_v_order = Model("vr_order");
        $vr_goodsinfo = $model_v_order->getOrderInfo(array("order_id" => $refund['order_id']), "order_id,erp_order_sn,erp_mobile,buyer_id,buyer_phone,is_live", true);
        if (!$vr_goodsinfo) {
            return false;
        }

        $post_data = [];
        $post_data['operationer'] = $refund['operationer'];
        $post_data['order_id'] = $vr_goodsinfo['erp_order_sn'] ?: $refund['order_sn'];
        $post_data['reason'] = $refund['admin_message'];
        $post_data['refundsn'] = $refund['refund_sn'] ?: $refund['erp_refund_id'];
        $post_data['auto_status'] = $refund['auto_status'];
        //同意退款，ERP发起回调 //审核状态:1为待审核,2为同意,3为不同意
        $updata = array();
        if ($refund['admin_state'] == '2') {
            if ($refund['mobile']) {
                $post_data['open_id'] = $this->getUserOpenId($refund['mobile']);
            }
            $orderrefund = $this->refundAgree($vr_goodsinfo['buyer_id'], $post_data);
            //视频号 13:退款完成
            if ($orderrefund && $vr_goodsinfo['is_live'] == 2) {
                $post_data['status'] = 13;
                VideoOrderUpdateaftersaleQueue::dispatch($order_info, $post_data, 1);
            }
            $updata['erp_order_status'] = 3;

        } elseif ($refund['admin_state'] == '3') {//不同意退款，ERP发起驳回
            $orderrefund = $this->refundReject($vr_goodsinfo['buyer_id'], $post_data);
            //视频号 4:商家拒绝退款,
            if ($orderrefund && $vr_goodsinfo['is_live'] == 2) {
                $post_data['status'] = 4;
                VideoOrderUpdateaftersaleQueue::dispatch($order_info, $post_data, 1);
            }
            $updata['erp_order_status'] = 4;
        }

        if ($orderrefund) {
            $updata['erp_status'] = 1;
            $updata['erp_time'] = time();
            $model_v_order->editOrder($updata, array('order_id' => $vr_goodsinfo['order_id']));
            return true;
        }
        return false;
    }

    /**
     * @desc 退款退货 有初审和终审，初审需要同步物流信息到数据中心
     * <AUTHOR>
     * @date 2021/4/6
     */
    public function syncExpressinfo($memberId, $data)
    {
        try {
            $url = C('datacenter_orderpay_url').'/order-api/order/expressinfoupdate';
            DatacenterHttp::withAuth($memberId)
                ->post($url, $data);
            return true;
        }catch (Exception $e){
            return false;
        }
    }

    public function getUserOpenId($phone)
    {
        $m_info = Model('member')->getMemberInfo(['member_mobile' => $phone], "weixin_mini_openid", false);
        if ($m_info['weixin_mini_openid']) {
            return $m_info['weixin_mini_openid'];
        }
    }


    /*********************************定金预售商家主动退款*********************************************/
    /**
     * 添加定金退款记录
     * @param $order_info
     * @param $arr_pic
     * @return array
     */
    function addRefundReturnForPre($order_info, $arr_pic)
    {
        $refund_array = $goods_info = [];
        if (isset($order_info['goods_id']) && $order_info['goods_id'] > 0){
            $goods_info = Model('goods')->where(['goods_id'=>$order_info['goods_id']])->find();
        }
        $pre_price = (new OrderPresale())->getPrePrice($order_info['order_sn']); // 定金金额
        $refund_array['refund_type'] = '1';//类型:1为退款,2为退货
        $refund_array['seller_state'] = '2';//状态:1为待审核,2为同意,3为不同意
        $refund_array['order_lock'] = '2';//锁定类型:1为不用锁定,2为需要锁定
        $refund_array['refund_state'] = '2';//申请状态:1为处理中,2为待管理员处理,3为已完成,默认为1
        $refund_array['reason_id'] = '0';
        $refund_array['reason_info'] = '商家和用户达成一致';
        $refund_array['buyer_message'] = $refund_array['reason_info'];
        $refund_array['seller_message'] = "同意";
        $refund_array['goods_name'] = '订单商品定金退款';
        $refund_array['refund_amount'] = $pre_price;
        $refund_array['add_time'] = $refund_array['finnshed_time'] = $refund_array['seller_time'] = time();
        $refund_array['pic_info'] = serialize(['buyer'=>$arr_pic]);
        $refund_array['refund_id'] = Model('refund_return')->addRefundReturn($refund_array, $order_info, $goods_info);

        return $refund_array;
    }

    /**
     * 同意预售定金退款
     * @param $refund_detail
     * @return array
     */
    function agreePreRefund($refund_detail, $member_name)
    {
        $result = ['state' => 0, 'msg' => '参数错误，微信退款失败'];
        $model_refund = Model('refund_return');
        $order = $model_refund->getPayDetailInfo($refund_detail);//退款订单详细
        if ($order['pay_refund_amount'] <= 0) { //本次在线退款总金额
            $result['msg'] = '退款失败，退款总金额小于0';
            return $result;
        }
        $use_refund_time = $order['payment_time'] + C('dianyin_allow_time');
        if ($order['payment_code'] <> 'card' && $use_refund_time > time()) {
            $result['msg'] = '操作太快了，5分钟后再来吧~';
            return $result;
        }

        $refund_data = array();
        $refund_data['trade_no'] = $order['trade_no'];
        $refund_data['refund_amount'] = $order['pay_refund_amount'];
        $refund_data['order_type'] = $order['order_type'];
        $extendInfo = ['order_type' => 'r'];
        $refund_data['extendInfo'] = json_encode($extendInfo);
        /**@var dianyin_payLogic $dianyinLogic*/
        $dianyinLogic = Logic('dianyin_pay');
        $dianyin_result = $dianyinLogic->orderRefund($refund_data);
        Redis::lPush('pre_refund_debug', json_encode(['dianyin_pay_'.$refund_detail['refund_id']=>$dianyin_result, 'refund_data'=>$refund_data]));
        $data = $dianyin_result['data'];
        if ($dianyin_result['state'] && in_array($data['rspCode'], ['1', '2', '0'])) {
            $detail_array = array();
            $detail_array['pay_amount'] = ncPriceFormat($data['refundAmt'] / 100);
            $detail_array['pay_time'] = time();
            $result['state'] = 'true';
            $result['msg'] = '已提交申请退款:' . $detail_array['pay_amount'] . "元";

            $refund = $model_refund->getRefundReturnInfo(array('refund_id' => $refund_detail['refund_id']));
            $consume_array = array();
            $consume_array['member_id'] = $refund['buyer_id'];
            $consume_array['member_name'] = $refund['buyer_name'];
            $consume_array['consume_amount'] = $detail_array['pay_amount'];
            $consume_array['consume_time'] = time();
            $consume_array['consume_remark'] = '在线退款成功（到账有延迟），退款退货单号：' . $refund['refund_sn'];
            QueueClient::push('addConsume', $consume_array);//old end

            $refund['admin_state_balance'] = 3;
            $state = $model_refund->editOrderRefund($refund, $member_name);
            if ($state) {
                $refund_array = array();
                $refund_array['admin_time'] = time();
                $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
                $refund_array['admin_message'] = "管理员处理在线退款";
                $refund_array['dy_refund_id'] = $data['refundId'];
                $refund_array['dy_transaction_no'] = $data['transactionNo'];
                if ($data['rspCode'] === '1') {
                    $refund_array['dy_state'] = 2;
                    $refund_array['dy_dealtime'] = time();
                }
                $model_refund->editRefundReturn(array('refund_id' => $refund_detail['refund_id']), $refund_array);

                // 发送买家消息
                $param = array();
                $param['code'] = 'refund_return_notice';
                $param['member_id'] = $refund['buyer_id'];
                $param['param'] = array(
                    'refund_url' => urlShop('member_refund', 'view', array('refund_id' => $refund['refund_id'])),
                    'refund_sn' => $refund['refund_sn']
                );
                $refund['msg'] = '管理员已处理退款，请查收';//状态描述
                $param['param']['mp_array'] = Logic('wx_api')->getTemplateData($param['code'], $refund);
                RealTimePush('sendMemberMsg', $param);

                //添加预售订单定金退款记录
                RefundPreOrder::insert([
                    'order_sn' => $order['erp_order_sn'],
                    'pre_status' => 1,
                    'refund_amount' => ncPriceFormat($data['refundAmt'] / 100),
                    'dy_refund_id' => $data['refundId'],
                    'dy_transaction_no' => $data['transactionNo'],
                    'rsp_code' => $data['rspCode'],
                    'refund_state' => 2,
                    'msg' => $data['msg'],
                ]);
                //尾款退款
                if ($order['last_refund_amount'] > 0) {
                    OrderPresale::dopreRefund($order, $order['last_refund_amount']);
                }
                $result['msg'] = "已提交申请退款，（含定金：{$detail_array['pay_amount']}元,尾款：{$order['last_refund_amount']}元）";

                $this->log('退款确认，退款编号' . $refund['refund_sn']);
                $detail_array['pd_amount'] = 0;
                $detail_array['refund_state'] = 2;
                $model_refund->editDetail(array('refund_id' => $refund_detail['refund_id']), $detail_array);
                $result['state'] = 1;
                $result['msg'] = "退款成功";
            }
        } else {
            if ($data['rspCode'] == "3" || $data['rspCode'] == "0" || $data['rspCode'] == "-1") {
                $result['msg'] = '退款返回信息,' . $data['msg'];//错误描述
            } else {
                $result['msg'] = '退款错误,' . $dianyin_result['msg'];//错误描述
            }
        }
        return $result;
    }
    /**
     * 记录系统日志
     *
     * @param $lang 日志语言包
     * @param $state 1成功0失败null不出现成功失败提示
     * @param $admin_name
     * @param $admin_id
     */
    protected function log($lang = '', $state = 1, $admin_name = '', $admin_id = 0) {
        if (!C('sys_log') || !is_string($lang)) return true;
        if ($admin_name == ''){
            $admin = unserialize(decrypt(cookie('sys_key'),MD5_KEY));
            $admin_name = $admin['name'];
            $admin_id = $admin['id'];
        }
        $data = array();
        if (is_null($state)){
            $state = null;
        }else{
            $state = $state ? '' : L('nc_fail');
        }
        $data['content']    = $lang.$state;
        $data['admin_name'] = $admin_name;
        $data['createtime'] = TIMESTAMP;
        $data['admin_id']   = $admin_id;
        $data['ip']         = getIp();
        $data['url']        = $_REQUEST['act'].'&'.$_REQUEST['op'];
        return Model('admin_log')->insert($data);
    }
    /*********************************定金预售商家主动退款**********************************************/

    /**
     * 售后单更新
     *
     * @param array $data
     * @param bool $fromCallback
     * @return array
     */
    public function refundUpdate($data = [
        'refund_id' => '',
        'buyer_id' => 0,
        'buyer_message' => '',
        'reason_id' => 0,
        'reason_info' => '',
        'return_type' => 0,
        'pic' => []
    ], $fromCallback = false)
    {
        $refundReturn = RefundReturn::where([
            'refund_id'=>$data['refund_id'],
            'buyer_id'=>$data['buyer_id'],
        ])->find();
        if (empty($refundReturn)) {
            return ['status' => 0, 'msg' => '未查询到退款信息'];
        }

        if (!in_array($refundReturn['refund_state'],[1,2])) {
            return ['status' => 0, 'msg' => '仅审核中的售后单允许修改'];
        }

        // 退款原因、退款说明、凭证修改
        if($data['reason_id']){
            $updateData['reason_id'] = intval($data['reason_id']);// 退货退款原因
            if($data['reason_info']){
                $updateData['reason_info'] = $data['reason_info'];
            }
        }

        $updateData['buyer_message'] = $data['buyer_message'];// 退款说明
        $updateData['pic_info'] = serialize(['buyer' => $data['pic']]); //上传凭证;
        $updateData['steps'] = Model('refund_return')->addStep($refundReturn,'买家修改');
        // 更新售后状态
        $res = RefundReturn::where('refund_id', $data['refund_id'])->update($updateData);

        // 从微信回调过来的不联动
        if(!$fromCallback){
            $data['refund_sn'] = $refundReturn['refund_sn'];
            // 同步到视频号
            SyncTaskQueue::dispatch(
                $data,'mini_program','shop_ecaftersale_update'
            );
        }

        if (!$res) {
            return ['status' => 0, 'msg' => '系统繁忙，请稍后再试'];
        }
        return ['status' => 1, 'msg' => '修改成功'];
    }

    // 申请售后取消
    function refundCancel($condition, $extend = [])
    {
        $refundReturn = RefundReturn::where($condition)->find();
        if (empty($refundReturn)) {
            return ['status' => 0, 'msg' => '未查询到售后信息'];
        }
        if (3 == $refundReturn['refund_state']) {
            return ['status' => 0, 'msg' => '已完成的售后单不允许取消'];
        }

        $orderInfo = OrderAlias::where('order_id',$refundReturn['order_id'])->find();
        if(empty($orderInfo)){
            return ['status' => 0, 'msg' => '查询不到订单信息'];
        }
        if ($orderInfo['order_type'] == 9 && $orderInfo['is_head'] == 1){

        }else {
            // 通知数据中心
            $res = $this->refundReject($refundReturn['buyer_id'], [
                'order_id' => $refundReturn['order_sn'],
                'reason' => '用户取消',
                'refundGoodsOrders' => [],
                'refundsn' => $refundReturn['refund_order_sn'],
            ]);
            if (!$res) {
                return ['status' => 0, 'msg' => '退款单同步状态失败'];
            }
        }
        // 订单解锁
        if($refundReturn['order_lock'] == 2){
            Model('refund_return')->editOrderUnlock($refundReturn['order_id']);
        }

        // 通知微信
        if (is_wx_live($orderInfo->toArray())) {
            SyncTaskQueue::dispatch(array_merge([
                'refund_sn' => $refundReturn['refund_sn'],
                'member_id' => $refundReturn['buyer_id'],
            ], $extend), 'mini_program', 'shop_ecaftersale_cancel');
        }

        $updateData = ['refund_state' => 4];
        $updateData['seller_state'] = 3;
        $updateData['steps'] = Model('refund_return')->addStep($refundReturn, '申请关闭');
        // 更新售后状态
        $resUpdate = RefundReturn::where('refund_id', $refundReturn['refund_id'])->update($updateData);
        return ['status' => 1, 'msg' => '取消成功'];
    }
}