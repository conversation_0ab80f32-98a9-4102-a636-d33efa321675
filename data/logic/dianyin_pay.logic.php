<?php

/**
 * 电银支付对接接口
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Curl\Curl;
use Upet\Integrates\Redis\RedisManager;

defined('InShopNC') or exit('Access Invalid!');
class dianyin_payLogic
{

    public $refund_url;
    private $refund_url_new;
    private $refund_query_url;

    public function __construct()
    {
        $this->refund_url = C('dianyin_request_url') . '/pay/refund'; //订单退款
        $this->refund_url_new = C('dianyin_request_url') . '/pay/pay-api/pay/refund'; //新标准支付退款地址
        $this->refund_query_url = C('dianyin_request_url') . '/pay/refundquery'; //退款查询
    }

    /**
     * 获取签名
     *
     * @return string
     */
    public function getSignature($param, $type = 0)
    {
        if (is_null($param) || !is_array($param)) {
            return false;
        }
        ksort($param);
        $string = '';  // 初始化字符串变量
        foreach ($param as $key => $val) {
            if ($val !== "") {
                $string .= $key . '=' . $val . '&';
            }
        }
        $string = rtrim($string, "&"); //echo $string;
        log_info('支付签名1', ['params' => $string,  'action' => __FUNCTION__]);
        if ($type == 4) {
            $string = md5($string) . C('dianyin_pay_key_s2b2c');
        } else {
            $string = md5($string) . C('dianyin_pay_key');
        }
        log_info('支付签名2', ['params' => $string,  'action' => __FUNCTION__]);

        return strtoupper(md5($string));
    }

    /**
     * 下单
     * @param $order_info 订单信息
     * @param int $type 类型 1虚拟2实物
     * @param int $transType 微信支付 1微信 2支付宝
     */
    public function submitOrderToPay($order_info, $type = 1, $transType = 1)
    {
        $param = array();
        $param['transType'] = (int)$transType;
        $param['outTradeNo'] = $order_info['serial_number'] ?: $order_info['order_sn'];
        $param['orderId'] = $order_info['order_sn'];
        $param['payPrice'] = $order_info['order_amount'] * 100;
        $param['discount'] = $order_info['promotion_total'] * 100;
        $param['totalPrice'] = $param['payPrice'] + $param['discount'];
        if ($order_info['openid']) {
            $param['openid'] = $order_info['openid'];
        }
        $param['subAppId'] = $order_info['subAppId'];
        $param['productId'] = $order_info['goods_id'];
        $param['productName'] = $order_info['goods_name'];
        $param['appId'] = $order_info['app_id'];

        if (isset($order_info['order_pay_type'])) {
            $param['orderPayType'] = $order_info['order_pay_type'];
        }

        if ($type == 3) { //云订货请求
            $param['productDesc'] = 'real_order_' . $order_info['order_sn'];
            $param['merchantId'] = $order_info['merchant_id'];
        } else if ($type == 2) { //实物
            $param['productDesc'] = 'real_order_' . $order_info['order_sn'];
            // 电商实物订单走老商户号
            if ($param['appId'] == 7) {
                $param['merchantId'] =  C('dianyin_jcj_merchant_id');
            } else {
                $param['merchantId'] = 'O-' . C('dianyin_merchant_id');
            }
        } else {
            $param['productDesc'] = 'vr_order_' . $order_info['order_sn'];
            $param['merchantId'] = C('dianyin_merchant_id');
        }
        if (get_env() == 'www') {
            $param['offlineNotifyUrl'] =  'https://www.upetmart.com/mobile/api/payment/wxpay_jsapi/notify_url.php';
        } else {
            $param['offlineNotifyUrl'] = MOBILE_SITE_URL . '/api/payment/wxpay_jsapi/notify_url.php';
        }
        $param['clientIP'] = getClientIp();

        if ($order_info['extendInfo']) {
            $param['extendInfo'] = $order_info['extendInfo'];
        }
        // v6.23.0 订单支付增加支付时效为:15分钟-(发起支付ime()-$order_info["add_time"]))/60);
        // v6.23.0 订单支付增加支付时效为:15分钟-(发起支付时间-订单创建时间)

        //如果是预售订单  且 是支付尾款时，
        if (isset($order_info['presale_last_end_time'])) {
            $param['validTime'] = intval(($order_info['presale_last_end_time'] - time()) / 60);
        } else {
            $param['validTime'] = intval((15 * 60 - (time() - $order_info["add_time"])) / 60);
        }

        if ($param['validTime'] <= 0) {
            $param['validTime'] = -1;
        }

        $cacheKey = sprintf('prepay_%s_%s_%s_%.2f', $transType, $param['openid'], $order_info['order_sn'], $param['payPrice']);
        // 微信支付信息需要缓存
        if ($transType == 1 && $cachePrepay = rkcache($cacheKey)) {
            return $cachePrepay;
        }

        if ($param['validTime'] <= 0) {
            $data['state'] = false;
            $data['msg'] = "已过支付时效，请重新下单再发起支付!";
            return $data;
        }

        try {
            $rs = $this->request('pay/unifiedorder', $param, 'post');
            $pay_info = array();
            if ($transType == 1) {  // 微信支付有预支付信息
                $pay_data = $rs['details']['wx_jsapi'];
                $pay_info['appId'] = $pay_data['appId'];
                $pay_info['nonceStr'] = $pay_data['nonceStr'];
                $pay_info['package'] = $pay_data['package'];
                $pay_info['sign'] = $pay_data['paySign'];
                $pay_info['signType'] = $pay_data['signType'];
                $pay_info['timeStamp'] = $pay_data['timeStamp'];
            } elseif ($transType == 10 || $transType == 11) {  // 微信支付有预支付信息,新增支付宝支付
                $pay_info = $rs['details']['wx_js_app'];
                $pay_info['order_id'] = $rs['details']['order_id'];
            } else {
                $pay_info['order_id'] = $rs['details']['order_id'];
            }

            $pay_info['pay_type'] = intval($rs['pay_type']); // pay/unifiedorder 返回的支付方式
            $data['state'] = true;
            $data['data'] = $pay_info;

            wkcache($cacheKey, $data, 4200);
        } catch (Exception $e) {
            $data['state'] = false;
            $data['msg'] = $e->getMessage();
        }

        return $data;
    }

    /**
     * 订单退款 feature-merchant
     * @param $order_info 订单信息
     * @return array
     */
    public function orderRefund($order_info)
    {
        // 百度支付退款
        if ($order_info['payment_code'] == 'bd_pay') {
            return $this->orderRefundForBaiduPay($order_info);
        }

        $param = array();
        if ($order_info['order_type'] == 4) {
            $param['merchantId'] = C('dianyin_merchant_id_s2b2c');
        } else {
            if ($order_info['appId'] == 7) {
                $param['merchantId'] =  C('dianyin_jcj_merchant_id');
            } else {
                $param['merchantId'] = C('dianyin_merchant_id');
            }
        }
        $param['tradeNo'] = $order_info['trade_no'];
        //refund_sn没有传则返回提示
        if (!$order_info['refund'] && !$order_info['refund_sn']) {
            return ['state' => false, 'msg' => '退款单号不能为空'];
        }
        if ($order_info['refund_sn']) {
            $param['refundId'] = $order_info['refund_sn'];
        }
        $param['refundAmt'] = (int)bcmul($order_info['refund_amount'], 100);
        $param['callbackUrl'] = C('mobile_site_url') . '/api/payment/wxpay_jsapi/notify_refundurl.php';
        $param['clientIP'] = getClientIp();
        if ($order_info['extendInfo']) {
            $param['extendInfo'] = $order_info['extendInfo'];
        }
        $param['appId'] = $order_info['appId'] ?: 1;
        $sign = $this->getSignature($param, $order_info['order_type']);
        $param['sign'] = $sign;
        $result = apiRequest($this->refund_url, $param, "post");
        //记录退款请求日志
        log_info('订单退款', ['params' => $param, 'res' => $result, 'action' => __FUNCTION__]);

        $data = array();
        $data['state'] = false;
        if ($result['code'] == '200') {
            $refund_data = $result['data'];
            if ($refund_data['rspCode'] == "2" || $refund_data['rspCode'] == "1" || $refund_data['rspCode'] == "0") {
                //返回状态码 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败  异步通知是否成功
                $refund_info = array();
                $refund_info['transactionNo'] = $refund_data['transactionNo'];
                $refund_info['refundId'] = $refund_data['refundId'];
                $refund_info['refundAmt'] = $refund_data['refundAmt'];
                $refund_info['rspCode'] = $refund_data['rspCode'];
                $refund_info['msg'] = $refund_data['rspMessage'];
                $data['state'] = true;
                $data['data'] = $refund_info;
                return $data;
            } else {
                $data['msg'] = $refund_data['rspMessage'] ?: $result['message'];
            }
        } else {
            $data['msg'] = $result['message'];
        }
        return $data;
    }

    /**
     * 百度支付退款
     * @param $order_info
     * @return array
     */
    function orderRefundForBaiduPay($order_info)
    {
        $param = array(
            'app_id' => 1,
            'refund_amount' => (int)bcmul($order_info['refund_amount'], 100),
            'trade_no' => $order_info['trade_no'],
            'client_ip' => getClientIp(),
            'notify_url' => MOBILE_SITE_URL . '/api/payment/wxpay_jsapi/notify_refundurl.php',
            'merchantId' => C('dianyin_merchant_id'),
            'timestamp' => time() * 1000,
        );
        if ($order_info['extendInfo']) {
            $param['extend_info'] = $order_info['extendInfo'];
        }

        // 签名
        $str = '';
        ksort($param);
        foreach ($param as $key => $val) {
            if ($val == '') continue;
            $str .= ($key . '=' . $val . '&');
        }
        $str .= 'secret=' . C('app_secret_key');
        $param['sign'] = strtoupper(md5($str));

        // 发送请求
        list($code, $response) = http_post_json($this->refund_url_new, json_encode($param), '');
        $result = json_decode($response, true);

        log_info('百度支付退款', ['params' => $param, 'res' => $result, 'action' => __FUNCTION__]);

        $data = array();
        $data['state'] = false;
        if ($code == '200') {
            $refund_data = $result['data'];
            if ($refund_data['result'] == "2" || $refund_data['result'] == "1" || $refund_data['result'] == "0") { //返回状态码 0：未退款 1：退款成功 2：退款处理中 3：退款失败  异步通知是否成功
                $refund_info = array();
                $refund_info['transactionNo'] = $refund_data['third_refund_no'];
                $refund_info['refundId'] = $refund_data['refund_trade_no'];
                $refund_info['refundAmt'] = $refund_data['refund_amount'];
                $refund_info['rspCode'] = $refund_data['result'];
                $refund_info['msg'] = $refund_data['result_msg'];
                $data['state'] = true;
                $data['data'] = $refund_info;
                return $data;
            } else {
                $data['msg'] = $refund_data['result_msg'] ?: $result['message'];
            }
        } else {
            $data['msg'] = $result['message'];
        }
        return $data;
    }

    /**
     * 百度退款查询
     * @param $refund_info
     * @return array
     * @throws Exception
     */
    function orderQueryForBaiduPay($refundInfo)
    {
        $result = apiRequest(C('dianyin_request_url') . '/pay/baidu/order/refund-get', array('trade_no' => $refundInfo['trade_no']));
        $data['state'] = false;
        if ($result['code'] == '200') {
            $refund_data = $result['data'];
            if ($refund_data['refundStatus'] == 2) { //返回状态码 1 退款中 2 退款成功 3 退款失败
                $refund_info = array();
                $refund_info['transactionNo'] = $refund_data['bizRefundBatchId'];
                $refund_info['refundId'] = $refund_data['refundBatchId'];
                $refund_info['refundAmt'] = bcmul($refundInfo['refund_amount'], 100);
                $data['state'] = true;
                $data['data'] = $refund_info;
            } else if ($refund_data['refundStatus'] == 1) {
                $data['msg'] = "退款中";
            }
        } else {
            $data['msg'] = $result['message'];
        }
        log_info('百度退款查询', ['params' => $refundInfo, 'res' => $result, 'action' => __FUNCTION__]);
        return $data;
    }

    /**
     * 查询退款订单
     * @param $refund_info 退款信息
     * @return array
     */
    public function queryOrder($refund_info)
    {
        // 百度支付退款
        if ($refund_info['payment_code'] == 'bd_pay') {
            return $this->orderQueryForBaiduPay($refund_info);
        }

        $param = array();
        $param['merchantId'] = C('dianyin_merchant_id');
        $param['transactionNo'] = $refund_info['dy_transaction_no'];
        $param['refundAmt'] = $refund_info['refund_amount'] * 100;
        $param['refundId'] = $refund_info['dy_refund_id'];
        $param['clientIP'] = getClientIp();
        $sign = $this->getSignature($param);
        $param['sign'] = $sign;

        $result = apiRequest($this->refund_query_url, $param, "post");
        $data = array();
        $data['state'] = false;
        if ($result['code'] == '200') {
            $refund_data = $result['data'];
            if ($refund_data['rspCode'] == "1") { //返回状态码 -1：接口异常 0：未退款 1：退款成功 2：退款处理中 3：退款失败
                $refund_info = array();
                $refund_info['transactionNo'] = $refund_data['transactionNo'];
                $refund_info['refundId'] = $refund_data['refundId'];
                $refund_info['refundAmt'] = $refund_data['refundAmt'];
                $data['state'] = true;
                $data['data'] = $refund_info;
            } else {
                $data['msg'] = $refund_data['rspMessage'];
            }
        } else {
            $data['msg'] = $result['message'];
        }
        return $data;
    }


    /**
     * 异常处理记录/任务计划执行
     * @param int $order_id
     * @param array $info
     * @param string $msg
     * @param string $logname
     */
    public function _error_log($order_id, $info, $msg, $log_name)
    {
        //异常订单处理
        $arr = [];
        $arr["order_id"] = $order_id;
        $arr["info"] = $info;
        $arr['msg'] = $msg;
        $arr['date_time'] = date("Y-m-d H:i:s", time());

        $push = rkcache($log_name);
        if (!$push) {
            $push = [];
        }
        $push[$order_id] = $arr;

        wkcache($log_name, $push);
    }

    /**
     * 支付中心请求处理
     *
     * @param $url
     * @param array $params
     * @param string $method
     * @return mixed
     * @throws Exception
     */
    public function request($url, array $params, $method = 'get')
    {
        if (empty($params['merchantId'])) {
            $params['merchantId'] = C('dianyin_merchant_id');
        }

        $sign = $this->getSignature($params);
        $params['sign'] = $sign;

        $curl = new Curl();
        $curl->setDefaultJsonDecoder(true);

        if (stripos($url, 'http') !== 0) {
            $url = C('dianyin_request_url') . '/' . $url;
        }
        //记录请求日志
        //        log_info($url, $params);

        $rs = $curl->$method($url, $params);

        // 记录错误日志
        if (!is_array($rs) || $rs['code'] != 200) {
            log_error('payment', compact('params', 'rs', 'url'));
        }

        if ($curl->error) {
            throw new Exception('请求支付出错 ' . $curl->errorCode . ': ' . $curl->errorMessage);
        } else {
            if ($rs['code'] != 200) {
                throw new Exception('请求支付出错：' . $rs['message']);
            }

            return $rs;
        }
    }
}
