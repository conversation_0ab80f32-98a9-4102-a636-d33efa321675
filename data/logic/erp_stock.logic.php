<?php
/**
 * 操作ERP库存类
 * <AUTHOR>
 * @date 2018.10.23
 */
defined('InShopNC') or exit('Access Invalid!');
class erp_stockLogic {	
	
 	public function __construct(){
 	   header("Content-Type: text/html;charset=utf-8");
       define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');      
       require_once SCRIPT_ROOT.'/order/'.'stock.php';
    }	

    /**
     * 查询某一商品在各仓的库存情况
     * @param int $GoodsId  商品货号Id
     * @return mixed|multitype:
     */
    public function goodsgetstocks($GoodsId){
    	$param=[];
    	$param['GoodsId']=$GoodsId; //商品货号Id
    
    	$Stock = new Stock();
    	$result=json_decode($Stock->Goodsgetstocks($param),1);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    	 
    }
    
    /**
     * 查询某一商品档案信息
     * @param int $GoodsId 商品货号Id
     * @return mixed|multitype:
     */
    public function goodsinfo($GoodsId){
    	$param=[];
    	$param['GoodsId']=$GoodsId; //商品货号Id
    
    	$Stock = new Stock();
    	$result=json_decode($Stock->Goodsinfo($param),1);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}    	 
    }    
  
    /**
     * 查询某一商品在指定仓库(一个或多个)的商品信息及库存
     * @param int $GoodsId 商品货号Id
     * @param int $deptId 部门Id,多个部门用逗号隔开
     * @return mixed|multitype:
     */
    public function goodsgetstocksinfo($GoodsId,$deptId){ 
    	$param=[];
    	$param['GoodsId']=$GoodsId; //商品货号Id
    	$param['deptId']=$deptId; //部门Id,多个部门用逗号隔开
    	 
    	$Stock = new Stock();
    	$result=json_decode($Stock->GoodsgetStocksInfo($param),1);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    }
     	
 	/**
 	 * 查询指定仓库(一个或多个)所有商品及库存
 	 * @param int $StockId
 	 * @return mixed|multitype:
 	 */
    public function goodsgetstocksid($StockId){
    	$param=[];
    	$param['StockId']=$StockId; //仓库Id    
    	
    	$Stock = new Stock();
    	$result=json_decode($Stock->GoodsgetStocksId($param),1);    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		$data=$message;    		
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    }
    
    /**
     * 同步单个商品平台仓库存（根据单个在门店库存状况）
     * @param int $GoodsId 商品ID
     */
//    public function syncGoodsByChainStock($GoodsId){
//    	$ginfo=Model('goods')->getGoodsInfo(array('goods_id'=>$GoodsId),'*',false);
//    	if(!$ginfo){
//    		return false;
//    	}
//    	if(empty($ginfo['goods_serial'])){
//    		return false;
//    	}
//
//    	$gc_arr=$this->goodsgetstocks($ginfo['goods_serial']);
//    	if(empty($gc_arr)){
//    		return false;
//    	}
//    	//更新库存
//    	foreach ($gc_arr as $v){
//    		//如果是平台仓 更新总库存(根据平台仓)
//    		if($v['deptid']==C('erp_platform_deptId')){
//    			//排除预售、预定
//    			if($ginfo['is_book']==1 || $ginfo['is_presell']==1){
//
//    			}else{
//    				$update_param=array();
//    				$update_param['goods_storage']=$v['stock'];
//    				$update_param['erp_time']=time();
//    				Model('goods')->editGoodsById($update_param,$ginfo['goods_id']);
//    			}
//
//    		}
//
//    	}
//    	return callback(true, '同步完成', 'stock');
//    }

    

    /**
     * 同步某一商品在所有门店信息及库存
     * @param int $GoodsId 商品ID 
     * 
     */
    public function syncChainGoodsByStock($GoodsId){      
    		  	
    	$chain_list = Model('chain')->where(array('chain_state'=>1,'store_id'=>1))->order('store_id desc')->select(); //array('in','1')     	
    	$ginfo=Model('goods')->getGoodsInfo(array('goods_id'=>$GoodsId),'*',false);
    	if(!$ginfo){
    		return false;
    	}
    	/* //查询门店库存表（关联门店）
    	$model=Model();
    	$field="chain_stock.*,chain.chain_erp_id";
    	$on="chain_stock.chain_id=chain.chain_id";
    	$model->table("chain_stock,chain")->field($field);
    	$stock_list=$model->join("left")->on($on)->select(); */
    	
    	//组装门店列表    	
    	$deptId_arr=array();
    	$deptId="";
    	foreach ($chain_list as $v){
    		array_push($deptId_arr, $v['chain_erp_id']);
    	}
    	if(!empty($deptId_arr)){
    		$deptId=implode(",", $deptId_arr);
    	}    
    	
    	//获取ERP数据
    	$gs_arr=$this->goodsgetstocksinfo($ginfo['goods_serial'],$deptId);    	
    	if(empty($gs_arr)){
    		return false;
    	}
    	//更新库存
    	$data=[];
    	foreach ($gs_arr as $v){
    		
    		//如果是平台仓 更新总库存(根据平台仓)
    		if($v['deptid']==C('erp_platform_deptId')){
    			//排除预售、预定
    			if($ginfo['is_book']==1 || $ginfo['is_presell']==1){
    				    				
    			}else{
    				$update_param=array();
    				$update_param['goods_storage']=$v['stock'];
    				$update_param['erp_time']=time();
    				Model('goods')->editGoodsById($update_param,$ginfo['goods_id'],$ginfo['store_id']);
    			}
    			
    		}
    		//更新存在的门店库存
    		$model_chain=Model("chain")->where(array('chain_state'=>1,'store_id'=>1,'chain_erp_id'=>$v['deptid']))->find();
    		if(!$model_chain){
    			continue;
    		}
    		$model_stock=Model('chain_stock')->where(array('chain_id'=>$model_chain['chain_id'],'goods_id'=>$ginfo['goods_id']))->find();
    		if(!$model_stock){
    			$insert = array();
    			$insert['chain_id']     = $model_chain['chain_id'];
    			$insert['goods_id']   = $ginfo['goods_id'];
    			$insert['goods_commonid']   = $ginfo['goods_commonid'];
    			$insert['stock']   = $v['stock'];
    			$insert['chain_price']   = $v['minsaleprice'];
    			$insert['cate_id']   = 0;
    			$insert['p_cate_id']   = 0;
    			$insert['goods_salenum']   = 0;
    			$insert['erp_time']   = time();
    			array_push($data, $insert);
    			continue;
    		}
    		$updata = array();
    		$updata['stock']    =$v['stock'];  
    		$updata['chain_price']   = $v['minsaleprice'];
    		$updata['erp_time']=time();
    		Model('chain_stock')->where(array('chain_id'=>$model_chain['chain_id'],'goods_id'=>$ginfo['goods_id']))->update($updata);
    	}
    	if(!empty($data)){
    		Model('chain_stock')->insertAll($data);
    	}
    	return callback(true, '同步完成', 'stock');
    }
    
    
    /**
     * 同步某一商品在指定门店信息及库存
     * @param int $GoodsId 商品ID
     * @param int $deptId  门店ID
     */
    public function syncChainGoodsByDeptId($GoodsId,$deptId){ 
    	    	
    	$ginfo=Model('goods')->getGoodsInfo(array('goods_id'=>$GoodsId),'*',false);
    	if(!$ginfo){
    		return false;
    	}
    	$gs_arr=$this->goodsgetstocksinfo($ginfo['goods_serial'],$deptId);
    	if(empty($gs_arr)){
    		return false;
    	}
    	//更新库存
    	$data=[];
    	foreach ($gs_arr as $v){    		
    		//更新存在的门店库存
    		$model_chain=Model("chain")->where(array('chain_state'=>1,'store_id'=>1,'chain_erp_id'=>$v['deptid']))->find();
    		if(!$model_chain){
    			continue;
    		}
    		$model_stock=Model('chain_stock')->where(array('chain_id'=>$model_chain['chain_id'],'goods_id'=>$ginfo['goods_id']))->find();
    		if(!$model_stock){
    			$insert = array();
    			$insert['chain_id']     = $model_chain['chain_id'];
    			$insert['goods_id']   = $ginfo['goods_id'];
    			$insert['goods_commonid']   = $ginfo['goods_commonid'];
    			$insert['stock']   = $v['stock'];
    			$insert['chain_price']   = $v['minsaleprice'];
    			$insert['cate_id']   = 0;
    			$insert['p_cate_id']   = 0;
    			$insert['goods_salenum']   = 0;
    			$insert['erp_time']   = time();
    			array_push($data, $insert);
    			continue;
    		}
    		$updata = array();
    		$updata['stock']    =$v['stock'];
    		$updata['chain_price']   = $v['minsaleprice'];
    		$updata['erp_time']=time();
    		Model('chain_stock')->where(array('chain_id'=>$model_chain['chain_id'],'goods_id'=>$ginfo['goods_id']))->update($updata);
    	}
    	if(!empty($data)){
    		Model('chain_stock')->insertAll($data);
    	}
    	return callback(true, '同步完成', 'stock');
    }
    
	/**
	 * 同步单个仓库所有商品（接口速度慢，暂不用）
	 * @param int $StockId 仓库ID
	 */
	public function syncgoodsgetstocksid($StockId){		
		$gsk_arr=$this->goodsgetstocksid($StockId);
		//更新库存
		$data=[];
		foreach ($gsk_arr as $v){		

			//更新存在的门店库存
			/* $model_chain=Model("chain")->where(array('chain_state'=>1,'store_id'=>1,'chain_erp_id'=>$v['deptid']))->find();
			if(!$model_chain){
				continue;
			}
			$model_stock=Model('chain_stock')->where(array('chain_id'=>$model_chain['chain_id'],'goods_id'=>$v['articlenumber']))->find();
			if(!$model_stock){			
				continue;
			}
			$updata = array();
			$updata['stock']    =$v['stock'];
			$updata['chain_price']   = $v['minsaleprice'];
			$updata['erp_time']=time();
			Model('chain_stock')->where(array('chain_id'=>$model_chain['chain_id'],'goods_id'=>$ginfo['goods_id']))->update($updata); */
			
			
		}
		if(!empty($data)){
			Model('chain_stock')->insertAll($data);
		}
		return callback(true, '同步完成', 'stock');
		
		
	}	
    
}
