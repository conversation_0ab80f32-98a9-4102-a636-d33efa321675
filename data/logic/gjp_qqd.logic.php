<?php
/**
 * 管家婆全渠道对接接口
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
defined('InShopNC') or exit('Access Invalid!');
class gjp_qqdLogic {
    private $appkey;
    private $appsecret;
    public function __construct(){
        //parent::__construct('express');
        return true;
        $this->appkey = C('gjp_appkey');
        $this->appsecret = C('gjp_appsecret');
    }

    /**
     * 获取管家婆code
     *
     * @return string
     */
    public function getAuthorizeCode() {
        return rkcache('gjp_code');
    }

    /**
     * 获取管家婆token
     */

    public function getGjpToken() {
        $token = rkcache("gjp_token");
        if ($token) {
            return $token;
        }else {
            $data = array(
                'appkey' => C('gjp_appkey'),
                'appsecret' => C('gjp_appsecret'),
                'oauthcode' => $this->getAuthorizeCode()
            );
            $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=emall.token.get";
            $result = $this->httpPost($url,$data,true);
            if ($result['code'] == 0){
                $token = $result['token'];
                if ($token) {
                    wkcache("gjp_token",$token);
                }
                return $token;
            }
        }
    }

    /**
     * 查询商品信息获取
     * @param page int 当前页索引（从 1 开始）
     * @param pagesize int 每页条数（不超过 100 ）
     * @param type int 0：阿里商品（商品erpid为空）1：线下ERP商品（已保存erpid）
     * @param goodscode array 商品编码 eg. ["帆布鞋", "硫化鞋"]
     * @param goodsname array 商品名称  eg. ["002", "33"]
     * @return  array 商品信息
     */
    public function getGoodsInfos($page = 1,$pagesize = 20,$type = 1,$goodscode = array(),$goodsname = array()) {
        $appkey = C('gjp_appkey');
        $appsecret = C('gjp_appsecret');
        $method = "emall.goods.get";
        $token = $this->getGjpToken();
        $time = date('Y-m-d H:i:s');
        $bodyArr = array(
            'page' => $page,
            'pagesize' => $pagesize,
            'returntype' => $type,
            'goodscode'  => $goodscode,
            'goodsname'  => $goodsname
        );
        $signStr = "$appsecret"."app_key".$appkey.'formatjsonmethod'.$method.'sign_methodmd5timestamp'.$time.'token'.$token.'v1.0'.json_encode($bodyArr)."$appsecret";
        $sign = strtoupper(md5($signStr));
        $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=".$method."&timestamp=".urlencode($time)."&format=json&app_key=".$appkey."&token=".$token."&v=1.0&sign=".$sign."&sign_method=md5";
        $result = $this->httpPost($url,$bodyArr,true);
        if ($result["code"] == 0) {
            return $this->toData($result["code"],$result["goods"]);
        } else {
            return $this->toData($result["code"],"",$result['message']);
        }
    }

    /**
     * 查询管家婆订单订单状态
     * @param $order_sn 商城订单编号
     * @return array
     */
    public function getGjpOrderStatus($order_sn) {
        $method = "emall.orderstatus.get";
        $token = $this->getGjpToken();
        $time = date('Y-m-d H:i:s');
        $data[] = $order_sn;
        $bodyArr = array(
            'ordercodes' => $data
        );
        $signStr = "$this->appsecret"."app_key".$this->appkey.'formatjsonmethod'.$method.'sign_methodmd5timestamp'.$time.'token'.$token.'v1.0'.json_encode($bodyArr)."$this->appsecret";
        $sign = strtoupper(md5($signStr));
        $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=".$method."&timestamp=".urlencode($time)."&format=json&app_key=".$this->appkey."&token=".$token."&v=1.0&sign=".$sign."&sign_method=md5";
        $result = $this->httpPost($url,$bodyArr,true);
        if ($result["code"] == 0) {
            return $this->toData($result["code"],$result["ordersstatus"][0]);
        } else {
            return $this->toData($result["code"],"",$result['message']);
        }
    }

    /**
     * 向管家婆添加订单
     * @param $order_info   订单信息
     * @return array
     * @throws Exception
     */
    public function addOrderToGjp($order_info,$type = 1) {
    	//如果存在管家婆订单号直接返回
    	if($order_info['billcode'] && $type == 1){
    		return $this->toData(0,$order_info['billcode']);
    	}
    	
        $model_order = Model('order');
        $orderCommon = $model_order->getOrderCommonInfo(array('order_id'=>$order_info['order_id']),"order_message,reciver_info,promotion_total,reciver_name,invoice_info");
        $orderCommon['reciver_info'] = unserialize($orderCommon['reciver_info']);
        $orderCommon['invoice_info'] = unserialize($orderCommon['invoice_info']);
        $method = "emall.order.synchronize";
        $token = $this->getGjpToken();
        $time = date('Y-m-d H:i:s');
        //,goods_serial eshopskuid
        $field = "rec_id oid,goods_barcode barcodes,goods.goods_id eshopgoodsid,goods.goods_name eshopgoodsname,goods_num num,order_goods.goods_price payment,order_goods.goods_image picpath";
        $order_goods_list = $model_order->getOrderGoodsListToGjp(array('order_id'=>$order_info['order_id']),$field);
        if (is_array($order_goods_list) && !empty($order_goods_list)) {
            foreach ($order_goods_list as $key => $val) {
                $order_goods_list[$key]['payment'] = $val['payment'] * $val['num'];
                $order_goods_list[$key]['eshopskuid'] = "";
                $order_goods_list[$key]['picpath'] = cthumb($val['picpath'],'60');
                $order_goods_list[$key]['eshopskuname'] = "";
                $order_goods_list[$key]['weight'] = "0";
                $order_goods_list[$key]['size'] = "0";
                $order_goods_list[$key]['unitid'] = "0";
            }
        }
        $orderCommonPromotionTotal = $orderCommon['promotion_total'] ? $orderCommon['promotion_total'] : 0;
        if($order_info['order_type'] == 4) {//拼团订单
            $totalMoney = $order_info['goods_amount'] + $orderCommon['promotion_total'];
            $promotion_total = $order_info['goods_amount'] - $order_info['order_amount'] + $orderCommonPromotionTotal;
        } else {
            $totalMoney = $order_info['order_amount'] + $orderCommon['promotion_total'];
            $promotion_total = $orderCommon['promotion_total'];
        }
        $area = $orderCommon['reciver_info']['area'];
        $area_arr = explode(" ",$area);
        $orders[] = array(
            'tid' => $order_info['order_sn'],
            'weight' => '0',
            'size' => '0',
            'buyernick' => $order_info['buyer_name'],
            'buyermessage' => $orderCommon['order_message'],
            'sellermemo' => '',
            'total' => $totalMoney,
            'privilege' => $promotion_total,
            'postfee' => $order_info['shipping_fee'],
            'receivername' => $orderCommon['reciver_name'],
            'receiverstate' => $area_arr[0] ? $area_arr[0] : $orderCommon['reciver_info']['address'],
            'receivercity' => $area_arr[1] ? $area_arr[1] : $orderCommon['reciver_info']['address'],
            'receiverdistrict' => $area_arr[2] ? $area_arr[2] : $orderCommon['reciver_info']['address'],
            'receiveraddress' => $orderCommon['reciver_info']['street'] ? $orderCommon['reciver_info']['street'] : $orderCommon['reciver_info']['address'],
            'receiverphone' => $orderCommon['reciver_info']['tel_phone'],
            'receivermobile' => $orderCommon['reciver_info']['mob_phone'] ? $orderCommon['reciver_info']['mob_phone'] : $orderCommon['reciver_info']['phone'],
            'created' => date("Y-m-d H:i:s",$order_info['add_time']),
            'paytime' => $order_info['payment_time'] > 0 ? date("Y-m-d H:i:s",$order_info['payment_time']) : "",
            'status' => "Payed",
            'type' => "NoCod",
            'invoicename' => $orderCommon['invoice_info'][抬头] ? $orderCommon['invoice_info'][抬头] : "",
            'details' => $order_goods_list
        );
        $bodyArr = array(
            'orders' => $orders
        );
        $signStr = "$this->appsecret"."app_key".$this->appkey.'formatjsonmethod'.$method.'sign_methodmd5timestamp'.$time.'token'.$token.'v1.0'.json_encode($bodyArr)."$this->appsecret";
        $sign = strtoupper(md5($signStr));
        $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=".$method."&timestamp=".urlencode($time)."&format=json&app_key=".$this->appkey."&token=".$token."&v=1.0&sign=".$sign."&sign_method=md5";
        $result = $this->httpPost($url,$bodyArr,true);
        if ($result["code"] === 0) {
            $billcode = $result['orders'][0]["billcode"];
            if ($billcode) {
                $result = $model_order->editOrder(array('billcode'=> $billcode,'gjp_time' => strtotime($time)),array('order_id'=>$order_info['order_id']));
                if (!$result) {
                    throw new Exception('订单更新失败');
                }
            }
            return $this->toData($result["code"],$result['orders'][0]);
        } else {
            //if ($result["code"] == -1) {
                $this->_error_log($order_info['order_id'], $order_info, "提交管家婆全渠道订单失败", "syns_addgjp_order_error_list");
            //}
            return $this->toData($result["code"],"",$result['message']);
        }
    }

    /**
     * 向管家婆添加售后订单
     * @param $order_info 订单信息
     */
    public function afterorderToGjp($refund_id,$status = 0) {
        $method = "emall.afterorder.synchronize";
        $token = $this->getGjpToken();
        $time = date('Y-m-d H:i:s');
        $model_order = Model('order');
        $model_refund = Model('refund_return');
        $refundReturnInfo = $model_refund->getRefundReturnInfo(array('refund_id' => $refund_id),"order_id,order_sn,refund_sn,goods_id,order_goods_id,goods_name,goods_num,refund_amount,refund_type,admin_message,reason_info",true);
        $order_info=$model_order->getOrderInfo(array("order_id"=>$refundReturnInfo['order_id']));
        $orderCommon = $model_order->getOrderCommonInfo(array('order_id'=>$order_info['order_id']),"order_message,reciver_info,promotion_total,reciver_name,invoice_info");
        $refundStatus = array(
            '0' => 'WaitAgree',
            '1' => 'Agree',
            '2' => 'Refuse',
            '3' => 'Invalid',
            '4' => 'UnFinish',
            '5' => 'Finished',
            '6' => 'AgreeInvalid',
            '7' => 'RefuseInvalid',
        );
        if ($refundReturnInfo['refund_type'] == 1) {
            $aftsaletype = "JustRefund";//仅退款
        } else {
            $aftsaletype = "RefundAndGoods";//退款退货
        }
        if ($refundReturnInfo['goods_id'] > 0) {
            $order_goods_list[] = array(
                'oid' => $refundReturnInfo['order_goods_id'],
                'eshopgoodsname' => $refundReturnInfo['goods_name'],
                'backqty' => $refundReturnInfo['goods_num'] ? $refundReturnInfo['goods_num'] : 1,
                'backtotal' => $refundReturnInfo['refund_amount'],
                'eshopskuname' => "",
                'outeriid' => ""
            );
        } else {
            $field = "rec_id oid,goods.goods_name eshopgoodsname,order_goods.goods_num backqty,order_goods.goods_pay_price backtotal";
            $order_goods_list = $model_order->getOrderGoodsListToGjp(array('order_id'=>$order_info['order_id']),$field);
            if (is_array($order_goods_list) && !empty($order_goods_list)) {
                foreach ($order_goods_list as $key => $val) {
                    $order_goods_list[$key]['eshopskuname'] = "";
                    $order_goods_list[$key]['outeriid'] = "";
                }
            }
        }
        if($order_info['order_type'] == 4) {//拼团订单
            $totalMoney = $order_info['goods_amount'] + $orderCommon['promotion_total'];
            $promotion_total = $order_info['goods_amount'] - $order_info['order_amount'];
        } else {
            $totalMoney = $order_info['order_amount'] + $orderCommon['promotion_total'];
            $promotion_total = $orderCommon['promotion_total'];
        }
        $orders[] = array(
            'tid' => $refundReturnInfo['order_sn'],
            'rtid' => $refundReturnInfo['refund_sn'],
            'total' => $totalMoney,
            'privilege' => $promotion_total,
            'postfee' => $order_info['shipping_fee'],
            'created' => date("Y-m-d H:i:s",$order_info['add_time']),
            'status' => $refundStatus[$status],
            'aftsaletype' => $aftsaletype,
            'logistbillcode' => $order_info['shipping_code'],
            'aftsaleremark' => $refundReturnInfo['reason_info'].$refundReturnInfo['admin_message'],
            'details' => $order_goods_list
        );
        $bodyArr = array(
            'orders' => $orders
        );
        $signStr = "$this->appsecret"."app_key".$this->appkey.'formatjsonmethod'.$method.'sign_methodmd5timestamp'.$time.'token'.$token.'v1.0'.json_encode($bodyArr)."$this->appsecret";
        $sign = strtoupper(md5($signStr));
        $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=".$method."&timestamp=".urlencode($time)."&format=json&app_key=".$this->appkey."&token=".$token."&v=1.0&sign=".$sign."&sign_method=md5";
        $result = $this->httpPost($url,$bodyArr,true);
        if ($result["code"] == 0) {
            $billcode = $result['orders'][0]["billcode"];
            if ($billcode) {
                $result = $model_refund->editRefundReturn(array('refund_id' => $refund_id),array('billcode'=> $billcode,'gjp_time' => strtotime($time)));
                if (!$result) {
                    throw new Exception('售后单更新失败');
                }
            } else {//更新失败
                $this->_error_log($refund_id, $status, "提交管家婆全渠道售后订单失败", "syns_addgjp_afterorder_error_list");
            }
            return $this->toData($result["code"],$result['orders'][0]);
        } else {
            if ($result["code"] == -1) {
                $this->_error_log($refund_id, $status, "提交管家婆全渠道售后订单失败", "syns_addgjp_afterorder_error_list");
            }
            return $this->toData($result["code"],"",$result['message']);
        }
    }

    /**
     * 添加积分订单到管家婆
     * @param $order_info   积分订单信息
     * @return array
     */
    public function addPointOrderToGjp($order_info) {
        $pointOrderModel = Model('pointorder');
        $order_id = $order_info['point_orderid'];
        $condition['point_orderid'] = $order_id;
        $field = "point_orderid,point_ordersn,point_buyername,point_ordermessage,point_orderstate,point_addtime";
        #$order_info= $pointOrderModel->getPointOrderInfo($condition,$field);
        $addresInfo = $pointOrderModel->getPointOrderAddressInfo($condition);
        $goodsField = "points_ordergoods.point_recid oid,points_ordergoods.point_goodsid eshopgoodsid,points_ordergoods.point_goodsname eshopgoodsname,points_ordergoods.point_goodsnum num,points_ordergoods.point_goodsimage picpath";
        #"points_ordergoods.*,points_goods.pgoods_name,points_goods.pgoods_serial,points_goods.pgoods_price,points_goods.pgoods_serial eshopskuid"
        $order_goods_list = $pointOrderModel->getOrderGoodsAndGoodsList(array("points_ordergoods.point_orderid"=> $order_id),$goodsField);
        if (is_array($order_goods_list) && !empty($order_goods_list)) {
            foreach ($order_goods_list as $key => $val) {
                $order_goods_list[$key]['barcode'] = "";
                $order_goods_list[$key]['eshopskuid'] = "";
                $order_goods_list[$key]['payment'] = 0;
                $order_goods_list[$key]['picpath'] = pointprodThumb($val['picpath'], 'small');
                $order_goods_list[$key]['eshopskuname'] = "";
                $order_goods_list[$key]['weight'] = "0";
                $order_goods_list[$key]['size'] = "0";
                $order_goods_list[$key]['unitid'] = "0";
                $order_goods_list[$key]['unitqty'] = "0";
            }
        }
        $area = $addresInfo['point_areainfo'];
        $area_arr = explode(" ",$area);
        $orders[] = array(
            'tid' => "p_".$order_info['point_ordersn'],
            'weight' => '0',
            'size' => '0',
            'buyernick' => $order_info['point_buyername'],
            'buyermessage' => $order_info['point_ordermessage'],
            'sellermemo' => '',
            'total' => 0,
            'privilege' => 0,
            'postfee' => 0,
            'receivername' => $addresInfo['point_truename'],
            'receiverstate' => $area_arr[0] ? $area_arr[0] : $addresInfo['point_address'],
            'receivercity' => $area_arr[1] ? $area_arr[1] : $addresInfo['point_address'],
            'receiverdistrict' => $area_arr[2] ? $area_arr[2] : $addresInfo['point_address'],
            'receiveraddress' => $addresInfo['point_address'] ? $addresInfo['point_address'] : $addresInfo['point_address'],
            'receiverphone' => $addresInfo['point_telphone'],
            'receivermobile' => $addresInfo['point_mobphone'] ? $addresInfo['point_mobphone'] : $addresInfo['point_mobphone'],
            'created' => date("Y-m-d H:i:s",$order_info['point_addtime']),
            'paytime' => $order_info['point_addtime'] > 0 ? date("Y-m-d H:i:s",$order_info['point_addtime']) : "",
            'status' => "Payed",
            'type' => "NoCod",
            'invoicename' => "",
            'details' => $order_goods_list
        );
        $bodyArr = array(
            'orders' => $orders
        );
        $method = "emall.order.synchronize";
        $token = $this->getGjpToken();
        $time = date('Y-m-d H:i:s');
        $signStr = "$this->appsecret"."app_key".$this->appkey.'formatjsonmethod'.$method.'sign_methodmd5timestamp'.$time.'token'.$token.'v1.0'.json_encode($bodyArr)."$this->appsecret";
        $sign = strtoupper(md5($signStr));
        $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=".$method."&timestamp=".urlencode($time)."&format=json&app_key=".$this->appkey."&token=".$token."&v=1.0&sign=".$sign."&sign_method=md5";
        $result = $this->httpPost($url,$bodyArr,true);
        if ($result["code"] == 0) {
            $billcode = $result['orders'][0]["billcode"];
            if ($billcode) {
                $result = $pointOrderModel->editPointOrder(array('point_orderid'=>$order_info['point_orderid']),array('billcode'=> $billcode,'gjp_time' => strtotime($time)));
                if (!$result) {
                    throw new Exception('订单更新失败');
                }
            }
            return $this->toData($result["code"],$result['orders'][0]);
        } else {
            if ($result["code"] == -1) {
                $this->_error_log("p_".$order_info['point_orderid'], $order_info, "提交管家婆全渠道订单失败", "syns_addgjp_order_error_list");
            }
            return $this->toData($result["code"],"",$result['message']);
        }
    }

    /**
     * 添加管家婆订单失败 异常处理记录/任务计划执行
     * @param int $order_id
     * @param array $info
     * @param string $msg
     * @param string $logname
     */
    public function _error_log($order_id,$info,$msg,$log_name){
        //异常订单处理
        $arr=[];
        $arr["order_id"] = $order_id;
        $arr["info"] = $info;
        $arr['msg'] = $msg;
        $arr['date_time'] = date("Y-m-d H:i:s",time());

        $push=rkcache($log_name);
        if(!$push){
            $push=[];
        }
        $push[$order_id]=$arr;

        wkcache($log_name, $push);
    }

    /**
     * 获取管家婆库存数量
     * @param int $page 当前页索引（从 1 开始）
     * @param int $pagesize 每页条数（不超过 100 ）
     * @param string $skucode   规格编码 例如：002,003
     * @param string $goodscode 商品编码（多个商品编码请以 ”,“ 分隔， 例如：002,003,004 ）
     * @return  array 商品库存信息
     */

    public function getGjpStockCounts($goodscode = "",$page = 1,$pagesize = 20,$skucode = ""){
        $appkey = C('gjp_appkey');
        $appsecret = C('gjp_appsecret');
        $method = "emall.stock.get";
        $token = rkcache("gjp_token");;
        $time = date('Y-m-d H:i:s');
        $bodyArr = array(
            'page' => $page,
            'pagesize' => $pagesize,
            'skucode' => $skucode,
            'goodscode' => $goodscode
        );
        $signStr = "$appsecret"."app_key".$appkey.'formatjsonmethod'.$method.'sign_methodmd5timestamp'.$time.'token'.$token.'v1.0'.json_encode($bodyArr)."$appsecret";
        $sign = strtoupper(md5($signStr));
        $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=".$method."&timestamp=".urlencode($time)."&format=json&app_key=".$appkey."&token=".$token."&v=1.0&sign=".$sign."&sign_method=md5";
        $result = $this->httpPost($url,$bodyArr,true);
        if ($result["code"] == 0) {
            return $this->toData($result["code"],$result["stocks"]);
        } else {
            return $this->toData($result["code"],"",$result['message']);
        }

    }

    /** 更新订单状态
     * @param int $ordersn  订单编号
     * @param int $orderStatus  订单状态 NoPay = 0未付款 Payed = 1已付款 Sended = 2已发货 TradeSuccess = 3交易成功 TradeClosed = 4交易关闭 PartSend = 5部分发货
     * @param int $refundStatus 退款状态 Normal = 1正常 Refunding = 2退款中（客户点击退款）RefundSuccess = 3退款成功（同意退款）RefundClosed = 4退款关闭（客户取消退款或者拒绝退款）
     *
     */
    public function updateOrderStatus($ordersn,$orderStatus = 1,$refundStatus = 1){
        $method = "emall.orderstatus.synchronize";
        $token = $this->getGjpToken();
        $time = date('Y-m-d H:i:s');
        $data[] = array(
            'tid' => $ordersn,
            'status' => $this->getOrderStatus($orderStatus),
            'refundstatus' => $this->getOrderRefundStatus($refundStatus)
        );
        $bodyArr = array(
            'orders' => $data
        );
        $signStr = "$this->appsecret"."app_key".$this->appkey.'formatjsonmethod'.$method.'sign_methodmd5timestamp'.$time.'token'.$token.'v1.0'.json_encode($bodyArr)."$this->appsecret";
        $sign = strtoupper(md5($signStr));
        $url = C('gjp_apisurl')."/Service/ERPService.asmx/EMallApi?method=".$method."&timestamp=".urlencode($time)."&format=json&app_key=".$this->appkey."&token=".$token."&v=1.0&sign=".$sign."&sign_method=md5";
        $result = $this->httpPost($url,$bodyArr,true);
        if ($result["code"] == 0) {
            return $this->toData($result["code"],$result["orders"][0]);
        } else {
            if ($result["code"] == -1) {
                $statusInfo = $orderStatus."_".$refundStatus;
                $this->_error_log($ordersn, $statusInfo, "更新管家婆全渠道订单状态失败", "syns_updategjp_orderstatus_error_list");
            }
            return $this->toData($result["code"],"",$result['message']);
        }
    }

    /**
     *
     * @param int $status 订单状态  status NoPay = 未付款 Payed = 已付款 Sended = 已发货 TradeSuccess = 交易成功 TradeClosed = 交易关闭 PartSend = 部分发货
     * @return string 订单状态标识
     */
    private function getOrderStatus($status = 1) {
        switch ($status) {
            case 0 :
                $orderStatus = "NoPay";
                break;
            case 2 :
                $orderStatus = "Sended";
                break;
            case 3 :
                $orderStatus = "TradeSuccess";
                break;
            case 4 :
                $orderStatus = "TradeClosed";
                break;
            case 5 :
                $orderStatus = "PartSend";
                break;
            default:
                $orderStatus = "Payed";
                break;
        }
        return $orderStatus;
    }

    /**
     * @param int $status 退款状态 Normal = 正常 Refunding = 退款中（客户点击退款）RefundSuccess = 退款成功（同意退款）RefundClosed = 退款关闭（客户取消退款或者拒绝退款）
     */
    private function getOrderRefundStatus($status = 1) {
        switch ($status) {
            case 2 :
                $refundStatus = "Refunding";
                break;
            case 3 :
                $refundStatus = "RefundSuccess";
                break;
            case 4 :
                $refundStatus = "RefundClosed";
                break;
            default:
                $refundStatus = "Normal";
                break;
        }
        return $refundStatus;
    }

    private function toData($code,$result,$msg = "调用成功") {
        $data['code'] = $code;
        $data['message'] = $msg;
        $data['data'] = $result;
        return $data;
    }
    /**
     *  post提交数据
     * @param  string $url 请求Url
     * @param  array $data 提交的数据
     * @param  bool $json 数据格式
     * @return url响应返回的json
     */
    private function httpPost($url, $data = NULL, $json = false)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if (!empty($data)) {
            if($json && is_array($data)){
                $data = json_encode( $data );
            }
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            if($json){ //发送JSON数据
                curl_setopt($curl, CURLOPT_HTTPHEADER,
                    array(
                        'Content-Type: application/json; charset=utf-8',
                        'Content-Length:' . strlen($data))
                );
            }
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $res = curl_exec($curl);
        $errorno = curl_errno($curl);
        if ($errorno) {
            return array('errorno' => false, 'errmsg' => $errorno);
        }
        curl_close($curl);
        return json_decode($res, true);
    }

}
