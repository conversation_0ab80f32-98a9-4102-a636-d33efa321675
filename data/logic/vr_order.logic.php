<?php
/**
 * 虚拟订单行为
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Log;
use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Models\Datacenter\MemberPropertyGuaranteeQuota;
use Upet\Models\Goods;
use Upet\Models\GoodsCommonLive;
use Upet\Models\Member as MemberAlias;
use Upet\Models\Order as OrderAlias;
use Upet\Modules\Order\Actions\VipOrderPaidAction;
use Upet\Modules\Order\Events\MixOrderCanceled;
use Upet\Modules\Order\Events\MixOrderPaid;
use Upet\Modules\Order\Queues\SyncDatacenterOrderPayQueue;

defined('InShopNC') or exit('Access Invalid!');
class vr_orderLogic {

    /**
     * 取消订单
     * @param array $order_info
     * @param string $role 操作角色 buyer、seller、admin、system 分别代表买家、商家、管理员、系统
     * @param string $msg 操作备注
     * @param boolean $if_queue 是否使用队列
     * @param boolean $dcSync 是否同步数据中心状态，针对退款取消不需要同步
     * @return array
     */
    public function changeOrderStateCancel($order_info, $role, $msg, $if_queue = true, $dcSync = true) {
        try {
            $model_vr_order = Model('vr_order');
            $model_vr_order->beginTransaction();

            $lock = Redis::lock('vrChangeOrderStateCancel:' . $order_info['order_id'], 30)
                ->setAutoRelease();
            if (!$lock->get()) {
                throw new Exception('请求处理中');
            }

            $_info = $model_vr_order->table('vr_order')->where(array('order_id'=> $order_info['order_id']))->master(true)->find();
            if ($_info['order_state'] == ORDER_STATE_CANCEL) {
                throw new Exception('参数错误');
            }

            //库存、销量变更
            //为保证数据准确，不使用队列
            $goods = Model('goods')->where(['goods_id'=>$order_info['goods_id']])->field('goods_commonid')->find();
            $goods_sale[$order_info['goods_id']] = $goods['goods_commonid'];
            $result = Logic('queue')->cancelOrderUpdateStorage(array(
                $order_info['goods_id'] => $order_info['goods_num']),
                $goods_sale,
                $order_info['store_id']
            );
            if (!$result['state']) {
                Log::record("还原库存失败\n".$result['msg'] , Log::RUN);
                // throw new Exception('还原库存失败');
            }

            $model_pd = Model('predeposit');

            //解冻充值卡
            $pd_amount = floatval($order_info['rcb_amount']);
            if ($pd_amount > 0) {
                $data_pd = array();
                $data_pd['member_id'] = $order_info['buyer_id'];
                $data_pd['member_name'] = $order_info['buyer_name'];
                $data_pd['amount'] = $pd_amount;
                $data_pd['order_sn'] = $order_info['order_sn'];
                $model_pd->changeRcb('order_cancel',$data_pd);
            }

            //解冻预存款
            $pd_amount = floatval($order_info['pd_amount']);
            if ($pd_amount > 0) {
                $data_pd = array();
                $data_pd['member_id'] = $order_info['buyer_id'];
                $data_pd['member_name'] = $order_info['buyer_name'];
                $data_pd['amount'] = $pd_amount;
                $data_pd['order_sn'] = $order_info['order_sn'];
                $model_pd->changePd('order_cancel',$data_pd);
            }
            //更新订单信息
            $update_order = array(
                    'order_state' => ORDER_STATE_CANCEL,
                    'pd_amount' => 0,
                    'close_time' => TIMESTAMP,
                    'close_reason' => $msg
            );
            $update = $model_vr_order->editOrder($update_order,array('order_id'=>$order_info['order_id'],'order_state' => array('neq', ORDER_STATE_CANCEL)));
            if (!$update) {
                throw new Exception('保存失败');
            }
            if (!empty($order_info['chain_id'])) {
                Model('chain_voucher')->returnVrVoucher($order_info['order_id']);
            }
            $model_vr_order->commit();

            // 待支付
            if(!$_info['payment_time']) {
                MemberPropertyGuaranteeQuota::freezeRelease($_info['order_sn']);
            }

            if ($order_info['is_use_virtual_stock']){
                Goods::virtualStockIncrBy($order_info['goods_id'], intval($order_info['goods_num']),$order_info['store_id']);
            }

            if($dcSync){
                //取消ERP虚拟订单
                define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
                require_once SCRIPT_ROOT.'/order/'.'order.php';
                require_once SCRIPT_ROOT.'/base/'.'member.php';
                $vr_logic=Logic('erp_order');

//            $res = $vr_logic->syncOrderCancell($order_info['order_id']);
                $res = $vr_logic->noPayCancelOrder($order_info['order_sn']);
                if (!$res) {
                    throw new Exception('ERP释放库存失败');
                }
            }

            event(new MixOrderCanceled($order_info, OrderAlias::TYPE_V));

            return callback(true,'更新成功');

        } catch (Exception $e) {
            $model_vr_order->rollback();
            return callback(false,$e->getMessage());
        }
    }

    /**
     * 支付订单
     * @param array $order_info
     * @param string $role 操作角色 buyer、seller、admin、system 分别代表买家、商家、管理员、系统
     * @param string $post
     * @return array
     */
    public function changeOrderStatePay($order_info, $role, $post) {
        //redis 加锁操作
        $redis_logic = Logic('redis');
        $redis_get = $redis_logic->redisLock('out_trade_no_vr'.$order_info['pay_sn']);
        if(!$redis_get){
            return callback(true,'不可重复请求');
        }
        try {

            $model_vr_order = Model('vr_order');
            $model_vr_order->beginTransaction();
            $_info = $model_vr_order->getOrderInfo(array('order_id'=>$order_info['order_id']), 'order_state', true,true);
            if ($_info['order_state'] == ORDER_STATE_PAY) {
                return callback(true,'更新成功');
            }
            $model_pd = Model('predeposit');
            //下单，支付被冻结的充值卡
            $rcb_amount = floatval($order_info['rcb_amount']);
            if ($rcb_amount > 0) {
                $data_pd = array();
                $data_pd['member_id'] = $order_info['buyer_id'];
                $data_pd['member_name'] = $order_info['buyer_name'];
                $data_pd['amount'] = $rcb_amount;
                $data_pd['order_sn'] = $order_info['order_sn'];
                $model_pd->changeRcb('order_comb_pay',$data_pd);
            }

            //下单，支付被冻结的预存款
            $pd_amount = floatval($order_info['pd_amount']);
            if ($pd_amount > 0) {
                $data_pd = array();
                $data_pd['member_id'] = $order_info['buyer_id'];
                $data_pd['member_name'] = $order_info['buyer_name'];
                $data_pd['amount'] = $pd_amount;
                $data_pd['order_sn'] = $order_info['order_sn'];
                $model_pd->changePd('order_comb_pay',$data_pd);
            }

            //更新订单状态
            $update_order = array();
            $update_order['order_state'] = ORDER_STATE_PAY;
            $update_order['payment_time'] = $post['payment_time'] ? strtotime($post['payment_time']) : TIMESTAMP;

            // 198会员购买
            if (Goods::isVipGood($order_info['goods_id'])) {
                action(new VipOrderPaidAction($order_info, $update_order));
            }

            $update_order['payment_code'] = $post['payment_code'];
            if(C('dianyin_pay')) {
                $update_order['payment_from'] = 1;
            }
            $update_order['trade_no'] = $post['trade_no'];

            if(isset($post['is_live'])){
                $update_order['is_live'] = $post['is_live'];
            }

            $update = $model_vr_order->editOrder($update_order,array('order_id'=>$order_info['order_id'],'order_state'=>ORDER_STATE_NEW));
            if (!$update) {
                throw new Exception(L('nc_common_save_fail'));
            }

            $order_info = array_merge($order_info, $update_order);
            //发放兑换码
           /* $insert = $model_vr_order->addOrderCode($order_info);
            if (!$insert) {
                throw new Exception('兑换码生成失败');
            }*/

            //订单支付成功修改分销员绑定关系
            $distribute_logic = Logic('distribute');
            $distribute_logic->editDisMemberFans($order_info['buyer_id']);
            // 支付成功发送买家消息
            $param = array();
            $param['code'] = 'order_payment_success';
            $param['member_id'] = $order_info['buyer_id'];
            $param['param'] = array(
                    'order_sn' => $order_info['order_sn'],
                    'order_url' => urlShop('member_vr_order', 'show_order', array('order_id' => $order_info['order_id']))
            );
            $param['param']['mp_array'] = Logic('wx_api')->getTemplateData($param['code'],$order_info);
            RealTimePush('sendMemberMsg', $param);

            // 支付成功发送店铺消息
            $param = array();
            $param['code'] = 'new_order';
            $param['store_id'] = $order_info['store_id'];
            $param['param'] = array(
                    'order_sn' => $order_info['order_sn']
            );
            RealTimePush('sendStoreMsg', $param);

            //发送兑换码到手机
            $param = array('order_id'=>$order_info['order_id'],'buyer_id'=>$order_info['buyer_id'],'buyer_phone'=>$order_info['buyer_phone'],'goods_name'=>$order_info['goods_name']);
            RealTimePush('sendVrCode', $param);

            // 触发订单支付时间
            event(new MixOrderPaid($order_info, OrderAlias::TYPE_V));

            $model_vr_order->commit();

            //订单支付处理完成，删除redis 订单记录
            $redis_logic->delRedisLock('out_trade_no_vr'.$order_info['pay_sn']);

            //通知数据中心订单已支付
            SyncDatacenterOrderPayQueue::dispatch($order_info, $update_order, 2,$order_info['encrypt_mobile']);

            return callback(true, '更新成功');

        } catch (Exception $e) {
            $model_vr_order->rollback();

            //订单支付处理失败，删除redis 记录，从新走流程
            $redis_logic->delRedisLock('out_trade_no_vr'.$order_info['pay_sn']);

            return callback(false,$e->getMessage());
        }
    }

    /**
     * 完成订单(如果全部兑换完成)
     * @param int $order_id
     * @return array
     */
    public function changeOrderStateSuccess($order_id) {
        $model_vr_order = Model('vr_order');
        $condition = array();
        $condition['vr_state'] = 0;
        $condition['refund_lock'] = array('in',array(0,1));
        $condition['order_id'] = $order_id;
        $condition['vr_indate'] = array('gt',TIMESTAMP);
        $order_code_info = $model_vr_order->getOrderCodeInfo($condition,'*',true);
        if (empty($order_code_info)) {
            $update = $model_vr_order->editOrder(array('order_state' => ORDER_STATE_SUCCESS,'finnshed_time' => TIMESTAMP), array('order_id' => $order_id));
            if (!$update) {
                callback(false,'更新失败');
            }
            $order_info = $model_vr_order->getOrderInfo(array('order_id'=>$order_id));
            //添加会员积分
            if (C('points_isuse') == 1){
                Model('points')->savePointsLog('order',array('pl_memberid'=>$order_info['buyer_id'],'pl_membername'=>$order_info['buyer_name'],'orderprice'=>$order_info['order_amount'],'order_sn'=>$order_info['order_sn'],'order_id'=>$order_info['order_id']),true);
            }

            //添加会员经验值
            Model('exppoints')->saveExppointsLog('order',array('exp_memberid'=>$order_info['buyer_id'],'exp_membername'=>$order_info['buyer_name'],'orderprice'=>$order_info['order_amount'],'order_sn'=>$order_info['order_sn'],'order_id'=>$order_info['order_id']),true);

            if ($order_info['is_live'] == 2) {
                //
            }

            if($order_info['order_type'] == 10){
                Model('member')->editMember(
                    ['member_id' => $this->canceled->order['buyer_id']],
                    ['newcomer_tag' => MemberAlias::NEWCOMER_OLD]
                );
            }
        }

        return callback(true,'更新成功');
    }
}
