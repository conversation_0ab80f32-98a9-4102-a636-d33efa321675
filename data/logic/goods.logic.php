<?php
/**
 * 商品
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Upet\Models\DcActivity\WatermarkGoods;
use Upet\Models\DcGroupBuyProduct;
use Upet\Models\GoodsGroup;
use Upet\Models\GoodsProduct;
use Upet\Models\PTime;
use Upet\Models\PXianshiGoods;
use Upet\Models\UserLevel;
use Upet\Modules\Goods\Queues\SyncMiniShopDownQueue;
use Upet\Modules\Goods\Queues\CycleBuyCollectQueue;
use Upet\Models\Eshop\GoodsEshop;
use Upet\Models\GoodsHandleLog;

defined('InShopNC') or exit('Access Invalid!');

class goodsLogic
{

    public function __construct()
    {
        Language::read('member_store_goods_index');
    }

    public function saveLibGoods($param, $store_id, $store_name, $store_state, $seller_id, $seller_name, $bind_all_gc){
        $model_goods = Model('goods');
        $count = $model_goods->getGoodsCommonCount(array());
        if ($count >= C('sg_goods_limit') && C('sg_goods_limit') != 0) {
            return callback(false, '发布商品数量已超过最大总商品数量，请不要继续添加');
        }
        $common_array = $param;
        unset($common_array['goods_img']);
        unset($common_array['g_storage']);
        $common_array['goods_state'] = ($store_state != 1) ? 0 : 1;            // 店铺关闭时，商品下架
        $common_array['goods_addtime'] = TIMESTAMP;
        $common_array['goods_selltime'] = 0;
        $common_array['goods_verify'] = 1;
        $common_array['store_id'] = $store_id;
        $common_array['store_name'] = $store_name;
        $common_array['spec_name'] = serialize(null);
        $common_array['spec_value'] = serialize(null);
        $common_array['areaid_1'] = 0;
        $common_array['areaid_2'] = 0;
        $common_array['goods_vat'] = 0;
        $common_array['goods_commend'] = 0;
        $common_array['g_search_status'] = 0;
        $common_array['is_virtual'] = 0;
        $common_array['virtual_indate'] = 0;
        $common_array['virtual_limit'] = 0;
        $common_array['virtual_invalid_refund'] = 0;
        $common_array['goods_storage_alarm'] = 0;
        $common_array['goods_inv'] = 0;
        $common_array['is_own_shop'] = in_array($store_id, Model('store')->getOwnShopIds()) ? 1 : 0;
        $common_id = $model_goods->addGoodsCommon($common_array);
        if (!$common_id) {
            return callback(false, '商品添加失败');
        }
        // 生成商品返回商品ID(SKU)数组
        $goodsid_array = $this->_addGoods($param, $common_id, $common_array);
        $image_list = array();
        if (!empty($param['goods_img'])) $image_list = unserialize($param['goods_img']);
        $img_array = array();
        $tmp_insert = array();
        $tmp_insert['goods_commonid'] = $common_id;
        $tmp_insert['store_id'] = $store_id;
        $tmp_insert['color_id'] = 0;
        $tmp_insert['goods_image'] = $param['goods_image'];
        $tmp_insert['goods_image_sort'] = 0;
        $tmp_insert['is_default'] = 1;
        $img_array[] = $tmp_insert;
        $tmp_insert['is_default'] = 0;
        if (!empty($image_list) && is_array($image_list)) {
            foreach ($image_list as $key => $value) {
                if (!empty($value)) {
                    $tmp_insert['goods_image'] = $value;
                    $img_array[] = $tmp_insert;
                }
            }
        }
        $model_goods->addGoodsImagesAll($img_array);

        // 生成商品二维码
        if (!empty($goodsid_array)) {
            QueueClient::push('createGoodsQRCode', array('store_id' => $store_id, 'goodsid_array' => $goodsid_array));
        }

        //商品加入消费者保障服务更新队列
        Model('cron')->addCron(array('exetime' => TIMESTAMP, 'exeid' => $common_id, 'type' => 9), true);

        // 记录日志
        $this->_recordLog('添加商品，SPU:' . $common_id, $seller_id, $seller_name, $store_id);


        $video_file_url = BASE_UPLOAD_PATH.DS.ATTACH_GOODS.'/0/goods_video/'.$param['goods_video'];
        $video_aim_url = BASE_UPLOAD_PATH.DS.ATTACH_GOODS.DS.$store_id.'/goods_video/'.$param['goods_video'];
        if ($param['goods_video']) {
            $fileUtil = new FileUtil();
            $fileUtil->copyFile($video_file_url,$video_aim_url,false);
        }

        return callback(true, '', $common_id);
    }


    public function saveGoods($param, $store_id, $store_name, $store_state, $seller_id, $seller_name, $bind_all_gc) {
        $model_goods = Model('goods');
        $count = $model_goods->getGoodsCommonCount(array());
        if ($count >= C('sg_goods_limit') && C('sg_goods_limit') != 0) {
            return callback(false, '发布商品数量已超过最大总商品数量，请不要继续添加');
        }
        
        // 验证参数
        $error = $this->_validParam($param);
        if ($error != '') {
            return callback(false, $error);
        }

        $gc_id = intval($param['cate_id']);
        // 验证商品分类是否存在且商品分类是否为最后一级
        $data = Model('goods_class')->getGoodsClassForCacheModel();
        if (!isset($data[$gc_id]) || isset($data[$gc_id]['child']) || isset($data[$gc_id]['childchild'])) {
            return callback(false, '您选择的分类不存在，或没有选择到最后一级，请重新选择分类。');
        }
        
        // 三方店铺验证是否绑定了该分类
        if (!checkPlatformStoreBindingAllGoodsClass($store_id, $bind_all_gc)) {
            $where = array();
            $where['class_1|class_2|class_3'] = $gc_id;
            $where['store_id'] = $store_id;
            $rs = Model('store_bind_class')->getStoreBindClassInfo($where);
            if (empty($rs)) {
                return callback(false, '您的店铺没有绑定该分类，请重新选择分类。');
            }
        }

        // 根据参数初始化通用商品数据
        $common_array = $this->_initCommonGoodsByParam($param, $store_id, $store_name, $store_state); //echo '<pre>';print_r($common_array);exit;
        // 生成通用商品返回通用商品编号
        $common_res = $model_goods->addGoodsCommon($common_array);
        if (!$common_res) {
            return callback(false, '商品添加失败');
        }
        $common_id = $common_array['goods_commonid'];
        //组合商品添加
        if ($common_array['goods_type'] == 1){
            GoodsGroup::addGoodsGroup($param['goods_id'], $common_array);
        }

        // 商品多图保存
        if(!empty($param['image_all'])) {
            $this->_imageAll($common_id, $store_id, $param['image_all'], $common_array['goods_image']);
        }

        // 生成商品返回商品ID(SKU)数组
        $goodsid_array = $this->_addGoods($param, $common_id, $common_array);

        // 生成商品二维码
        if (!empty($goodsid_array)) {
            Model('chain_stock')->addChainGoods($goodsid_array,$common_id);
            QueueClient::push('createGoodsQRCode', array('store_id' => $store_id, 'goodsid_array' => $goodsid_array));
        }

        //通知中心已认领
        if (isset($param['goods_id']) && $param['goods_id']){
            Model('basedata_relation')->insert(array('data_id'=>$common_id, 'data_type'=>'goods', 'data_uuid'=>$param['goods_id']));
            $apiURL = BOSS_CENTER_ADDR . "/boss/product/product/edit-isuse";
            $params = array('id'=>$param['goods_id']);
            $result = apiRequest($apiURL, $params);
            if ($result){

            }
        }

        // 商品加入上架队列
        if (isset($param['starttime'])) {
            $selltime = strtotime($param['starttime']) + intval($param['starttime_H'])*3600 + intval($param['starttime_i'])*60;
            if ($selltime > TIMESTAMP) {
                Model('cron')->addCron(array('exetime' => $selltime, 'exeid' => $common_id, 'type' => 1), true);
            }
        }

        //商品加入消费者保障服务更新队列
        Model('cron')->addCron(array('exetime' => TIMESTAMP, 'exeid' => $common_id, 'type' => 9), true);

        //记录商品操作日志 给数据中心用
        $data_handle_log = array();
        $data_handle_log['gh_good_ids'] = implode(",",$goodsid_array);
        $data_handle_log['gh_notes'] = "添加商品";
        $data_handle_log['gh_addtime'] = time();
        $data_handle_log['store_id'] = $store_id;
        $model_goods->addGoodsHandlelog($data_handle_log);

        // 记录日志
        $this->_recordLog('添加商品，SPU:'.$common_id, $seller_id, $seller_name, $store_id);

        return callback(true, '', $common_id);
    }
    
    public function updateGoods($param, $store_id, $store_name, $store_state, $seller_id, $seller_name, $bind_all_gc) {
        $model_goods = Model('goods');

        $common_id = intval($param['commonid']);
        if ($common_id <= 0) {
            return callback(false, '商品编辑失败');
        }
        // 验证参数
        $error = $this->_validParam($param);
        if ($error != '') {
            return callback(false, $error);
        }

        $gc_id = intval($param['cate_id']);
        // 验证商品分类是否存在且商品分类是否为最后一级
        $data = Model('goods_class')->getGoodsClassForCacheModel();
        if (!isset($data[$gc_id]) || isset($data[$gc_id]['child']) || isset($data[$gc_id]['childchild'])) {
            return callback(false, '您选择的分类不存在，或没有选择到最后一级，请重新选择分类。');
        }
        
        // 三方店铺验证是否绑定了该分类
        if (!checkPlatformStoreBindingAllGoodsClass($store_id, $bind_all_gc)) {
            $where = array();
            $where['class_1|class_2|class_3'] = $gc_id;
            $where['store_id'] = $store_id;
            $rs = Model('store_bind_class')->getStoreBindClassInfo($where);
            if (empty($rs)) {
                return callback(false, '您的店铺没有绑定该分类，请重新选择分类。');
            }
        }

        // 根据参数初始化通用商品数据
        $common_array = $this->_initCommonGoodsByParam($param, $store_id, $store_name, $store_state);

        // 接口不标记字段
        if (APP_ID == 'mobile') {
            unset($common_array['brand_id']);
            unset($common_array['brand_name']);
            unset($common_array['mobile_body']);
            unset($common_array['plateid_top']);
            unset($common_array['plateid_bottom']);
            unset($common_array['sup_id']);
        }
        // 更新商品数据
        $data = $this->_editGoods($param, $common_id, $common_array, $store_id);
        if (isset($data['state']) && !$data['state']){
            return $data;
        }
        extract($data);

        // 清理商品数据
        $model_goods->delGoods(array('goods_id' => array('not in', $goodsid_array), 'goods_commonid' => $common_id, 'store_id' => $store_id));
        // 清理商品图片表
        $model_goods->delGoodsImages(array('goods_commonid' => $common_id, 'color_id' => array('not in', $colorid_array)));

        //如果上架，则更新门店商品状态
        if ($common_array['goods_state'] == 1){
            Model('chain_stock')->editCommonGoods([$common_id], false);//上架
        }else{
            // 下架移除限时折扣
           PXianshiGoods::where('end_time','>',time())->whereIn('goods_id',$goodsid_array)->where('store_id',$_SESSION['store_id'])->delete();
           PTime::where('promotion_type','2')->whereIn('goods_id',$goodsid_array)->where('end_time','>',time())->where('store_id',$_SESSION['store_id'])->delete();
        }

        // 商品加入上架队列
        if (isset($param['starttime'])) {
            $selltime = strtotime($param['starttime']) + intval($param['starttime_H'])*3600 + intval($param['starttime_i'])*60;
            if ($selltime > TIMESTAMP) {
                Model('cron')->addCron(array('exetime' => $selltime, 'exeid' => $common_id, 'type' => 1), true);
            }
        }
        
        if ($common_array['is_virtual'] == 1) {
            // 如果是特殊商品清理促销活动，团购、限时折扣、组合销售
            QueueClient::push('clearSpecialGoodsPromotion', array('goods_commonid' => $common_id, 'goodsid_array' => $goodsid_array, 'store_id' => $store_id));
        } else {
            // 更新商品促销价格
            QueueClient::push('updateGoodsPromotionPriceByGoodsCommonId', ['goods_commonid'=>$common_id,'store_id'=>$store_id]);
        }
        
        $return = $model_goods->editGoodsCommon($common_array, array('goods_commonid' => $common_id, 'store_id' => $store_id));
        if (!$return) {
            return callback(false, '商品编辑失败');
        }
        GoodsGroup::editGoodsGroup($param['commonid'], $common_array);

        // 生成商品二维码
        if (!empty($goodsid_array)) {
            QueueClient::push('createGoodsQRCode', array('store_id' => $store_id, 'goodsid_array' => $goodsid_array));
        }
        //记录商品操作日志 给数据中心用
        $data_handle_log = array();
        $data_handle_log['gh_good_ids'] = implode(",",$goodsid_array);
        $data_handle_log['gh_notes'] = "编辑或添加商品";
        $data_handle_log['gh_addtime'] = time();
        $data_handle_log['store_id'] = $store_id;
        $model_goods->addGoodsHandlelog($data_handle_log);
        // 记录日志
        $this->_recordLog('编辑商品，SPU:'.json_encode(['commonid'=>$common_id,'validity_type'=>$common_array['validity_type'],'virtual_indate'=>$common_array['virtual_indate']]), $seller_id, $seller_name, $store_id);
        
        return callback(true, '', $common_id);
    }

    /**
     * 验证参数
     */
    private function _validParam($param) {
        $obj_validate = new Validate();
        $obj_validate->validateparam = array(
            array (
                "input" => $param["g_name"],
                "require" => "true",
                "message" => L('store_goods_index_goods_name_null')
            ),
            array (
                "input" => $param["g_price"],
                "require" => "true",
                "validator" => "Double",
                "message" => L('store_goods_index_goods_price_null')
            )
        );
        if (isset($param['validity_type'])) {
            if ($param['validity_type'] == "1") { //指定时间
                $gVindate = strtotime($param['g_vindate']);
                $maxVindate = strtotime(date('Y-m-d') . " +364 day");
                if ($gVindate > $maxVindate) {
                    return "兑换有效期的指定时间不能超过" . date("Y-m-d", $maxVindate);
                }
            } else if ($param['validity_type'] == '2') { //相对时间
                if ($param['o_vindate'] > 364) {
                    return "兑换有效期的相对时间不能超过364天";
                }
            }
        }

        return $obj_validate->validate();
    }

    /**
     * 根据参数初始化通用商品数据
     */
    private function _initCommonGoodsByParam($param, $store_id, $store_name, $store_state) {
        // 分类信息
        $goods_class = Model('goods_class')->getGoodsClassLineForTag(intval($param['cate_id']));
        
        if(!empty($param['sp_val']) && is_array($param['sp_val'])) {
            foreach($param['sp_name'] as $k => $v) {
                if(empty($param['sp_val'][$k])) unset($param['sp_name'][$k]);
            }
        }
        //敏感词过滤
        $model_setting = Model('setting');
        $sensitive_info = $model_setting->getRowSetting('sensitive_set');
        if ($sensitive_info !== false) {
            $sensitive_list = @unserialize($sensitive_info['value']);
        }
        if (!is_array($sensitive_list)) {
            $sensitive_list = array();
        }
        foreach ($sensitive_list as $v) {
            switch($v['name']) {
                case 'goods_name':
                    $goods_name = $v['is_open'];
                    break;
                case 'goods_jingle':
                    $goods_jingle = $v['is_open'];
                    break;
                case 'goods_body':
                    $goods_body = $v['is_open'];
                    break;
                case 'short_name':
                    $short_name = $v['is_open'];
                    break;
            }
        }
        //开关
        if($goods_name == 1){
            $param['g_name'] = sensitiveWordFilter($param['g_name']);
        }
        if($short_name == 1){
            $param['short_name'] = sensitiveWordFilter($param['short_name']);
        }
        if($goods_jingle == 1){
            $param['g_jingle'] = sensitiveWordFilter($param['g_jingle']);
        }
        if($goods_body == 1){
            $param['g_body'] = sensitiveWordFilter($param['g_body']);
        }

        $common_array = array();
        if (isset($param['goods_id']) && $param['goods_id']) {
            $common_array['goods_commonid'] = $param['goods_id'];
        }elseif (!$param['commonid']){
            $goods_commonid = Model('goods')->getAutoGoodsCommonID();
            if ($goods_commonid){
                $common_array['goods_commonid'] = $goods_commonid;
            }
        }
        $param['goods_id'] = $param['goods_id']?$param['goods_id']:$param['commonid'];

        if($param['goods_id']){
            $product_info = GoodsProduct::where(['id'=>$param['goods_id']])->field('product_type,group_type,is_intel_goods')->find();
            $common_array['goods_type']=$product_info->group_type?$product_info->group_type:0;
            $common_array['is_intel_goods'] = $product_info->is_intel_goods ?: 0;
        }else{
            $common_array['is_intel_goods'] = intval($param['is_intel_goods']);
        }

        $common_array['goods_name']         = html_entity_decode($param['g_name']);
        $common_array['short_name']         = html_entity_decode($param['short_name']);
        $common_array['goods_jingle']       = htmlspecialchars_decode($param['g_jingle']);
        $common_array['gc_id']              = intval($param['cate_id']);
        $common_array['gc_id_1']            = intval($goods_class['gc_id_1']);
        $common_array['gc_id_2']            = intval($goods_class['gc_id_2']);
        $common_array['gc_id_3']            = intval($goods_class['gc_id_3']);
        $common_array['gc_name']            = htmlspecialchars_decode($param['cate_name']);
        $common_array['brand_id']           = intval($param['b_id']);
        $common_array['brand_name']         = $param['b_name'];
        $common_array['type_id']            = intval($param['type_id']);
        $common_array['goods_image']        = $param['image_path'];
        $common_array['goods_price']        = trim($param['goods_sel_type']) == 'single' ? floatval($param['g_price']) : 0;
        $common_array['goods_marketprice']  = floatval($param['g_marketprice']);
        $common_array['goods_costprice']    = floatval($param['g_costprice']);
        $common_array['goods_discount']     = floatval($param['g_discount']);
        $common_array['goods_serial']       = $param['g_serial'];
        $common_array['goods_storage_alarm']= intval($param['g_alarm']);
        $common_array['goods_barcode']      = $param['g_barcode'];
        $common_array['goods_attr']         = serialize($param['attr']);
        $common_array['goods_custom']       = serialize($param['custom']);
        $common_array['goods_body']         = $param['g_body'];
        $common_array['mobile_body']        = $this->_getMobileBody($param['m_body'],$goods_body);
        $common_array['goods_use_body']     = $param['goods_use_body']?:'';
        $common_array['mobile_use_body']    = $this->_getMobileBody($param['mobile_use_body'],$goods_body);
        $common_array['goods_package_body'] = $param['goods_package_body']?:'';
        $common_array['mobile_package_body']= $this->_getMobileBody($param['mobile_package_body'],$goods_body);
        $common_array['goods_commend']      = intval($param['g_commend']);
        $common_array['g_search_status']    = intval($param['g_search_status']);
        $common_array['goods_state']        = ($store_state != 1) ? 0 : intval($param['g_state']);            // 店铺关闭时，商品下架
        $common_array['goods_addtime']      = TIMESTAMP;
        $common_array['goods_selltime']     = strtotime($param['starttime']) + intval($param['starttime_H'])*3600 + intval($param['starttime_i'])*60;
        $common_array['goods_verify']       = (C('goods_verify') == 1) ? 10 : 1;
        $common_array['store_id']           = $store_id;
        $common_array['store_name']         = $store_name;
        $common_array['spec_name']          = is_array($param['spec']) ? serialize($param['sp_name']) : serialize(null);
        $common_array['spec_value']         = is_array($param['spec']) ? serialize($param['sp_val']) : serialize(null);
        $common_array['goods_inv']          = intval($param['g_inv']);
        $common_array['goods_vat']          = intval($param['g_vat']);
        $common_array['areaid_1']           = intval($param['province_id']);
        $common_array['areaid_2']           = intval($param['city_id']);
        $common_array['transport_id']       = ($param['freight'] == '0') ? '0' : intval($param['transport_id']); // 运费模板
        $common_array['transport_title']    = $param['transport_title'];
        $common_array['goods_freight']      = floatval($param['g_freight']);
        $common_array['goods_trans_v']      = floatval($param['goods_trans_v']);
        $common_array['goods_stcids']       = $this->_getStoreClassArray($param['sgcate_id'], $store_id);
        $common_array['plateid_top']        = intval($param['plate_top']) > 0 ? intval($param['plate_top']) : 0;
        $common_array['plateid_bottom']     = intval($param['plate_bottom']) > 0 ? intval($param['plate_bottom']) : 0;
        $common_array['is_virtual']         = intval($param['is_gv']);
        $common_array['validity_type']      = intval($param['validity_type']);
        if(!empty($param['validity_type']) && $param['validity_type'] == 2){
            $common_array['virtual_indate'] = !empty($param['o_vindate']) ?$param['o_vindate']: 0;
        }else{
            $common_array['virtual_indate'] = !empty($param['g_vindate']) ? (strtotime($param['g_vindate']) + 24 * 60 * 60 - 1) : 0;  // 当天的最后一秒结束
        }
        $common_array['virtual_limit']      = intval($param['g_vlimit']) > 1000 || intval($param['g_vlimit']) < 0 ? 1000 : intval($param['g_vlimit']);
        $common_array['virtual_invalid_refund'] = intval($param['g_vinvalidrefund']);
        $common_array['virtual_refund_day'] = intval($param['g_refundday']);
        $common_array['sup_id']             = $param['sup_id'];
        $common_array['is_own_shop']        = in_array($store_id, Model('store')->getOwnShopIds()) ? 1 : 0;
        $common_array['freight']       = ($param['freight']) ;// 运费设置
        $common_array['vip_state']       = intval($param['vip_state']);// 医保会员价

        if(isset($param['warehouse_type'])){
            $common_array['warehouse_type'] = $param['warehouse_type'] ? 1 : 0 ;
        }

        // 互联网医院商品，不上架，但是，如果是之前在小程序上架的，重新认领到互联网医院商品时，上架状态不变
        if ($common_array['is_intel_goods'] == 1 && !(isset($_POST['commonid']) && $_POST['commonid'])){ // commonid没值表示认领，有值表示重新认领
            $common_array['goods_state'] = 0;
        }

        if($param['chain_id']){
            $common_array['chain_id']  = $param['chain_id'];
        }
        if($param['region_id']){
            $common_array['region_id'] = $param['region_id'];
        }

        // 选择商品视频后，添加商品视频链接
        $common_array['goods_video'] = $param['video_path'];
        //添加门店品牌lihaobin
        $common_array['chain_brand_id'] = intval($param['chain_brand_id']);
        //添加商品归属lihaobin
        $common_array['g_c_type'] = $param['g_c_type'];
        $batch_price = array();
        $common_array['is_batch'] = 0;
        if(trim($param['goods_sel_type']) == 'batch'){
            $common_array['is_batch'] = 1;
            $common_array['goods_price'] = floatval($param['batch_price']);
            $batch_price[1] = $common_array['goods_price'];
            if(!empty($param['_batchNum'])){
                foreach($param['_batchNum'] as $k => $val){
                    if(isset($param['_batchPrice'][$k]) && $param['_batchPrice'][$k] > 0 && intval($val) > 0){
                        $batch_price[intval($val)] = $param['_batchPrice'][$k];
                    }
                }
            }
            $common_array['batch_price'] = serialize($batch_price);
        }

        return $common_array;
    }
    /**
     * 序列化保存手机端商品描述数据
     */
    private function _getMobileBody($mobile_body,$is_open=0) {
        if ($mobile_body != '') {
            $mobile_body = str_replace('&quot;', '"', $mobile_body);
            $mobile_body = json_decode($mobile_body, true);
            //过滤敏感词
            if($is_open == 1){
                $data = array();
                foreach ($mobile_body as $v){
                    if($v['type'] == 'text'){
                        $v['value'] = sensitiveWordFilter($v['value']);
                    }
                    $data[] = $v;
                }
                $mobile_body = $data;
            }
            if (!empty($mobile_body)) {
                return serialize($mobile_body);
            }
        }
        return '';
    }

    /**
     * 查询店铺商品分类
     */
    private function _getStoreClassArray($sgcate_id, $store_id) {
        $goods_stcids_arr = array();
        if (!empty($sgcate_id)){
            $sgcate_id_arr = array();
            foreach ($sgcate_id as $k=>$v){
                $sgcate_id_arr[] = intval($v);
            }
            $sgcate_id_arr = array_unique($sgcate_id_arr);
            $store_goods_class = Model('store_goods_class')->getStoreGoodsClassList(array('store_id' => $store_id, 'stc_id' => array('in', $sgcate_id_arr), 'stc_state' => '1'));
            if (!empty($store_goods_class)){
                foreach ($store_goods_class as $k=>$v){
                    if ($v['stc_id'] > 0){
                        $goods_stcids_arr[] = $v['stc_id'];
                    }
                    if ($v['stc_parent_id'] > 0){
                        $goods_stcids_arr[] = $v['stc_parent_id'];
                    }
                }
                $goods_stcids_arr = array_unique($goods_stcids_arr);
                sort($goods_stcids_arr);
            }
        }
        if (empty($goods_stcids_arr)){
            return '';
        } else {
            return ','.implode(',',$goods_stcids_arr).',';// 首尾需要加,
        }
    }

    /**
     * 生成商品返回商品ID(SKU)数组
     */
    private function _addGoods($param, $common_id, $common_array) {
        $goodsid_array = array();

        $model_goods = Model('goods');
        $model_type = Model('type');

        // 商品规格
        if (is_array($param['spec'])) {
            foreach ($param['spec'] as $value) {
                $goods = $this->_initGoodsByCommonGoods($common_id, $common_array);
                if (isset($value['goods_id']) && $value['goods_id']){
                    $goods_id = $goods['goods_id'] = $value['goods_id'];
                }else{
                    $goods_id = Model('goods')->getAutoGoodsID();
                    if ($goods_id){
                        $goods['goods_id'] = $goods_id;
                    }
                }
                $goods['goods_name']        = $common_array['goods_name'] . ' ' . implode(' ', $value['sp_value']);
                if($common_array['short_name']){
                    $goods['short_name']        = $common_array['short_name'] . ' ' . implode(' ', $value['sp_value']);
                }
                $goods['goods_price']       = $value['price'];
                $goods['goods_promotion_price']=$value['price'];
                $goods['goods_marketprice'] = $value['marketprice'] == 0 ? $common_array['goods_marketprice'] : $value['marketprice'];
                $goods['goods_serial']      = $value['sku'];
                $goods['goods_storage_alarm'] = intval($value['alarm']);
                $goods['goods_spec']        = serialize($value['sp_value']);
                $goods['goods_storage']     = $value['stock'];
                $goods['goods_barcode']     = $value['barcode'];
                $goods['color_id']          = intval($value['color']);
                $goods['goods_sku']         = $value['goods_sku'];
                $goods['goods_sku_type']    = $value['goods_sku_type'];
                $goods['vip_state']         = intval($param['vip_state']);
                // 新增虚拟库存
                $goods['goods_virtual_storage'] = intval($value['virtual_stock']);
                $goods['is_open_virtual_stock'] = intval($value['is_open_virtual_stock']);

                $model_goods->addGoods($goods);
                $model_type->addGoodsType($goods_id, $common_id, array('cate_id' => $param['cate_id'], 'type_id' => $param['type_id'], 'attr' => $param['attr']));

                $goodsid_array[] = $goods_id;
            }
        } else {
            $goods = $this->_initGoodsByCommonGoods($common_id, $common_array);
            $goods_id = $param['goods_sku_id']?$param['goods_sku_id']:Model('goods')->getAutoGoodsID();
            if ($goods_id){
                $goods['goods_id'] = $goods_id;
            }
            $goods['goods_name']        = $common_array['goods_name'];
            $goods['goods_price']       = $common_array['goods_price'];
            $goods['goods_promotion_price']=$common_array['goods_price'];
            $goods['goods_marketprice'] = $common_array['goods_marketprice'];
            $goods['goods_serial']      = $common_array['goods_serial'];
            $goods['goods_storage_alarm'] = $common_array['goods_storage_alarm'];
            $goods['goods_spec']        = serialize(null);
            $goods['goods_storage']     = intval($param['g_storage']);
            $goods['goods_barcode']     = $common_array['goods_barcode'];
            $goods['color_id']          = 0;
            $goods['goods_sku']         = $common_array['goods_sku'];
            $goods['goods_sku_type']    = $common_array['goods_sku_type'];
            $goods['vip_state']         = $common_array['vip_state'];

            $model_goods->addGoods($goods);
            $model_type->addGoodsType($goods_id, $common_id, array('cate_id' => $param['cate_id'], 'type_id' => $param['type_id'], 'attr' => $param['attr']));

            $goodsid_array[] = $goods_id;
        }

        return $goodsid_array;
    }
    
    private function _editGoods($param, $common_id, $common_array, $store_id) {
        $goodsid_array = array();
        $colorid_array = array();

        $model_goods = Model('goods');
        $model_type = Model('type');
        $model_type->delGoodsAttr(array('goods_commonid' => $common_id));
        $model_goods->beginTransaction();
        if (is_array($param['spec'])) {
            foreach ($param['spec'] as $value) {
                $goods = $this->_initGoodsByCommonGoods($common_id, $common_array,false);
                $goods_info = $model_goods->getGoodsInfo(
                    array(
                        'goods_id' => $value['goods_id'],
                        'goods_commonid' => $common_id,
                        'store_id' => $store_id
                    ),
                    'is_dis,goods_id,goods_price,short_name,enable_member_price,have_gift,vip_state,virtual_indate,validity_type'
                );
                if (!empty($goods_info)) {
                    $goods_id = $goods_info['goods_id'];
                    $goods['goods_name']        = htmlspecialchars_decode($common_array['goods_name'] . ' ' . implode(' ', $value['sp_value']));
                    $goods['short_name']        = htmlspecialchars_decode($common_array['short_name'] . ' ' . implode(' ', $value['sp_value']));
                    $goods['goods_price']       = $value['price'];
                    $goods['goods_marketprice'] = $value['marketprice'] == 0 ? $common_array['goods_marketprice'] : $value['marketprice'];
                    $goods['goods_serial']      = $value['sku'];
                    $goods['goods_storage_alarm']= intval($value['alarm']);
                    $goods['goods_spec']        = serialize($value['sp_value']);
                    $goods['goods_storage']     = $value['stock'];
                    $goods['goods_barcode']     = $value['barcode'];
                    $goods['color_id']          = intval($value['color']);
                    // 新增虚拟库存
                    $goods['goods_virtual_storage'] = intval($value['virtual_stock']);
                    $goods['is_open_virtual_stock'] = intval($value['is_open_virtual_stock']);

                    if (isset($value['goods_sku'])){
                        $goods['goods_sku'] = $value['goods_sku'];
                    }

                    if (isset($value['goods_sku_type'])){
                        $goods['goods_sku_type'] = $value['goods_sku_type'];
                    }
                    $goods['is_dis'] = $goods_info['is_dis'];
                    // 虚拟商品不能有赠品
                    if ($common_array['is_virtual'] == 1) {
                        // 相对时间不能小于之前设置的时间
//                        if ($common_array['validity_type'] == 2 && $common_array['virtual_indate'] < $goods_info['virtual_indate']) {
//                            return callback(false, '商品编辑失败,相对时间不能小于之前设置的时间]');
//                        }
                        $goods['have_gift']    = 0;
                        Model('goods_gift')->delGoodsGift(array('goods_id' => $goods_id,'store_id'=>$store_id));
                        Model('goods_fcode')->delGoodsFCode(array('goods_id' => $goods_id));
                        //如果是医保价商品，添加p_time记录，避免其它活动配置该商品促销
                        if ($param['vip_state'] == 2 || $param['vip_state'] == 0){
                            $goods_info['goods_price'] = $value['price'];
                            $res = $this->insuranceGoods($goods_info, $param['vip_state']);
                            if (!$res['state']){
                                return $res;
                            }
                            $goods['vip_state'] = intval($param['vip_state']);
                        }
                    }

                    unset($goods['goods_addtime']);
                    $updateXS = $goods['goods_verify']==10?true:false;
                    $res = $model_goods->editGoodsById($goods, $goods_id, $updateXS);
                    if (!$res){
                        $model_goods->rollback();
                        return callback(false,'更新失败');
                    }
                    //异步更新医保价商品：收藏下架、清除足迹
                    if (intval($param['vip_state']) == 2){
                        CycleBuyCollectQueue::dispatch($goods_id);
                    }
                } else {
                    if ($value['goods_id']){
                        $goods_id = $goods['goods_id'] = $value['goods_id'];
                    }else{
                        $goods_id = Model('goods')->getAutoGoodsID();
                        if ($goods_id){
                            $goods_id = $goods['goods_id'] = $goods_id;
                        }
                    }
                    $goods['goods_name']        = $common_array['goods_name'] . ' ' . implode(' ', $value['sp_value']);
                    $goods['short_name']        = $common_array['short_name'] . ' ' . implode(' ', $value['sp_value']);
                    $goods['goods_price']       = $value['price'];
                    $goods['goods_promotion_price']=$value['price'];
                    $goods['goods_marketprice'] = $value['marketprice'] == 0 ? $common_array['goods_marketprice'] : $value['marketprice'];
                    $goods['goods_serial']      = $value['sku'];
                    $goods['goods_storage_alarm']= intval($value['alarm']);
                    $goods['goods_spec']        = serialize($value['sp_value']);
                    $goods['goods_storage']     = $value['stock'];
                    $goods['goods_barcode']     = $value['barcode'];
                    $goods['color_id']          = intval($value['color']);
                    // 新增虚拟库存
                    $goods['goods_virtual_storage'] = intval($value['virtual_stock']);

                    if (isset($value['goods_sku'])){
                        $goods['goods_sku'] = $value['goods_sku'];
                    }

                    if (isset($value['goods_sku_type'])){
                        $goods['goods_sku_type'] = $value['goods_sku_type'];
                    }
                    $goods['is_dis'] = $param['is_dis'];
                    
                    $model_goods->addGoods($goods);
                }
                $goodsid_array[] = intval($goods_id);
                $colorid_array[] = intval($value['color']);
                $model_type->addGoodsType($goods_id, $common_id, array('cate_id' => $param['cate_id'], 'type_id' => $param['type_id'], 'attr' => $param['attr']));
            }
        } else {
            if (C('dbdriver') == 'mysql') {
                $goods_spec_field_name = 'goods_spec';
            } else {
                $goods_spec_field_name = 'to_char(goods_spec)';
            }
            $goods = $this->_initGoodsByCommonGoods($common_id, $common_array);
            $goods_info = $model_goods->getGoodsInfo(array($goods_spec_field_name => serialize(null), 'goods_commonid' => $common_id,
                'store_id' => $store_id), 'goods_id,goods_price,short_name,enable_member_price,have_gift,vip_state,virtual_indate,validity_type');
            if (!empty($goods_info)) {
                $goods_id = $goods_info['goods_id'];
                $goods['goods_name']        = $common_array['goods_name'];
                $goods['short_name']        = $common_array['short_name'];
                $goods['goods_price']       = $common_array['goods_price'];
                $goods['goods_marketprice'] = $common_array['goods_marketprice'];
                $goods['goods_serial']      = $common_array['goods_serial'];
                $goods['goods_storage_alarm']= $common_array['goods_storage_alarm'];
                $goods['goods_spec']        = serialize(null);
                $goods['goods_storage']     = intval($param['g_storage']);
                $goods['goods_barcode']     = $common_array['goods_barcode'];
                $goods['color_id']          = 0;
                if ($common_array['is_virtual'] == 1) {
                    $goods['have_gift']    = 0;
                    Model('goods_gift')->delGoodsGift(array('goods_id' => $goods_id,'store_id'=>$store_id));
                    Model('goods_fcode')->delGoodsFCode(array('goods_id' => $goods_id));
                    // 相对时间不能小于之前设置的时间
//                    if ($common_array['validity_type'] == 2 && $common_array['virtual_indate'] < $goods_info['virtual_indate']) {
//                        return callback(false, '商品编辑失败,相对时间不能小于之前设置的时间]');
//                    }

                    //如果是医保价商品，添加p_time记录，避免其它活动配置该商品促销
                    if ($param['vip_state'] == 2 || $param['vip_state'] == 0){
                        $goods_info['goods_price'] = $goods['goods_price'];
                        $res = $this->insuranceGoods($goods_info, $param['vip_state']);
                        if (!$res['state']){
                            return $res;
                        }
                        $goods['vip_state'] = intval($param['vip_state']);
                    }
                }

                if (isset($common_array['goods_sku'])){
                    $goods['goods_sku'] = $common_array['goods_sku'];
                }else{
                    $goods['goods_sku'] = $common_array['goods_serial'];
                }

                if (isset($common_array['goods_sku_type'])){
                    $goods['goods_sku_type'] = $common_array['goods_sku_type'];
                }elseif ($param['goods_sku_type']){
                    $goods['goods_sku_type'] = $param['goods_sku_type'];
                }
                $goods['is_dis'] = $param['is_dis'];
                unset($goods['goods_addtime']);

                $res = $model_goods->editGoodsById($goods, $goods_id,$goods['goods_verify']==10?true:false);
                if (!$res){
                    $model_goods->rollback();
                    return callback(false,'更新失败');
                }
                //异步更新医保价商品：收藏下架、清除足迹
                if (intval($param['vip_state']) == 2){
                    CycleBuyCollectQueue::dispatch($goods_id);
                }
            } else {
                $goods['goods_name']        = $common_array['goods_name'];
                $goods['short_name']        = $common_array['short_name'];
                $goods['goods_price']       = $common_array['goods_price'];
                $goods['goods_promotion_price']=$common_array['goods_price'];
                $goods['goods_marketprice'] = $common_array['goods_marketprice'];
                $goods['goods_serial']      = $common_array['goods_serial'];
                $goods['goods_storage_alarm']= $common_array['goods_storage_alarm'];
                $goods['goods_spec']        = serialize(null);
                $goods['goods_storage']     = intval($param['g_storage']);
                $goods['goods_barcode']     = $common_array['goods_barcode'];
                $goods['color_id']          = 0;

                if (isset($common_array['goods_sku'])){
                    $goods['goods_sku'] = $common_array['goods_sku'];
                }else{
                    $goods['goods_sku'] = $common_array['goods_serial'];
                }

                if (isset($common_array['goods_sku_type'])){
                    $goods['goods_sku_type'] = $common_array['goods_sku_type'];
                }
                $goods['is_dis'] = $param['is_dis'];
                $goods_id = $model_goods->addGoods($goods);
            }
            $goodsid_array[] = intval($goods_id);
            $colorid_array[] = 0;
            $model_type->addGoodsType($goods_id, $common_id, array('cate_id' => $param['cate_id'], 'type_id' => $param['type_id'], 'attr' => $param['attr']));
        }
        $model_goods->commit();
        return array('goodsid_array' => $goodsid_array, 'colorid_array' =>  array_unique($colorid_array));
    }

    /**
     * 根据通用商品数据初始化商品数据
     */
    private function _initGoodsByCommonGoods($common_id, $common_array,$type = true) {
        $goods = array();
        $goods['goods_commonid']    = $common_id;
        $goods['goods_jingle']      = $common_array['goods_jingle'];
        $goods['store_id']          = $common_array['store_id'];
        $goods['store_name']        = $common_array['store_name'];
        $goods['gc_id']             = $common_array['gc_id'];
        $goods['gc_id_1']           = $common_array['gc_id_1'];
        $goods['gc_id_2']           = $common_array['gc_id_2'];
        $goods['gc_id_3']           = $common_array['gc_id_3'];
        $goods['brand_id']          = $common_array['brand_id'];
        $goods['spec_name']         = $common_array['spec_name'];
        //编辑商品不更新图片
        if ($type){
            $goods['goods_image']       = $common_array['goods_image'];
        }
        $goods['goods_state']       = $common_array['goods_state'];
        $goods['goods_verify']      = $common_array['goods_verify'];
        $goods['goods_addtime']     = TIMESTAMP;
        $goods['goods_edittime']    = TIMESTAMP;
        $goods['areaid_1']          = $common_array['areaid_1'];
        $goods['areaid_2']          = $common_array['areaid_2'];
        $goods['transport_id']      = $common_array['transport_id'];
        $goods['goods_freight']     = $common_array['goods_freight'];
        $goods['goods_trans_v']     = $common_array['goods_trans_v'];
        $goods['goods_inv']         = $common_array['goods_inv'];
        $goods['goods_vat']         = $common_array['goods_vat'];
        $goods['goods_commend']     = $common_array['goods_commend'];
        $goods['g_search_status']     = $common_array['g_search_status'];
        $goods['goods_stcids']      = $common_array['goods_stcids'];
        $goods['is_virtual']        = $common_array['is_virtual'];
        $goods['validity_type']    = $common_array['validity_type'];
        $goods['virtual_indate']    = $common_array['virtual_indate'];
        $goods['virtual_limit']     = $common_array['virtual_limit'];
        $goods['virtual_invalid_refund'] = $common_array['virtual_invalid_refund'];
        $goods['is_own_shop']       = $common_array['is_own_shop'];
        $goods['is_batch']          = $common_array['is_batch'];
        $goods['batch_price']       = $common_array['batch_price'];
        $goods['g_c_type']          = $common_array['g_c_type'];//区分猫狗
        $goods['freight']          = $common_array['freight'];//是否包邮
        $goods['goods_type'] = $common_array['goods_type']; //商品组合

        if(isset($common_array['warehouse_type'])){
            $goods['warehouse_type'] = $common_array['warehouse_type'];
        }

        if(isset($common_array['is_intel_goods'])){
            $goods['is_intel_goods'] = $common_array['is_intel_goods'];
        }

        if($common_array['chain_id']){
            $goods['chain_id'] = $common_array['chain_id']; //记录归属门店
        }
        if($common_array['region_id']){
            $goods['region_id'] = $common_array['region_id']; //记录归属大区id
        }
        return $goods;
    }

    private function _imageAll($common_id, $store_id, $image_all, $image_main) {
        $model_goods = Model('goods');

        $image_array = explode(',', $image_all);

        $insert_array = array();
        foreach ($image_array as $value) {
            if(!empty($value)) {
                $tmp_insert = array();
                $tmp_insert['goods_commonid']   = $common_id;
                $tmp_insert['store_id']         = $store_id;
                $tmp_insert['color_id']         = 0;
                $tmp_insert['goods_image']      = $value;
                $tmp_insert['goods_image_sort'] = 0;
                if($value == $image_main) {
                    $tmp_insert['is_default'] = 1 ;
                } else {
                    $tmp_insert['is_default'] = 0;
                }
                $insert_array[] = $tmp_insert;
            }
        }

        $model_goods->addGoodsImagesAll($insert_array);
    }

    /**
     * 记录日志
     *
     * @param $content 日志内容
     * @param $state 1成功 0失败
     */
    private function _recordLog($content, $seller_id, $seller_name, $store_id, $state = 1) {
        $log = array();
        $log['log_content'] = $content;
        $log['log_time'] = TIMESTAMP;
        $log['log_seller_id'] = $seller_id;
        $log['log_seller_name'] = $seller_name;
        $log['log_store_id'] = $store_id;
        $log['log_seller_ip'] = getIp();
        $log['log_url'] = 'goodsLogic&saveGoods';
        $log['log_state'] = $state;
        $model_seller_log = Model('seller_log');
        $model_seller_log->addSellerLog($log);
    }

    /**
     * 上传图片
     *
     */
    public function uploadGoodsImage($image_name, $store_id, $album_limit)
    {
        // 判断图片数量是否超限
        $model_album = Model('album');
        if ($album_limit > 0) {
            $album_count = $model_album->getCount(array('store_id' => $store_id));
            if ($album_count >= $album_limit) {
                return callback(false, L('store_goods_album_climit'));
            }
        }

        $class_info = $model_album->getOne(array('store_id' => $store_id, 'is_default' => 1), 'album_class');
        // 上传图片
        $upload = new UploadFile();
        $upload->set('default_dir', ATTACH_GOODS . DS . $store_id . DS . $upload->getSysSetPath());
        $upload->set('max_size', C('image_max_filesize'));

        $upload->set('thumb_width', GOODS_IMAGES_WIDTH);
        $upload->set('thumb_height', GOODS_IMAGES_HEIGHT);
        $upload->set('thumb_ext', GOODS_IMAGES_EXT);
        $upload->set('fprefix', $store_id);
        $upload->set('allow_type', array('gif', 'jpg', 'jpeg', 'png'));
        $result = $upload->upfile($image_name,true,1280);
        if (!$result) {
            return callback(false, $upload->error);
        }

        $img_path = $upload->getSysSetPath() . $upload->file_name;

        // 取得图像大小
        list($width, $height, $type, $attr) = $upload->image_info;

        // 存入相册
        $image = explode('.', $_FILES[$image_name]["name"]);
        $insert_array = array();
        $insert_array['apic_name'] = $image['0'];
        $insert_array['apic_tag'] = '';
        $insert_array['aclass_id'] = $class_info['aclass_id'];
        $insert_array['apic_cover'] = $img_path;
        $insert_array['apic_size'] = intval($_FILES[$image_name]['size']);
        $insert_array['apic_spec'] = $width . 'x' . $height;
        $insert_array['upload_time'] = TIMESTAMP;
        $insert_array['store_id'] = $store_id;
        $model_album->addPic($insert_array);

        $data = array ();
        $data ['thumb_name'] = cthumb($img_path, 240, $store_id);
        $data ['name']      = getOriginImage($img_path,$store_id);

        return callback(true, '', $data);
    }

    /**
     * 上传视频
     *
     * @param $video_name 视频名称
     * @param $store_id 店铺ID
     * @param $album_limit 媒体库大小
     * @param $video_size 视频尺寸
     *
     */
    public function uploadGoodsVideo($video_name, $store_id, $album_limit,$video_size)
    {
        /**
         * 验证是否存在默认视频
         */
        $model_album = Model('video_album');
        $return = $model_album->checkAlbum(array('video_album_class.store_id' => $store_id, 'is_default' => '1'));
        if (!$return) {
            $album_arr = array();
            $album_arr['video_class_name'] = '默认媒体库';
            $album_arr['store_id'] = $store_id;
            $album_arr['video_class_des'] = '';
            $album_arr['video_class_sort'] = '255';
            $album_arr['upload_time'] = time();
            $album_arr['is_default'] = '1';
            $model_album->addClass($album_arr);
        }

        // 判断视频数量是否超限
        if ($album_limit > 0) {
            $album_count = $model_album->getCount(array('store_id' => $store_id));
            if ($album_count >= $album_limit) {
                return callback(false, L('store_goods_album_climit'));
            }
        }

        $class_info = $model_album->getOne(array('store_id' => $store_id, 'is_default' => 1), 'video_album_class');

        // 上传视频
        $video_path = ATTACH_GOODS . DS . $store_id . DS . 'goods_video';
        $upload = new UploadVideoFile();
        $upload->set('default_dir', $video_path);
        $upload->set('fprefix', $store_id);
        $upload->set('max_size' , $video_size);
        $result = $upload->upfile($video_name);
        if (!$result) {
            return callback(false, $upload->error);
        }

        $video_file = $upload->file_name;
        // 存入视频空间
        $video = explode('.', $_FILES[$video_name]["name"]);
        $insert_array = array();
        $insert_array['video_name'] = $video['0'];
        $insert_array['video_tag'] = '';
        $insert_array['video_class_id'] = $class_info['video_class_id'];
        $insert_array['video_cover'] = $video_file;
        $insert_array['video_size'] = intval($_FILES[$video_name]['size']);
        $insert_array['upload_time'] = TIMESTAMP;
        $insert_array['store_id'] = $store_id;
        Model('upload_video_album')->add($insert_array);

        $data = array ();
        $data['goods_video'] = goodsVideoPath($video_file , $store_id);
        $data['name']      = $video_file;

        return callback(true, '', $data);
    }


    /**
     * 编辑商品图
     */
    public function editSaveImage($img, $common_id, $store_id, $seller_id, $seller_name) {

        if ($common_id <= 0 || empty($_POST['img'])) {
            return callback(false, '参数错误');
        }
        /** @var goodsModel $model_goods */
        $model_goods = Model('goods');
        // 删除原有图片信息
        $model_goods->delGoodsImages(array('goods_commonid' => $common_id, 'store_id' => $store_id));
        // 保存
        $insert_array = array();
        $update_water_img = '';
        foreach ($_POST['img'] as $key => $value) {
            $k = 0;
            foreach ($value as $v) {
                if ($v['name'] == '') {
                    continue;
                }
                // 商品默认主图
                $update_array = array();        // 更新商品主图
                $update_where = array();
                $update_array['goods_image']    = $v['name'];
                $update_where['goods_commonid'] = $common_id;
                $update_where['store_id']       = $store_id;
                $update_where['color_id']       = $key;
                if ($v['default'] == 1) {
                    $k++;
                    $update_array['goods_image']    =  $update_water_img =  $v['name'];
                    $update_where['goods_commonid'] = $common_id;
                    $update_where['store_id']       = $store_id;
                    $update_where['color_id']       = $key;
                    // 更新商品主图
                    $model_goods->editGoods($update_array, $update_where);
                }
                $tmp_insert = array();
                $tmp_insert['goods_commonid']   = $common_id;
                $tmp_insert['store_id']         = $store_id;
                $tmp_insert['color_id']         = $key;
                $tmp_insert['goods_image']      = $v['name'];
                $tmp_insert['goods_image_sort'] = ($v['default'] == 1) ? 0 : $v['sort'];
                $tmp_insert['is_default']       = $v['default'];
                $insert_array[] = $tmp_insert;
            }
        }
        $rs = $model_goods->addGoodsImagesAll($insert_array);
        if ($rs) {
            $this->_recordLog('商品图片编辑，SPU:'.$common_id, $seller_id, $seller_name, $store_id);

            $goodLists = $model_goods->getGoodsList(['goods_commonid' => $common_id],'goods_id');
            $goodsIds = array_column((array)$goodLists,'goods_id');

            if($goodsIds){
                $model_goods->addGoodsHandlelog([
                    'gh_good_ids' => implode(",",$goodsIds),
                    'gh_notes' => '修改商品图片',
                    'gh_addtime' => time(),
                    'store_id'=>$store_id
                ]);
            }

            //判断水印的商品重新更新
            $water_goods = WatermarkGoods::where('goods_commonid',$common_id)
                ->where('status',2)->find();
            if ($water_goods) {
                WatermarkGoods::where('goods_id',$water_goods['goods_id'])->update(['status'=>1,'goods_image'=>$update_water_img]);
            }

            return callback(true);
        } else {
            return callback(false, '商品图片编辑失败');
        }
    }
    
    /**
     * 商品上架
     * @param unknown $commonid_array
     * @param unknown $store_id
     * @param unknown $seller_id
     * @param unknown $seller_name
     * @param unknown $type 1 默认 2 阿闻提审专用
     * @return multitype:unknown
     */
    public function goodsShow($commonid_array, $store_id, $seller_id, $seller_name,$type = 1) {
        $return = Model('goods')->editProducesOnline(array('goods_commonid' => array('in', $commonid_array), 'store_id' => $store_id),$type);
        if ($return) {
            $goods_list = Model('goods')->getGoodsList(array('goods_commonid' => array('in', $commonid_array),'store_id' => $store_id), 'goods_id','','',20000);
            if (!empty($goods_list)) {
                $goodsid_array = array();
                foreach ($goods_list as $val) {
                    $goodsid_array[] = $val['goods_id'];
                }
                //记录商品操作日志 给数据中心用
                $this->SynsGoodsHandleToEs($goodsid_array,$store_id,1);
            }
            // 添加操作日志
            $this->_recordLog('商品上架，SPU:'.implode(',', $commonid_array), $seller_id, $seller_name, $store_id);
            return callback(true);
        } else {
            return callback(false, '商品上架失败');
        }
    }
    
    /**
     * 商品下架
     * @param unknown $commonid_array
     * @param unknown $store_id
     * @param unknown $seller_id
     * @param unknown $seller_name
     * @param unknown $type 1 默认 2 阿闻小程序提审用
     * @return multitype:unknown
     */
    public function goodsUnShow($commonid_array, $store_id, $seller_id, $seller_name,$type = 1) {
        $model_goods = Model('goods');
        $where = array();
        $where['goods_commonid'] = array('in', $commonid_array);
        $where['store_id'] = $store_id;
        $goods_list = $model_goods->getGoodsList($where, 'goods_id','','',20000);
        if (!empty($goods_list)) {
            foreach ($goods_list as $value) {
                $is_pintuan = DcGroupBuyProduct::getPinProductInfo($value['goods_id']);
                if (!empty($is_pintuan)) {
                    return callback(false, '拼团活动商品不能下架');
                }
            }
        }
        $return = Model('goods')->editProducesOffline($where,$type);
        if ($return) {
            // 更新优惠套餐状态关闭
            if (!empty($goods_list)) {
                $goodsid_array = array();
                foreach ($goods_list as $val) {
                    $goodsid_array[] = $val['goods_id'];
                }
                //记录商品操作日志 给数据中心用
                $this->SynsGoodsHandleToEs($goodsid_array,$store_id,2);
            }

            SyncMiniShopDownQueue::dispatch($commonid_array);

            // 添加操作日志
            $this->_recordLog('商品下架，SPU:'.implode(',', $commonid_array), $seller_id, $seller_name, $store_id);
            return callback(true);
        } else {
            return callback(false, '商品下架失败');
        }
    }

    /**
     * 商品下下架 小程序提审用
     * @param unknown $commonid_array
     * @param unknown $store_id
     * @param unknown $seller_id
     * @param unknown $seller_name
     * @param unknown $type 1 上架 2 下架
     * @return multitype:unknown
     */
    public function goodsXcxCheck($commonid_array, $store_id, $seller_id, $seller_name,$type = 1) {
        $model_goods = Model('goods');
        $msg = "上架";
        if ($type == 2) {
            $msg = "下架";
        }
        $return = $model_goods->editProducesCheck($commonid_array,$type,$store_id);
        if ($return) {
            // 添加操作日志
            $this->_recordLog('商品'.$msg.'，SPU:'.implode(',', $commonid_array), $seller_id, $seller_name, $store_id);
            return callback(true);
        } else {
            return callback(false, '商品'.$msg.'失败');
        }
    }
    
    public function goodsDrop($commonid_array, $store_id, $seller_id, $seller_name) {
        $return = Model('goods')->delGoodsNoLock(array('goods_commonid' => array('in', $commonid_array), 'store_id' => $store_id));
        if ($return) {
            // 添加操作日志
            $this->_recordLog('删除商品，SPU：'.implode(',', $commonid_array), $seller_id, $seller_name, $store_id);
            return callback(true);
        } else {
            return callback(false, '商品删除失败');
        }
        
    }

    /**
     * 保存商品会员等级价格
     * @param $data
     */
    public function saveMemberPrice($data){
        $model_goods = Model('goods');
        // 商品列表
        $goods_array = $model_goods->getGoodsList(array('goods_commonid' => $data['commonid']), '*');
        if(!empty($goods_array)){
            foreach ($goods_array as $value){
                $update = array();
                // 价格不能修改
                $update['enable_member_price'] = $data['enable_member_price'];
                $model_goods->editGoodsById($update, $value['goods_id'],$value['store_id']);
            }
            return callback(true);
        }
        return callback(false, '会员等级价格保存失败');
    }

    /**
     * 通过会员等级获取商品会员价格
     * 与付费会员取更低价格
     */
    public function getGoodsMemberPrice($goods_info, $member_info = array(), $isBuy = false){
        //爱省钱属于员工分销，不校验登录取价判断，则重置为空
        if (isset($_GET['pet_minpro']) && $_GET['pet_minpro'] == 1){
            $member_info = [];
        }
        $goods_info['is_member_price'] = 0;
        $goods_info['member_price'] = 0.00;
        $goods_info['is_vip_discount'] = 0;
        $goods_info['member_price_1'] = 0;
        $goods_info['user_level_id'] = 0;
        $new_member_price = 0;
        //健康会员卡实体卡 vip-3.1
        $model_goods = Model("goods");
        $isPhysicalCard = $model_goods->isPhysicalCard([$goods_info['goods_id']])[$goods_info['goods_id']];
        if ((isset($goods_info['cycle_info']) && $goods_info['cycle_info'] && intval($_POST['cycle_num']) > 1) || $isPhysicalCard>0  ){
            return $goods_info;
        }
        //医保价
        if ($goods_info['vip_state'] == 2) {
            if ($member_info['card_id'] || isset($_GET['pet_minpro']) && $_GET['pet_minpro'] == 1) {
                $goods_info['is_member_price'] = 1;
            }
            $goods_info['member_price'] = $goods_info['goods_price'];
            $goods_info['promotion_price'] = $goods_info['goods_marketprice'];
            if (!isset($_GET['pet_minpro'])){
                $goods_info['goods_price'] = $goods_info['goods_marketprice'];
            }
            $goods_info['promotion_type'] = 'member';
            $goods_info['title'] = '会员价';
            return $goods_info;
        }

        // 开启了会员价
        // 已登录情况：商品已开启会员价，当前会员等级已启用 && 且勾选 "会员价"，此时才有商品返回；其他情况没有商品返回
        if ($goods_info['enable_member_price'] == 1 && $member_info) {
            $userLevels = UserLevel::getUserLevelMap();
            if ($userLevels) {
                if ($userLevels[$member_info['user_level_id']]['level_status'] == 1 && $userLevels[$member_info['user_level_id']]['member_price'] > 0) {
                    $new_member_price = ceil($goods_info['goods_price']*$userLevels[$member_info['user_level_id']]['member_price']*10 - 0.0001) / 100;
                    $goods_info['user_level_id'] = $member_info['user_level_id'];
                }
            }
            // 会员价还要和限时折扣比高低
            if($new_member_price > 0 && ($new_member_price - ($goods_info['xianshi_price'] ?: $goods_info['goods_price'])) < 0.001){
                $goods_info['is_member_price'] = 1;
                $goods_info['member_price'] = $new_member_price;
                $goods_info['member_price_1'] = $new_member_price; // 会员价，兼容历史版本
                $goods_info['promotion_price'] = $new_member_price;
                $goods_info['promotion_type'] = 'member';
                $goods_info['title'] = '会员价';
            } else{
                $new_member_price = 0;
                $goods_info['user_level_id'] = 0;
            }
        }

        // 存在付费会员价
        if($goods_info['vip_discount'] > 0) {
            // 折后折
            $vip_price = ncPriceFormat(ceil(($goods_info['xianshi_price'] ?: $goods_info['goods_price'])*$goods_info['vip_discount'] * 10 - 0.0001) / 100);
            // 付费会员价格更低
            if($new_member_price == 0 || $vip_price < $new_member_price) {
                // 如果已经购买了卡 或者不是下单场景
                if(($member_info && $member_info['vip_card_state']) || !$isBuy){
                    $goods_info['user_level_id'] = 0;
                    $goods_info['goods_promotion_type'] = 0;
                    $goods_info['is_member_price'] = ($member_info && $member_info['vip_card_state']) ? 1 : 0;
                    $goods_info['member_price'] = $vip_price;
                    $goods_info['member_price_1'] = $vip_price;
                    $goods_info['promotion_price'] = $vip_price;
                    $goods_info['promotion_type'] = 'member';
                    $goods_info['is_vip_discount'] = 1;
                    $goods_info['title'] = '会员价';

                    // 开通了付费会员
                    if($goods_info['is_member_price']){
                        // 如果有限时折扣叠加，原价显示为 原价*会员折扣价
                        if($goods_info['xianshi_price'] > 0){
                            $goods_info['goods_price'] = $goods_info['xianshi_price'] = ceil($goods_info['goods_price']*$goods_info['vip_discount'] * 10 - 0.0001) / 100;
                        }
                    }else if ($new_member_price){ // 没开通付费会员，且免费会员价格低
                        $goods_info['goods_price'] = $new_member_price;
                        // 如果免费会员价比限时折扣低，这移除限时折扣价格
                        unset($goods_info['xianshi_price']);
                        $goods_info['ifpromotion'] = true;
                        $goods_info['user_level_id'] = $member_info['user_level_id'];
                    }else if($goods_info['xianshi_price'] > 0){
                        $goods_info['promotion_price'] = $goods_info['xianshi_price'];
                        $goods_info['goods_price'] = $goods_info['xianshi_price'];
                    }
                }else if($new_member_price > 0){
                    $goods_info['ifpromotion'] = true;
                }
            }
        }else{
            if($goods_info['is_member_price'] == 1){
                unset($goods_info['xianshi_price']);
            }
        }

        return $goods_info;
    }

    /**
     * 批量导入修改商品资料
     */
    public function editGoods($goods_data,$goods_id_data){

        if(empty($goods_data) || empty($goods_id_data)){
            return callback(false, '数据为空');
        }
        $model_goods = Model('goods');
        $goods_data_list=$model_goods->table('goods')->field('goods_id,is_virtual,goods_name,goods_marketprice,goods_price,member_price_1,goods_storage,goods_commonid')->where(['goods_id'=>['in',$goods_id_data]])->limit(false)->key('goods_id')->select();


        try {
            $model_goods->beginTransaction();
            foreach ($goods_data as $value) {
                if(empty($goods_data_list[$value['goods_id']])){
                    throw new Exception('商品ID'.$value['goods_id'].'错误');
                }
                $value['goods_commonid'] = $goods_data_list[$value['goods_id']]['goods_commonid'];
                $value['goods_name'] = $goods_data_list[$value['goods_id']]['goods_name'];

                if($goods_data_list[$value['goods_id']]['is_virtual']==1){
                    if($value['goods_marketprice'] != $goods_data_list[$value['goods_id']]['goods_marketprice'] ||
                        $value['goods_price'] != $goods_data_list[$value['goods_id']]['goods_price'] ||
                        $value['member_price_1'] !=$goods_data_list[$value['goods_id']]['member_price_1']){
                        $value['is_virtual'] = $goods_data_list[$value['goods_id']]['is_virtual'];
                        $this->editGoodsTags($value,true);

                    }else{
                        $this->editGoodsTags($value,false);
                    }
                }else{
                    if($value['goods_marketprice'] != $goods_data_list[$value['goods_id']]['goods_marketprice'] ||
                        $value['goods_price'] != $goods_data_list[$value['goods_id']]['goods_price'] ||
                        $value['member_price_1'] !=$goods_data_list[$value['goods_id']]['member_price_1'] ||
                        $value['goods_storage'] !=$goods_data_list[$value['goods_id']]['goods_storage']){
                        $value['is_virtual'] = $goods_data_list[$value['goods_id']]['is_virtual'];
                        $this->editGoodsTags($value,true);

                    }else{
                        $this->editGoodsTags($value,false);
                    }
                }


            }
            $model_goods->commit();
            return callback(true, '操作成功');
        } catch (Exception $e) {
            $model_goods->rollback();
            return callback(false, $e->getMessage());
        }


    }

    /**
     * 修改商品标签
     * @param $goods_data
     * @param bool $is_up
     * @return multitype
     * @throws Exception
     */
    public function editGoodsTags($goods_data,$is_up=true){
        $store_id =$_SESSION['store_id'] ?: $_REQUEST['store_id']?:1;
        $model_tags = Model('tags_goods');
        $model_goods = Model('goods');
        $goods_id = intval($goods_data['goods_id']);
        $tagid_arr = $goods_data['tags_id'];
        $up_data = [];
        $condition = [];
        if($goods_id){

            if($is_up){
                $condition['goods_id'] = $goods_id;
                $up_data['goods_marketprice'] = $goods_data['goods_marketprice'];
                $up_data['goods_price'] = $goods_data['goods_price'];
                $up_data['member_price_1'] = $goods_data['member_price_1'];

                if($goods_data['is_virtual']!=1){
                    $up_data['goods_storage'] = $goods_data['goods_storage'];
                }
                $up_data['goods_edittime'] = TIMESTAMP;

                $goods_result = $model_goods->table('goods')->where($condition)->update($up_data);

                if (!$goods_result) {
                    throw new Exception('导入失败');
                }
            }

            if(is_array($tagid_arr)&&!empty($tagid_arr)){

                $model_tags->delTagsGoods(array("tag_goods_id"=>$goods_id));
                $tags_goods = [];
                if (is_array($tagid_arr) && !empty($tagid_arr)) {
                    foreach ($tagid_arr as $key => $val) {
                        $tagInfo = $model_tags->getTagsInfo(intval($val),"tag_name,tag_id");
                        if(empty($tagInfo)){
                            return;
                            //throw new Exception('商品标签ID'.$val.'错误');
                        }
                        $tags_goods[$key]['tag_goods_id'] = $goods_id;
                        $tags_goods[$key]['tag_goods_name'] = $goods_data['goods_name'];
                        $tags_goods[$key]['tag_goods_common'] = $goods_data['goods_commonid'];
                        $tags_goods[$key]['tag_id'] = $val;
                        $tags_goods[$key]['tag_title'] = $tagInfo['tag_name'] ? $tagInfo['tag_name']:'';
                        $tags_goods[$key]['tag_add_time'] = time();
                        $tags_goods[$key]['store_id'] = $store_id;
                    }
                }
                $result = $model_tags->addGoodsTagAll($tags_goods);
                if (!$result) {
                    throw new Exception('导入失败');
                }
            }

        }else{
            throw new Exception('有商品ID为空的数据，请检查！');
        }

        return callback(true,'操作成功');
    }

    /**
     * 均摊子商品价格
     *
     * @param array $goods
     */
    public function recalculateSubGoodsPrice(array &$goods)
    {
        if(empty($goods['group_goods_list'])){ // 非组合商品不处理
            return;
        }

        $groupTotal = 0;
        foreach ($goods['group_goods_list'] as $subGoods){
            $groupTotal += $subGoods['goods_price'] * $subGoods['goods_number'];
        }

        foreach ($goods['group_goods_list'] as & $subGoods) {
            $rate = $subGoods['goods_price'] / $groupTotal;
            // 只允许少，不允许多
            $price = (int)($goods['goods_price'] * $rate * 100) / 100;
            $subGoods['goods_price'] = ncPriceFormat($price);
        }
    }

    /**
     * 商品多图同步水印
     *
     * @return array
     */
    public function imagesSyncWatermark($goods_image, array $images)
    {
        if (stripos($goods_image, '?watermark/3') !== false) {
            $watermark = substr($goods_image, stripos($goods_image, '?watermark/3'));
            if ($watermark) {
                if (count($images) > 0) {
                    $mainImage = $images[0];
                    if (stripos($mainImage, 'file.vetscloud.com') !== false) { //七牛云图片
                        $mainImage = substr($mainImage, 0, stripos($mainImage, '|equal'));
                        if ($mainImage == "") {
                            $mainImage = $images[0];
                        }
                        if (stripos($mainImage, '?watermark/3') !== false) {
                            $images[0] = $mainImage;
                        } else {
                            $images[0] = $mainImage . $watermark;
                        }
                    }
                }
            }
        } else if (stripos($goods_image, '?x-oss-process=') !== false) {
            $watermark = substr($goods_image, stripos($goods_image, '?x-oss-process='));
            if ($watermark) {
                if (count($images) > 0) {
                    $mainImage = $images[0];
                    if (stripos($mainImage, 'oss.upetmart.com') !== false) { //阿里云图片
                        $mainImage = substr($mainImage, 0, stripos($mainImage, '?x-oss-process=style'));
                        if ($mainImage == "") {
                            $mainImage = $images[0];
                        }
                        if (stripos($mainImage, '?x-oss-process=') !== false) {
                            $images[0] = $mainImage;
                        } else {
                            $images[0] = $mainImage . $watermark;
                        }
                    }
                }
            }
        }

        return $images;
    }

    /**
     * Notes:添加医保会员价商品活动记录
     * 判断是否有p_time记录
     * 1.vip_state =2 时，有则更新时间数据，没有则创建
     * 2.vip_state=0时，有则更新为无效
     * User: rocky
     * DateTime: 2023/6/25 17:19
     */
    public function insuranceGoods($data, $vip_state=0)
    {
        $promotion_type_arr = array( //订单类型
            2 => "[限时折扣]",
            3 => "[秒杀]",
            5 => "[拼团]",
            6 => "[周期购]",
            7 => "[新人专享]",
            8 => "[预售]",
            9 => "[秒杀]",
            99 => "[助力]"
        );

        /** @var p_timeModel $p_time_mod */
        $p_time_mod = Model('p_time');
        if ($vip_state == 2) {
            $condition = array();
            $condition['end_time'] = array('gt', time());
            $condition['goods_id'] = $data['goods_id'];
            $condition['promotion_type'] = ['neq', 10];
            $p_time = $p_time_mod->getInfo($condition);
            if ($p_time) {
                return callback(false, '商品编辑失败,该商品已参与' . $promotion_type_arr[$p_time['promotion_type']]);
            }
            //判断付费会员、免费会员、赠品
            if ($data['enable_member_price'] == 1){
                return callback(false, '商品编辑失败,该商品已参与[免费会员]');
            }
            if ($data['have_gift'] == 1){
                return callback(false, '商品编辑失败,该商品已参与[赠品]');
            }
            if ($data['vip_state'] == 1){
                return callback(false, '商品编辑失败,该商品已参与[付费会员]');
            }
        }

        $condition = array();
        $condition['end_time'] = array('gt', time());
        $condition['goods_id'] = $data['goods_id'];
        $condition['promotion_type'] = 10;
        $p_time_info = $p_time_mod->getInfo($condition);
        if ($vip_state == 2) {
            if ($p_time_info) {
                $p_time_mod->edit(['log_id' => $p_time_info['log_id']], [
                    'promotion_price' => $data['goods_price'],
                ]);
            } else {
                $p_time_mod->add([
                    'start_time' => time(),
                    'end_time' => strtotime('+10 years', time()),
                    'goods_id' => $data['goods_id'],
                    'promotion_id' => 1,
                    'promotion_price' => $data['goods_price'],
                    'promotion_type' => 10,
                    'is_update' => 1,
                    'state' => 1,
                ]);
            }
        } else {
            if ($p_time_info) {
                $p_time_mod->del(['goods_id' => $data['goods_id'], 'promotion_type' => 10]);
            }
        }
        return callback();
    }

    /**
     * 宠商云库存同步Es
     * @param $goodsid_array
     * @param $store_id
     */
    public function SynsGoodsHandleToEs($goods_ids, $store_id, $type = 0)
    {
        $desc = $this->getDescriptionForType($type);

        if (is_string($goods_ids)) {
            $goods_ids = explode(',', $goods_ids);
        }

        log_info('SynsEshopGoodsHandle', [
            'desc' => $desc,
            'store_id' => $store_id,
            'goods_ids' => implode(',', $goods_ids)
        ]);

        $batch_size = 100; // Reduced batch size
        for ($i = 0; $i < count($goods_ids); $i += $batch_size) {
            $batch_data = array_slice($goods_ids, $i, $batch_size);
            $this->logGoodsHandle($batch_data, $store_id, $desc);
        }
    }

    private function getDescriptionForType($type)
    {
        $descriptions = [
            1 => '商品上架',
            2 => '商品下架',
            3 => '商品删除',
            4 => '库存更新',
            5 => '更新商品促销',
        ];

        return $descriptions[$type] ?? '商品编辑';
    }

    private function logGoodsHandle($goods_ids, $store_id, $desc)
    {
        $data_handle_log = [
            'gh_good_ids' => implode(',', $goods_ids),
            'gh_notes' => $desc,
            'gh_addtime' => time(),
            'store_id' => $store_id
        ];
        GoodsHandleLog::insert($data_handle_log);
    }

    private function handleStore3Goods($goods_ids, $store_id, $desc)
    {
        $shop_ids = GoodsEshop::getShopId($goods_ids);
        log_info('SynsEshopGoodsHandle 2', [
            'desc' => $desc,
            'count' => count($shop_ids),
            'shop_ids' => implode(',', $shop_ids),
            'goods_ids' => implode(',', $goods_ids)
        ]);

        $data_handle_log = [];
        foreach ($shop_ids as $k => $shop_id) {
            $data_handle_log[$k] = [
                'gh_good_ids' => implode(",", $goods_ids),
                'gh_notes' => $desc,
                'gh_addtime' => time(),
                'store_id' => $store_id,
                'shop_id' => $shop_id
            ];
        }
        $this->batchInsertHandleLogs($data_handle_log);
    }

    private function batchInsertHandleLogs(array $data_handle_log)
    {
        $model_goods = Model('goods');
        $batch_size = 100; // Reduced batch size
        for ($i = 0; $i < count($data_handle_log); $i += $batch_size) {
            $batch_data = array_slice($data_handle_log, $i, $batch_size);
            //调用重试机制
            RetryOperation(function() use ($model_goods, $batch_data) {
                $model_goods->addGoodsHandlelogAll($batch_data);
            }, 'Failed to log goods handle');
        }
    }

}
