<?php
/**
 * 周期购推单
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
defined('InShopNC') or exit('Access Invalid!');

use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Modules\Order\Queues\SyncDatacenterOrderPayQueue;

class cycleLogic
{
    /**
     * @var 推送数据
     */
    public $push_data;
    /**
     * @var 订单拓展信息
     */
    public $cycle_data;
    /**
     * @var 主订单信息
     */
    public $order_info;
    /**
     * @desc 查询周期购活动当天有推送的订单
     */
    public function cycleOrderPush($parent_order_sn, $ymd='')
    {
        $cyclePushInfo_mod = Model('cycle_push_info');
        $model_order = new Model('orders');
//        $model_order = Model('order');
        $model_order_common = Model('order_common');
        $model_order_goods = Model('order_goods');
        if ($ymd){
            $data = $ymd;
        }else{
            $data = date('Y-m-d');
        }
        $condition = 'is_head =1 and status=1 and refund_status=0 and push_date like "%'.$data.'%" and parent_order_sn='.$parent_order_sn;//test
        $res = $cyclePushInfo_mod->where($condition)->select();
        if (!$res){
            return true;
        }
        foreach ($res as $v) {
            //计算推单日期
            $pushdate = explode(',', $v['push_date']);
            if ($pushdate[$v['push_num']] == $data) {
                $this->cycle_data = $v;
                $this->push_data = json_decode($v['push_param'], true);
                try {

                    $model_order->beginTransaction();
                    $this->order_info = $model_order->where(['order_sn' => $v['parent_order_sn']])->find();
                    if (empty($this->order_info)) {
                        throw new Exception('周期购订单查询失败');
                    }
                    $pay_info = $this->createOrderPay();
                    if (!$pay_info) {
                        throw new Exception('周期购子订单保存失败[未生成支付单]');
                    }
                    //创建订单

                    $order = $this->order_info;
                    $order['order_sn'] = $this->makeOrderSn($pay_info['order_pay_id']);
                    $order['add_time'] = TIMESTAMP;
                    $order['payment_time'] = TIMESTAMP;
                    $order['order_state'] = 20;
                    $order['refund_state'] = 0;
                    $order['lock_state'] = 0;
                    $order['delete_state'] = 0;
                    $order['is_head'] = 0;
                    $order['is_live'] = $this->order_info['is_live'];
                    $order['erp_order_id'] = 0;
                    $order['shipping_fee'] = $order['shipping_fee']>0?$this->subOrderPrice($order['shipping_fee']):0;
                    $order['order_amount'] = $this->subOrderPrice($this->order_info['order_amount']);
                    $order['goods_amount'] = $this->subOrderPrice($this->order_info['goods_amount']);
                    $order['pd_amount'] = $order['pd_amount']>0?$this->subOrderPrice($order['pd_amount']):0;
                    $order['refund_amount'] = 0;
                    unset($order['order_id']);
                    $order_id = Model('order')->addOrder($order);
                    if (!$order_id) {
                        throw new Exception('周期购订单保存失败');
                    }

                    $order_common = $model_order_common->where(['order_id' => $this->order_info['order_id']])->find();
                    $order_common['voucher_price'] = $order_common['voucher_price'] > 0 ? $this->subOrderPrice($order_common['voucher_price']) : 0;
                    $order_common['promotion_total'] = $order_common['promotion_total'] > 0 ? $this->subOrderPrice($order_common['promotion_total']) : 0;
                    $order_common['order_id'] = $order_id;
                    //收货信息可能变更 todo
                    $order_common_id = Model('order')->addOrderCommon($order_common);
                    if (!$order_common_id) {
                        throw new Exception('周期购订单保存失败[未生成订单扩展数据]');
                    }

                    $order_goods = $model_order_goods->where(['order_id' => $this->order_info['order_id']])->find();
                    $order_goods['order_id'] = $order_id;
                    $order_goods['goods_pay_price'] = $this->subOrderPrice($order_goods['goods_pay_price']);
                    $order_goods['goods_price'] = $this->subOrderPrice($order_goods['goods_price']);
                    unset($order_goods['rec_id']);
                    //分销业绩计算 （主订单不计算，只作子订单佣金） todo
                    $order_goods_id =  $model_order_goods->insert($order_goods);
//                    $order_goods_id = $this->createOrderGoods($order_id);
                    if (empty($order_goods_id)) {
                        throw new Exception('周期购订单保存失败[未生成商品数据01]');
                    }
                    //添加订单日志
                    $log_data = [];
                    $log_data['order_id'] = $order_id;
                    $log_data['log_role'] = '买家';
                    $log_data['log_msg'] = '生成周期购订单';
                    $log_data['log_user'] = $this->order_info['buyer_name'];
                    $log_data['log_orderstate'] = ORDER_STATE_PAY;
                    $order_log_id = Model('order')->addOrderLog($log_data);
                    if (empty($order_log_id)) {
                        throw new Exception('周期购订单记录保存失败');
                    }

                    //添加子订单推送记录
                    $order_split = $this->push_data;
                    foreach ($order_split as $k => $vv){
                        if ($k == 'order'){
                            $order_split[$k]['old_order_sn'] = $order['order_sn'];
                            $order_split[$k]['goods_total'] = (int)$this->subOrderPrice($vv['goods_total']);
                            $order_split[$k]['total'] = (int)$this->subOrderPrice($vv['total']);
                            $order_split[$k]['freight'] = (int)$this->subOrderPrice($vv['freight']);
                            $order_split[$k]['privilege'] = (int)$this->subOrderPrice($vv['privilege']);
                        }
                        if ($k =='order_products') {
                            $order_split['order_products'][0]['order_id'] = $order_id;
                            $order_split['order_products'][0]['goods_price'] = (int)$this->subOrderPrice($vv[0]['goods_price']);
                            $order_split['order_products'][0]['goods_pay_price'] = (int)$this->subOrderPrice($vv[0]['goods_pay_price']);;
                            $order_split['order_products'][0]['discount_price'] = (int)$this->subOrderPrice($vv[0]['discount_price']);;
                            $order_split['order_products'][0]['price'] = (int)$this->subOrderPrice($vv[0]['price']);
                        }
                        if ($k == 'pay_info'){
                            $order_split['pay_info'] = (object)[];
                        }
                    }
                    //添加每期订单推送数据
                    $model_cycle = Model('cycle_push_info');
                    $push_data['order_sn'] = $order['order_sn'];
                    $push_data['parent_order_sn'] = $v['parent_order_sn'];
                    $push_data['is_head'] = 0;
                    $push_data['cid'] = $v['cid'];
                    $push_data['member_id'] = $v['member_id'];
                    $push_data['cycle_num'] = $v['cycle_num'];
                    $push_data['cycle_price'] = $v['cycle_price'];
                    $push_data['delivery_interval'] = $v['delivery_interval'];
                    $push_data['push_date'] = $data;
                    $push_data['push_type'] = $v['push_num']+1;
                    $push_data['message_type'] = 0;
                    $push_data['push_param'] = json_encode($order_split,JSON_UNESCAPED_UNICODE);
                    $push_data['promotion_total'] = $order_split['order']['privilege'];
                    $push_insert_res = $model_cycle->addCyclePushInfo($push_data);
                    if (empty($push_insert_res)) {
                        throw new Exception('周期购推单记录添加失败');
                    }
                    //更新推送数+1
                    $update = [];
                    $update['push_num'] = ['exp', 'push_num+1'];
                    $push_res = $cyclePushInfo_mod->where(['id' => $v['id']])->update($update);
                    if (empty($push_res)) {
                        throw new Exception('周期购推单记录更新失败');
                    }

                    //事务提交
                    $model_order->commit();

                    //获取商品库存，以解决redis没有库存问题
                    $orderGoods = $model_order_goods->where(['order_id' => $order_id])->find();
                    if (empty($orderGoods)) {
                        throw new Exception('周期购订单商品查询失败');
                    }
                    Model('goods')->getStock(array(array('goodsid' => $orderGoods['goods_id'], 'goods_type' => (int)$orderGoods['goods_type'])));

                    //推送数据中心标识请求头是否微信广告
                    if ($order['click_id']){
                        $_SERVER['HTTP_CLICK_ID'] = $order['click_id'];
                    }
                    //推单
                    $res = $this->push_datacenter_order($order_split, $this->order_info['encrypt_mobile'], 9, $order['order_sn'],$order_split['order']['user_agent']);

                    if (!$res){
                        return false;
                    }

                    //支付通知数据 中心
                    $push_order = [
                        'order_id' => $order_id,
                        'order_sn' => $order['order_sn'],
                        'add_time' => $order['add_time'],
                        'buyer_phone' => $this->order_info['buyer_phone'],
                        'payment_time' => $this->order_info['payment_time'],
                        'payment_code' => $this->order_info['payment_code'],
                        'order_amount' => $order['order_amount'],
                        'trade_no' => $this->order_info['trade_no'],
                    ];
                    SyncDatacenterOrderPayQueue::dispatch($push_order, $push_order, 9, $this->order_info['encrypt_mobile']);
                    return true;
                } catch (Exception $e) {
                    Redis::lPush('erp_cronCycleOrderPush', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => $this->push_data['parent_order_sn'], 'result' => $e->getMessage()], JSON_UNESCAPED_UNICODE));
                    //事务回滚
                    $model_order->rollback();
                    return false;
                }
            }
        }
    }

    /**
     * @param $order_data 订单信息
     * @date 2021-07-20
     * @auth by zhuoyw
     */
    public function push_datacenter_order($order_data, $phone, $type = 0, $order_sn, $user_agent)
    {
        $cyclePushInfo_mod = new Model('cycle_push_info');
        $jsonstr = json_encode($order_data,JSON_UNESCAPED_UNICODE);
        list($code, $response) = http_post_json(C('datacenter_orderpay_url') . "/order-api/order/docommit", $jsonstr, $phone, $order_data['order']['old_order_sn'], $type, "5", $user_agent);
        $order_arr = json_decode($response, true);
        if ($code != '200' || $order_arr['code'] != '200') {
            $update = [];
            $update['status'] = 2;
            $cyclePushInfo_mod->where(['order_sn' => $order_sn])->update($update);
            //判断推单
            Redis::lPush('cycle_datacenter_return_error', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => $order_data, 'result' => $order_arr], JSON_UNESCAPED_UNICODE));
            return false;
        }
        return true;
    }

    /**
     * @return array|false
     */
    public function createOrderPay()
    {
        $model_order_pay = new Model('order_pay');
        $pay_sn = $this->makePaySn($this->push_data['member_id']);
        $order_pay = array();
        $order_pay['pay_sn'] = $pay_sn;
        $order_pay['buyer_id'] = $this->order_info['buyer_id'];
        $order_pay['api_pay_state'] = 1;
        $order_pay_id = $model_order_pay->insert($order_pay);
        if (!$order_pay_id) {
            return false;
        }
        return ['order_pay_id' => $order_pay_id, 'pay_sn' => $pay_sn];
    }

    /**
     * @desc 创建订单
     * @param $pay_info
     * @return false
     */
    public function createOrder($pay_info)
    {
        $model_order = Model('order');
        $order = $this->order_info;
        $order['order_sn'] = $this->makeOrderSn($pay_info['order_pay_id']);
        $order['pay_sn'] = $pay_info['pay_sn'];
        $order['add_time'] = TIMESTAMP;
        $order['order_amount'] = $this->subOrderPrice($this->order_info['order_amount']);
        $order['goods_amount'] = $this->subOrderPrice($this->order_info['goods_amount']);
        unset($order['order_id']);
        return $model_order->addOrder($order);
    }

    /**
     * @desc 计算金额
     * @param $price
     * @return string|null
     */
    public function subOrderPrice($price)
    {
        $cycle_num = intval($this->cycle_data['cycle_num']);
        $push_num = intval($this->cycle_data['push_num']);
        if ($cycle_num == 1) {
            return $price;
        }
        //每一期的价格
        $echoPrice = sprintf("%.2f", ($price/$cycle_num));
        //剩下推送的期数
        $num = $cycle_num - $push_num;
        if ($num > 1) {
            return $echoPrice;
        }
        $hadPirce = sprintf("%.2f", ($echoPrice*$push_num));
        $lastPrice = sprintf("%.2f", ($price - $hadPirce));
        return $lastPrice;
    }

    public function createOrderCommon($order_id)
    {
        $model_order_common = Model('order_common');
        $model_order = Model('order');
        $order_common = $model_order_common->where(['order_id' => $this->order_info['order_id']])->find();
        $order_common['voucher_price'] = $order_common['voucher_price'] > 0 ? $this->subOrderPrice($order_common['voucher_price']) : 0;
        $order_common['promotion_total'] = $order_common['promotion_total'] > 0 ? $this->subOrderPrice($order_common['promotion_total']) : 0;
        $order_common['order_id'] = $order_id;
        //收货信息可能变更 todo
        return $model_order->addOrderCommon($order_common);
    }

    public function createOrderGoods($order_id)
    {
        $model_order_goods = new Model('order_goods');
        $order_goods = $model_order_goods->where(['order_id' => $this->order_info['order_id']])->find();
        $order_goods['order_id'] = $order_id;
        $order_goods['goods_pay_price'] = $this->subOrderPrice($order_goods['goods_pay_price']);
        $order_goods['goods_price'] = $this->subOrderPrice($order_goods['goods_price']);
        unset($order_goods['rec_id']);
        //分销业绩计算 （主订单不计算，只作子订单佣金） todo
        return $model_order_goods->insert($order_goods);
    }

    /**
     * @param $pay_id
     * @return string
     */
    public function makeOrderSn($pay_id)
    {
        //记录生成子订单的个数，如果生成多个子订单，该值会累加
        static $num;
        if (empty($num)) {
            $num = 1;
        } else {
            $num++;
        }
        return (date('y', time()) % 9 + 1) . sprintf('%013d', $pay_id) . sprintf('%02d', $num);
    }

    /**
     * @param $member_id
     * @return string
     */
    public function makePaySn($member_id)
    {
        return mt_rand(10, 99)
            . sprintf('%010d', time() - 946656000)
            . sprintf('%03d', (float)microtime() * 1000)
            . sprintf('%03d', (int)$member_id % 1000);
    }
}
