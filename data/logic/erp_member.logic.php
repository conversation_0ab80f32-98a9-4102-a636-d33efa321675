<?php
/**
 * 操作ERP会员类
 * <AUTHOR>
 * @date 2018.10.23
 */

use Upet\Models\Member as Member;

defined('InShopNC') or exit('Access Invalid!');
class erp_memberLogic {	
	
 	public function __construct(){
       define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');      
       require_once SCRIPT_ROOT.'/base/'.'member.php';
    }
    
   /**
     * 会员添加
     * @param int $mobile 手机号码
     */
    public function memberadd($mobile,$passname=false){ 

        $param=[];
        $param['Mobile']=$mobile;
        if(!$passname){
         $memberModel = Model('member');
         $member_info = $memberModel->getMemberInfo(array("member_mobile"=>$mobile));

        /* $param['Membername']='测试用户名称';
         $param['Nickname']='测试昵称';
        $param['BrandName']='瑞鹏'; */

        $param['Membername']=$member_info['member_name'];
        $param['Nickname']=$member_info['member_name'];
        }else{
            $param['Membername']="阿闻用户";
            $param['Nickname']="阿闻用户";
        }
        
        $param['Membersource']='4';
        
        $member = new Member();
        $result = $member->baseMemberadd($param);
        //wkcache("erp_base_Member_add_" . $mobile, $result, 7200);
        if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态            
            return  $result['msg'];     
        }else{          
            return false;
        }
    }

    /**
     * 注册scrm会员
     * @param $mobile
     * @return mixed|string
     */
    public function scrmMemberAdd($mobile)
    {
        $memberModel = Model('member');
        $member_info = $memberModel->getMemberInfo(array("member_mobile" => $mobile), "member_id,scrm_user_id,member_name", true);
        $userName = "";
        if (is_array($member_info) && !empty($member_info)) {
            if (!$member_info['scrm_user_id']) {
                $userName = $member_info['member_name'];
            } else {
                return $member_info['scrm_user_id'];
            }
        } else {
            $userName = "电商注册用户";
        }
        if ($userName) {
            $path = '/scrm-organization-api/user/addScrmNewUserPet';
            $data = [
                'userName' => $userName,
                'userMobile' => $mobile
            ];
            $result = getAwasqContent($path, $data);
            if (is_array($result) && !empty($result) && $result['statusCode'] == 200) {
                if ($result['result']['userId']) {
                    $memberModel->editMember(array("member_mobile" => $mobile), array('scrm_user_id' => $result['result']['userId']));
                    return $result['result']['userId'];
                } else {
                    return "";
                }
            } else {
                redis_error_log($mobile, $data, "注册SCRM会员异常", "syns_scrm_member_add_error_list");
                return "";
            }
        }
    }

    /**
     * 同步scrm会员信息
     * @param $member_info
     * @return mixed|string
     */
    public function syncScrmMember($member_info)
    {
        $userName = $member_info['member_name'];
        $path = '/scrm-organization-api/user/addScrmNewUserPet';
        $data = [
            'userName' => $userName,
            'userMobile' => $member_info['member_mobile']
        ];
        $result = getAwasqContent($path, $data);
        if (is_array($result) && !empty($result) && $result['statusCode'] == 200) {
            if ($result['result']['userId']) {
                return $result['result']['userId'];
            } else {
                return "";
            }
        } else {
            redis_error_log($member_info['member_mobile'], $data, "注册SCRM会员异常", "syns_scrm_member_add_error_list");
            return "";
        }
    }

    //用户登陆
    public function memberlogin($mobile){  
    	
    	/*if(!preg_match("/^1[345678]{1}\d{9}$/",$mobile)){
    		return false;
    	}   */
    
    	$param=[];    
    	$param['mobile']= $mobile; //手机号码
    	
    	$member = new Member();
    	$result=$member->login($param);
    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$token=$result['msg']; 

    		//解析token 取出jwt
    		$rsa=new Rsa();
    		$token=$rsa->privDecrypt($token);
    		
    		$array=explode('*', $token);
    		$jwt=$array[1];
    		
    		$token = substr($jwt, strpos($jwt,".")+1, strrpos($jwt,".")- strlen($jwt));
    		$arr = json_decode(base64_decode($token),true);
    		$exp=strtotime($arr['exp']);    		
    		
    		$data=array();
    		$data['jwt'] = $jwt;
    		$data['info'] = $arr;
    		$data['expire'] = $exp;
            $vip_data = [];
            $vip_data['member_isvip'] = $arr['isVip'] ? $arr['isVip'] : 0;
            $vip_data['member_isbzk'] = $arr['isBZK'] ? $arr['isBZK'] : 0;
            $vip_data['member_vipstime'] = intval($arr['VipSTime']) > 0 ? $arr['VipSTime'] : 0;
            $vip_data['member_vipetime'] = intval($arr['VipETime']) > 0 ? $arr['VipETime'] : 0;
            $vip_data['member_bzkstime'] = intval($arr['BZKSTime']) > 0 ? $arr['BZKSTime'] : 0;
            $vip_data['member_bzketime'] = intval($arr['BZKETime']) > 0 ? $arr['BZKETime'] : 0;
            Model('member')->editMember(['member_mobile' => $mobile],$vip_data);
    		wkcache($mobile, $data,$exp-time()); //存储redis
    		return $data;
    	} else{
    		return false;
    	}
    }
        
    //登录请求ERP
    public function loginERP($mobile){
        $member = Model('member')->getMemberInfo([
            'member_mobile' => $mobile
        ],'scrm_user_id');
        $scrmId = $member['scrm_user_id'];

        $token = Logic('connect_api')->getJwtToken([
            'nameid' => $scrmId,
            'mobile' => $mobile
        ]);

    	$_SESSION['jsession'] = [
    	    'jwt' => $token
        ];
    	
    	return $token;
    }

    public function datacenterMemberInfo($mobile)
    {
        $url    = C('erp_url_host')."/api/member/get";
        $param  = array();
        $param['mobileCiphertext']  = "RP".$mobile;
        $result = apiRequest($url,$param);
        if(isset($result['code']) && $result['code'] == 200){ //判断http状态
            $token = $result['data']['token'];
            $exp = explode('.', $token);
            $login_info = json_decode(base64_decode($exp[1]),true);
            $exp = strtotime($login_info['exp']);
            $data=array();
            $data['jwt']    = $token;
            $data['info']   = $login_info;
            $data['expire'] = strtotime($login_info['exp']);
            wkcache($mobile, $data,$exp-time()); //存储redis
            return $data;
        } else{
            return false;
        }

    }

    public function onlyLoginERP($mobile){
        if(empty($mobile)){
            return false;
        }
        $token=rkcache($mobile);
        if(!$token){
            $token=$this->memberlogin($mobile);
        }
        //当前ERP登录
        //wkcache("a_erp_now_login_token", $token,$token['expire']-time()); //存储redis
        $_SESSION['jsession'] = $token;
        return $token;
    }

    public function getMemberInfo() {
        $param=[];
        //$param['memberIdStr'] = $memberId; //用户Id,逗号分隔
        $param['isvalid'] = 'true';  //true 只显示有效用户,false 显示所有用户
        $member = new Member();
        $result=$member->basicmemberinfo($param);
        $arrResult = json_decode($result,true);
        if (isset($arrResult['http_code']) && $arrResult['http_code'] == '200') {
            $message=json_decode($arrResult['msg'],1);
            $data=$message;
            if(is_array($data)){
                return $data;//true\false
            }else{
                return [];
            }
        } else {
            return [];
        }
    }
		
    
}
