<?php
/**
 * 操作ERP会员类
 * <AUTHOR>
 * @date 2018.10.23
 */
defined('InShopNC') or exit('Access Invalid!');
class erp_memberbakLogic {
	
 	public function __construct(){
 	    header("Content-Type: text/html;charset=utf-8");
       define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
       require_once SCRIPT_ROOT.'/base/'.'member.php';
    }
    
    /**
     * 会员添加
     * @param int $mobile 手机号码
     */
    public function memberadd($mobile){ 

    	$param=[];
    	$param['Mobile']=$mobile;
    	$memberModel = Model('member');
         $member_info = $memberModel->getMemberInfo(array("member_mobile"=>$mobile));

        /* $param['Membername']='测试用户名称';
         $param['Nickname']='测试昵称';
        $param['BrandName']='瑞鹏'; */

        $param['Membername']=$member_info['member_name'];
        $param['Nickname']=$member_info['member_name'];
        $param['Membersource']='4';
    	
   	    $member = new Member();
    	$result=$member->baseMemberadd($param);
                    
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态    		
    		return  $result['msg'];    	
    	}else{
    		return false;
    	}
    }
	
    //用户登陆
    public function memberlogin($mobile){  
    	
    	/*if(!preg_match("/^1[345678]{1}\d{9}$/",$mobile)){
    		return false;
    	}   */
    
    	$param=[];    
    	$param['mobile']= $mobile; //手机号码
    	
    	$member = new Member();
    	$result=$member->login($param);
    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$token=$result['msg']; 

    		//解析token 取出jwt
    		$rsa=new Rsa();
    		$token=$rsa->privDecrypt($token);
    		
    		$array=explode('*', $token);
    		$jwt=$array[1];
    		
    		$token = substr($jwt, strpos($jwt,".")+1, strrpos($jwt,".")- strlen($jwt));
    		$arr = json_decode(base64_decode($token),true);
    		$exp=strtotime($arr['exp']);    		
    		
    		$data=array();
    		$data['jwt'] = $jwt;
    		$data['info'] = $arr;
    		$data['expire'] = $exp;
    		if (!empty($arr['isVip']) || !empty($arr['isBZK'])) {
                $vip_data = [];
                $vip_data['member_isvip'] = $arr['isVip'];
                $vip_data['member_isbzk'] = $arr['isBZK'];
                $vip_data['member_vipstime'] = intval($arr['VipSTime']) > 0 ? $arr['VipSTime'] : 0;
                $vip_data['member_vipetime'] = intval($arr['VipETime']) > 0 ? $arr['VipETime'] : 0;
                $vip_data['member_bzkstime'] = intval($arr['BZKSTime']) > 0 ? $arr['BZKSTime'] : 0;
                $vip_data['member_bzketime'] = intval($arr['BZKETime']) > 0 ? $arr['BZKETime'] : 0;
                Model('member')->editMember(['member_mobile' => $mobile],$vip_data);
            }
    		wkcache($mobile, $data,$exp-time()); //存储redis
    		return $data;
    	} else{
    		return false;
    	}
    }
        
    //登录请求ERP
    public function loginERP($mobile){ 
    	
    	if(empty($mobile)){
    		return false;
    	}
    	$add=$this->memberadd($mobile);
    	$token=rkcache($mobile);

    	if(!$token){    		
    		$token=$this->memberlogin($mobile);
    	}

    	//当前ERP登录
    	//wkcache("a_erp_now_login_token", $token,$token['expire']-time()); //存储redis    	
    
    	$_SESSION['jsession'] = $token;    	
    	
    	return $token;
    	
    }

    public function onlyLoginERP($mobile){
        if(empty($mobile)){
            return false;
        }
        $token=rkcache($mobile);
        if(!$token){
            $token=$this->memberlogin($mobile);
        }
        //当前ERP登录
        //wkcache("a_erp_now_login_token", $token,$token['expire']-time()); //存储redis
        $_SESSION['jsession'] = $token;
        return $token;
    }

    public function getMemberInfo() {
        $param=[];
        //$param['memberIdStr'] = $memberId; //用户Id,逗号分隔
        $param['isvalid'] = 'true';  //true 只显示有效用户,false 显示所有用户
        $member = new Member();
        $result=$member->basicmemberinfo($param);
        $arrResult = json_decode($result,true);
        if (isset($arrResult['http_code']) && $arrResult['http_code'] == '200') {
            $message=json_decode($arrResult['msg'],1);
            $data=$message;
            if(is_array($data)){
                return $data;//true\false
            }else{
                return [];
            }
        } else {
            return [];
        }
    }
		
    
}
