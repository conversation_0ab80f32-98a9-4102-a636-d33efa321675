<?php

defined('InShopNC') or exit('Access Invalid!');

class member_searchLogic
{
    /**
     * 搜索关键词记录
     *
     * @param $search
     * @param null $memberId
     */
    public function record($search, $memberId = null)
    {
        $search = trim($search);

        if (empty($search)) { // 关键字为空不处理
            return;
        }

        $memberSearch = $this->getMemberSearch($search);

        Model('member_search_history')->insert([
            'member_id' => $memberId,
            'member_search_id' => $memberSearch['id'],
            'time' => time()
        ]);

        // 计数自增
        Model('member_search')->where([
            'id' => $memberSearch['id']
        ])->setInc('count');
    }

    /**
     * 获取搜索关键字模型id
     *
     * @param $search
     * @return int
     */
    private function getMemberSearch($search)
    {
        $memberSearch = Model('member_search')->getInfo([
            'search' => $search
        ]);

        if ($memberSearch) {  // 存在记录
            return $memberSearch;
        }

        $prefix = C('tablepre');

        // 当存在记录时忽略语句
        Model()->query("insert ignore into {$prefix}member_search(search) values('$search');");
        $data = Model('member_search')->getInfo(['search' => $search],"*",true);
        return $data;
    }
}