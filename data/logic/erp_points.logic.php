<?php
/**
 * 操作ERP积分类
 * <AUTHOR>
 * @date 2018.10.23
 */

use Upet\Integrates\Http\DatacenterHttp;

defined('InShopNC') or exit('Access Invalid!');
class erp_pointsLogic {	
	
 	public function __construct(){
 	   header("Content-Type: text/html;charset=utf-8");
       define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');      
       require_once SCRIPT_ROOT.'/order/'.'points.php';
       require_once SCRIPT_ROOT.'/base/'.'member.php';
    }
	
    /**
     * 我的积分
     * @param string $token 用户标识
     * @return float:
     */
    public function userPoints($token){
    	$param=[];
    	$param['token']=$token; //用户标识
    	 
    	$points = new Points();
    	$result = $points->my($param);
    	$arrResult = json_decode($result,true);
    	if(isset($arrResult['http_code'])&&$arrResult['http_code']=='200'){ //判断http状态
    		$data = $arrResult['msg'];
    		$point = substr($data,1,-1);
    		
    		if($point){
    			return $point;
    		}else{
    			return 0;
    		}
    	}
    }

    /**
     * 我的积分
     * @param string $phone 用户手机
     */
    public function getUserPoints($phone)
    {
        try {
            return DatacenterHttp::withAuth($phone)
                ->orderIntegralMy();
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * 冻结积分
     */
    public function freeze($token,$point) {
    	$param=[];
    	$param['token']=$token; //用户标识
    	$param['integralCount']=$point; //用户标识
    	
    	$points = new Points();
    	$result = $points->freeze($param);
    	//$arrResult = json_decode($result,true);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$data = $result['msg'];
    		if($data){
    			return $data;
    		}else{
    			return '';
    		}
    	}else {
    		return  '';
    	}
    }
    
    /**
     * 取消冻结积分
     */
    public function cancel($token,$code) {
    	$param=[];
    	$param['token']=$token; //用户标识
    	$param['integralCount']=$code; //用户标识
    	 
    	$points = new Points();
    	$result = $points->cancel($param);
    	//$arrResult = json_decode($result,true);
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		return 1;
    		/* $data = $result['msg'];
    		if($data){
    			return $data;
    		}else{
    			return '';
    		} */
    	}else {
    		return  0;
    	}
    }
    
    /**
     * 积分明细列表
     */
    public function pointsList($params) {
    	$param=[];
    	$param['token'] = $params['token']; //用户标识
    	$param['pageindex'] = $params['pageindex']; //分页
    	$param['pagesize'] = $params['pagesize']; //页显示数量
    	 
    	$points = new Points();
    	$result = $points->infoList($param);
    	$arrResult = json_decode($result,true);wkcache("point_list",$arrResult,3600);
    	if(isset($arrResult['http_code'])&&$arrResult['http_code']=='200'){ //判断http状态
    		$data = $arrResult['msg'];
    		$pointList=json_decode($data,true);//echo '<pre>';print_r($pointList);
    		$integrallist=$pointList['integrallist'];
            $integrallistcount = $pointList['integrallistcount'];
    		if(is_array($integrallist)){
    			return array($integrallist,$pointList['totalintegral'],$integrallistcount);//true\false
    		}else{
    			return [];
    		}
    	}else {
    		return  [];
    	}
    }

    /**
     * 兑换优惠券
     */
    public function getCouponInfo($couponid,$uuid) {
        $param=[];
        $param['templateIdArr'] = [$couponid]; //用户标识107
        $param['Number'] = 1; //数量
        $param['Uuid'] = [$uuid]; //ERP 用户ID 25ee3e148e02d55c
        $param['Source'] = "2"; //来源

        $points = new Points();
        $result = $points->couponInfo($param);
        $result_data = json_decode($result,true);
        if(isset($result_data['code'])&&$result_data['code']=='200') { //判断http状态
            $data=$result_data['data'];
            if(is_array($data)){
                return $data[0];
            }else{
                return [];
            }
        }else{
            return [];
        }
    }
    
    /**
     * 提交订单
     */
    public function addOrder($param) {
    	$points = new Points();
    	$result=$points->Orderadd($param);
    	//pp($result);
    	 
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    }
    
    /**
     * 通过手机号获取订单
     */
    public function getMobileOrder($param) {
    	$points = new Points();
    	$result=$points->getMobieOrder($param);

    	
    	if(isset($result['http_code'])&&$result['http_code']=='200'){ //判断http状态
    		$message=json_decode($result['msg'],1);
    		$data=$message;
    		if(is_array($data)){
    			return $data;
    		}else{
    			return [];
    		}
    	}
    }
		
    
}
