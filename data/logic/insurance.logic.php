<?php
/**
 * 保险
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
defined('InShopNC') or exit('Access Invalid!');

class insuranceLogic
{
    public function insure($data, &$meaasge) {
        $vr_giftorder_model = Model('gift_order');
        $data_code = $vr_giftorder_model->getOrderCodeInfo(array('order_id'=>$data['order_id']), 'erp_order_id');
        if ($data && $data_code['erp_order_id']) {
            //被保人基本信息
            $insureds['IdcartNo'] = $data['safe_to_idcard'];
            //$birthday = strlen($data['safe_to_idcard'])==15 ? ('19' . substr($data['safe_to_idcard'], 6, 6)) : substr($data['safe_to_idcard'], 6, 8);
            //$insureds['BornDate'] = substr($birthday,0,4) . '-' . substr($birthday,4,2). '-' . substr($birthday,6,2);
            $insureds['IdcartType'] = str_replace(array(1, 2, 3, 4), array('01', '02', '06', '05'), $data['safe_to_type']);
            //$insureds['IdcartType']  = sprintf("%02d",$data['safe_to_type']);
            $insureds['InsuredName'] = $data['safe_to_name'];
            $insureds['Sex'] = ($data['safe_to_sex'] == 1 ? 'M' : 'F');;
            $insureds['Mobile'] = $data['buyer_phone'];
            $insureds['InsuredType'] = "1";
            $insureds['RelationWithHolder'] = "14";
            $params['insureds'][] = $insureds;
            //核销用的订单号
            $params['order_id'] = $data_code['erp_order_id'];
            $params['skuid'] = $data['goods_id'];
            //宠物信息
            /*处理照片____开始*/
            $BusinessNo = $data['order_sn'] . date('YmdHis');
            if ($data['safe_pet_img1'] && $data['safe_pet_img2'] && $data['safe_pet_img3'])
            {
                /*$zip  = new Zipfile();
                $path = BASE_DATA_PATH . "/upload/shop/zip/" . date('Y-m-d');
                if (!is_dir($path)){
                    $res = mkdir($path,0777,true);
                    if (!$res){
                        $meaasge = "目录 $path 创建失败！";
                        return false;
                    }
                }
                $filename = $path . "/{$BusinessNo}.zip";
                $zip->add_file(file_get_contents($data['safe_pet_img1'].'?x-oss-process=image/resize,m_lfit,h_1000'), "1.1.2.jpg");
                $zip->add_file(file_get_contents($data['safe_pet_img2'].'?x-oss-process=image/resize,m_lfit,h_1000'), "1.1.3.jpg");
                $zip->add_file(file_get_contents($data['safe_pet_img3'].'?x-oss-process=image/resize,m_lfit,h_1000'), "1.1.5.jpg");
                $zip->output($filename);
                $zipData = file_get_contents($filename);*/
                $safe_pet_img[] = $data['safe_pet_img1'];
                $safe_pet_img[] = $data['safe_pet_img2'];
                $safe_pet_img[] = $data['safe_pet_img3'];
                $target['Base64Str'] = implode(',', $safe_pet_img);
            }
            /*处理照片____结束*/
            $target['Birthday'] = date('Y-m-d', $data['safe_pet_birthday']);
            $target['BizMode']  = 2;
            switch ($data['safe_pet_type']){
                case 1000:
                    $target['Category'] = '1';
                    break;
                case 1001:
                    $target['Category'] = '2';
                    break;
                default:
                    $target['Category'] = '0';
                    break;
            }
            $target['CategoryName'] = $data['safe_pet_class'];
            $target['DogLicenseCode'] = $data['safe_pet_dog_no']; //养犬许可证号码
            $target['Gender'] = $data['safe_pet_sex'];
            $target['HouseAddress'] = ($data['safe_to_address'] ? $data['safe_to_address'] : '');
            $target['HouseCity'] = ($data['safe_to_city'] ? $data['safe_to_city'] : '');
            $target['Immune'] = ($data['safe_pet_immunity'] == 1 ? true : false);
            $target['ImmunityCertifiCode'] = $data['safe_pet_immunity_no']; //犬类免疫证号码
            $target['PetDogBreed'] = $data['safe_pet_class'];
            $target['PetName'] = $data['safe_pet_name'];
            $target['Sterilization'] = ($data['safe_pet_sterilization'] == 1 ? true : false);
            $params['target'] = $target;
            $insurance_model = Model('insurance');
            //流水号
            $params['policy']['BusinessNo'] = $BusinessNo;
            $params['policy']['AppTime'] = date('Y-m-d H:i:s', $data['payment_time']);
            $result = $insurance_model->insure($params);
            if ($result['code'] == 200) {
                //投保成功后的处理
                $update_data = array('order_state'=>40, 'safe_time'=>time(), 'safe_elec_addr'=>$result['results'][0]['elec_addr'], 'safe_app_no'=>$result['results'][0]['policy_app_no'], 'safe_no'=>$result['results'][0]['policy_no']);
                $vr_giftorder_model->editOrder($update_data, array('order_id'=>$data['order_id']));
                return true;
            } else {
                //投保失败后的处理
                $params['error'] = $result['error'];
                //$params['target']['Base64Str'] = '';
                $params['message'] = $meaasge = $result['message'];
                $update_data = array('safe_status'=>1, 'safe_error'=>serialize($params));
                $vr_giftorder_model->editOrder($update_data, array('order_id'=>$data['order_id']));
                return false;
            }
        } else {
            $params['message'] = $meaasge = '数据有误或ERP订单号为空！';
            return false;
        }
    }
}
?>