<?php
/**
 * 文件的简短描述
 *
 * 文件的详细描述
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
defined('InShopNC') or exit('Access Invalid!');

class navigationModel extends Model {
    /**
     * 列表
     *
     * @param array $condition 检索条件
     * @param obj $page 分页
     * @return array 数组结构的返回结果
     */
    public function getNavigationList($condition,$page = ''){
        $condition_str = $this->_condition($condition);
        $param = array();
        $param['table'] = 'navigation';
        $param['where'] = $condition_str;
        $param['order'] = $condition['order'] ? $condition['order'] : 'nav_id';
        $result = $this->select1($param,$page);
        return $result;
    }

    /**
     * 构造检索条件
     *
     * @param int $id 记录ID
     * @return string 字符串类型的返回结果
     */
    private function _condition($condition){
        $condition_str = '';

        if ($condition['like_nav_title'] != ''){
            $condition_str .= " and nav_title like '%". $condition['like_nav_title'] ."%'";
        }
        if ($condition['nav_location'] != ''){
            $condition_str .= " and nav_location = '". $condition['nav_location'] ."'";
        }

        return $condition_str;
    }

    /**
     * 取单个内容
     *
     * @param int $id ID
     * @return array 数组类型的返回结果
     */
    public function getOneNavigation($id){
        if (intval($id) > 0){
            $param = array();
            $param['table'] = 'navigation';
            $param['field'] = 'nav_id';
            $param['value'] = intval($id);
            $result = $this->getRow1($param);
            return $result;
        }else {
            return false;
        }
    }

    /**
     * 新增
     *
     * @param array $param 参数内容
     * @return bool 布尔类型的返回结果
     */
    public function add($param){
        if (empty($param)){
            return false;
        }
        if (is_array($param)){
            $tmp = array();
            foreach ($param as $k => $v){
                $tmp[$k] = $v;
            }
            $result = $this->insert1('navigation',$tmp);
            return $result;
        }else {
            return false;
        }
    }

    /**
     * 更新信息
     *
     * @param array $param 更新数据
     * @return bool 布尔类型的返回结果
     */
    public function updates($param){
        if (empty($param)){
            return false;
        }
        if (is_array($param)){
            $tmp = array();
            foreach ($param as $k => $v){
                $tmp[$k] = $v;
            }
            $where = " nav_id = '". $param['nav_id'] ."'";
            $result = $this->update1('navigation',$tmp,$where);
            return $result;
        }else {
            return false;
        }
    }

    /**
     * 删除
     *
     * @return bool 布尔类型的返回结果
     */
    public function del($condition = array()){
        return $this->table('navigation')->where($condition)->delete();
    }
}
