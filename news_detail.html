<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻详情页-加入我们-阿闻宠物</title>
    <meta name="keywords" content="阿闻宠物，宠物医疗、宠物洗美、宠物服务、宠物商品">
    <meta name="description" content="阿闻宠物，是由新瑞鹏宠物医疗集团倾情打造的全生命周期养宠管理平台，是包含了宠物医疗、宠物洗美、宠物服务、宠物商品、健康保障、以及养护常识等的一站式养宠平台。">
    <link rel="stylesheet" href="/home/<USER>/css/bootstrap.min.css">
    <link rel="stylesheet" href="/home/<USER>/news_detail.css">
    <link rel="stylesheet" href="/home/<USER>/public.css">
    <script src="/home/<USER>/js/jquery-3.6.0.min.js"></script>
    <script src="/home/<USER>/jquery.SuperSlide.2.1.3.js"></script>
    <script src="/home/<USER>/js/bootstrap.min.js"></script>
    <link rel="shortcut icon" href="/home/<USER>/favicon.ico" />
</head>

<body>
<!-- 导航 -->
<nav class="navbar navbar-default" role="navigation">
    <div class="container-fluid row">
        <div class="navbar-header col-sm-6">
            <button type="button" class="navbar-toggle" data-toggle="collapse"
                    data-target="#example-navbar-collapse">
                <span class="sr-only">切换导航</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="/"><img src="./home/<USER>/logo-pc.png"></a>
        </div>
        <div class="collapse navbar-collapse col-sm-6" id="example-navbar-collapse">
            <ul class="nav navbar-nav row">
                <li class="col-sm-3"><a href="/">首页</a></li>
                <li class="col-sm-3"><a href="/news.html">新闻动态</a></li>
                <li class="col-sm-3"><a href="/contact.html">联系我们</a></li>
                <li class="col-sm-3"><a href="/join.html">加入我们</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="news_detail-banner">
    <img src="/home/<USER>/news-banner.jpg" alt="在这里，认识阿闻宠物">
</div>
<div class="mianbao">
    <ul class="breadcrumb">
        <li><a href="/">首页</a></li>
        <li><a href="/news.html">新闻动态</a></li>
        <li class="active">正文</li>
    </ul>

</div>
<div class="news_detail">
    <div class="news_detail-main">
        <div class="news_detail-top">
            <h1 class="news_detail-main-title"></h1>
            <p class="publish_time">发布时间：<span class="span_publish_time"></span></p>
        </div>
        <div class="news_detail-content">

        </div>
        <div class="dianzan">
            <p><span class="glyphicon glyphicon-eye-open"></span></p>
        </div>
    </div>
</div>

<!-- 小程序码 -->
<div class="miniapp">
    <div class="miniapp-cont row">
        <div class="miniapp-img">
            <ul class="row">
                <li class="col-md-4 col-xs-4">
                    <img class="center-block" src="/home/<USER>/weixinapp.png" alt="阿闻小程序二维码">
                    <p>阿闻小程序</p>
                </li>
                <li class="col-md-4 col-xs-4">
                    <img class="center-block" src="/home/<USER>/weixinhao.png" alt="阿闻微信公众号">
                    <p>阿闻微信公众号</p>
                </li>
                <li class="col-md-4 col-xs-4">
                    <img class="center-block" src="/home/<USER>/baiduapp.png" alt="阿闻百度小程序">
                    <p>阿闻百度小程序</p>
                </li>
            </ul>
        </div>
        <div class="miniapp-kefu">
            <p>客服电话：021-5339-5288</p>
            <p>咨询医生：400-020-8888</p>
            <p>深圳市巨星网络科技有限公司</p>
        </div>
    </div>
</div>

<footer>
    <div class="footer-cont">
        <p>Copyright 2019-2021 <a href="//www.upetmart.com">www.upetmart.com</a> <a href="https://beian.miit.gov.cn">粤ICP备17078631号</a></p>
        <p>粤公网安备 31010502000392号</p>
    </div>
</footer>

</body>
<script>
    $(function () {
        // 通过正则获取请求id
        var reg = new RegExp("(^|&)id=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        id = r != null ? unescape(r[2]) : 0;

        $.get('/e/news', {id: id}, function (res) {
            if (res.data){
                $('h1.news_detail-main-title').text(res.data.title)
                $('span.span_publish_time').text(res.data.newstime)
                $('.news_detail-content').html(res.data.newstext)
                $('.glyphicon-eye-open').text(res.data.onclick+'次')
                $('meta[name="keywords"]').attr('content',res.data.keyboard);
                $('meta[name="description"]').attr('content',res.data.smalltext );
                document.title = res.data.title + "-加入我们-阿闻宠物"
            }
        }, 'json')
        $('meta[name="description"]').after('<link rel="canonical" href="/news_detail.html?id='+id+'">')
    })
</script>
</html>