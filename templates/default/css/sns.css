@charset "utf-8";
/* ================================ */
/* 会员SNS个人主页（公共）样式定义 		*/
/* Author:			<PERSON><PERSON><PERSON><PERSON>		*/
/* Copyright:		www.shopnc.net	*/
/* Create Date:		Dec-01-2012		*/
/* Retrofit Date:	Jan-10-2013		*/
/* ================================ */
a { color: #0A8CD2;}
a:hover { text-decoration: underline; color: #0A8CD2;}
.nc-appbar-tabs a.compare { display: none !important;}
.wrapper { width: 1000px; margin : 0 auto;}
.hint { color: #BBB; line-height: 20px; }
/* ====================
 * 表单元素格式化及伪类效果
 * ==================== */
input[type="text"] { font-family: Tahoma; height: 16px; line-height: 16px; background-color:#F9F9F9; padding: 1px 2px 3px 4px; border: solid 1px; border-color: #CCC #DDD #DDD #CCC; box-shadow: 2px 2px 1px 0 #E7E7E7 inset; -moz-box-shadow: 2px 2px 1px 0 #E7E7E7 inset/* if FF*/; -webkit--box-shadow: 2px 2px 1px 0 #E7E7E7 inset/* if Webkie*/;}
input[type="text"]:hover { background-color:#FFF;}
input[type="text"]:focus { background-color:#FFF; border-color: #CCC; box-shadow: 1px 1px 1px 0 #E7E7E7; -moz-box-shadow: 1px 1px 1px 0 #E7E7E7/* if FF*/; -webkit--box-shadow: 1px 1px 1px 0 #E7E7E7/* if Webkie*/;}

textarea { font-family: Tahoma; line-height: 16px; color: #999; background-color:#FFF; padding: 5px; border: solid 1px; border-color: #CCC #DDD #DDD #CCC; box-shadow: 2px 2px 1px 0 #E7E7E7 inset; -moz-box-shadow: 2px 2px 1px 0 #E7E7E7 inset/* if FF*/; -webkit--box-shadow: 2px 2px 1px 0 #E7E7E7 inset/* if Webkie*/;}
textarea:hover { background-color:#FFF;}
textarea:focus { color: #555; background-color:#FFF; border-color: #CCC; box-shadow: 1px 1px 1px 0 #E7E7E7; -moz-box-shadow: 1px 1px 1px 0 #E7E7E7/* if FF*/; -webkit--box-shadow: 1px 1px 1px 0 #E7E7E7/* if Webkie*/;}

input.submit, a.submit, input[type="submit"] { font-weight: 700; color: #555; background: url(../images/sns/btn.png) no-repeat scroll 0 0; display:inline-block; width: 120px; height: 34px; border: 0; border-radius: 4px; cursor: pointer; box-shadow: 1px 1px 0 rgba(0,0,0,0.1);}
input[type="submit"]:hover { color: #000; background-position: 0 -150px; box-shadow: none;}

/* ===============
 * 通用弹出式窗口样式
 * =============== */
.eject_con { background-color: #FFF; overflow: hidden;}
.eject_con dl { line-height: 20px; display: block; width: 100%; clear: both; padding:12px 0 0 0; overflow:hidden;}
.eject_con dl dt { color: #555; text-align: right; text-overflow: ellipsis; white-space: nowrap; width: 29%; float: left; }
.eject_con dl dd { float: right; width: 70%;}
.eject_con h2 { line-height:20px; font-weight: 600; background-color:#FEFEDA; color: #630; text-align: left; width: 90%; padding:8px 16px; margin: 5px auto 5px auto; border: solid 1px #FFE8C2;}
.eject_con span.num { font-weight: 600; color: #390;}
.eject_con ul { overflow: hidden;}
.eject_con li h2 { font-size: 16px; font-weight: 600; line-height: 32px; color: #555; width: 98%; text-align: left; margin: 0 auto; border-bottom: dashed 1px #E7E7E7;}
.eject_con .checked { float: left; padding: 0; margin: 0;}
.eject_con .checked li { line-height: 16px; height: 16px; padding: 4px 0;}
.eject_con li p { float: left; }
.eject_con .strong { padding-left: 10px; color: #ff4e00; }
.eject_con dl.bottom { padding: 12px 0; background-color:#F9F9F9; border-top: 1px solid #EAEAEA; margin-top:12px; }

/*信息提示*/
.form-error .error { background: #FFC; color: #FFF; text-align:center; display:block; width: 99%; padding: 5px 0; margin: 10px auto; border: dashed 1px #F60;}
.warning { color: orange; margin:0; padding:0; border:none; background:none; width:auto; clear:none;}
.exceeded { color: red;}

/* 图片滚动 
-------------------------------------------*/
.jcarousel-list-horizontal { font-size: 0; *word-spacing:-1px/*IE6、7*/; }
.jcarousel-container-horizontal { padding: 0 18px;}
.jcarousel-clip { overflow: hidden;}
.jcarousel-clip-horizontal { z-index: 1;}
.jcarousel-item {}
.jcarousel-item-horizontal { font-size: 12px; vertical-align: top; display: inline-block; *display: inline/*IE7*/; *zoom: 1/*IE7*/;}
.jcarousel-direction-rtl .jcarousel-item-horizontal { margin-right: 0;}
.jcarousel-item-placeholder { background: #fff; color: #000;}
.jcarousel-prev-horizontal,
.jcarousel-next-horizontal { background: transparent url(../images/member/member_pics.png) no-repeat; width: 9px; height: 16px; padding: 10px 13px; margin-top: -18px; position: absolute; z-index: 9; top: 50%; cursor: pointer;}
.jcarousel-prev-horizontal { background-position: -240px -40px; left: 0; }
.jcarousel-prev-horizontal:hover, 
.jcarousel-prev-horizontal:focus,
.jcarousel-prev-horizontal:active { background-position: -276px -40px;}
.jcarousel-prev-disabled-horizontal:hover,
.jcarousel-prev-disabled-horizontal:focus,
.jcarousel-prev-disabled-horizontal:active { background-position: -240px -40px; cursor: default;}
.jcarousel-next-horizontal { background-position: -240px -76px; right: 0; }
.jcarousel-next-horizontal:hover,
.jcarousel-next-horizontal:focus,
.jcarousel-next-horizontal:active { background-position: -276px -76px;}
.jcarousel-next-disabled-horizontal,
.jcarousel-next-disabled-horizontal:hover,
.jcarousel-next-disabled-horizontal:focus,
.jcarousel-next-disabled-horizontal:active { background-position: -240px -76px; cursor: default;}


.sns-skin { width: 458px; height: 180px; overflow: hidden; margin: 20px auto; zoom:1;}
.sns-skin ul { width: 468px; height: 190px; margin-left: -10px;}
.sns-skin ul li { display: block; width: 140px; height: 79px; float: left; padding:0; border: solid 3px #E7E7E7; margin: 0 0 10px 10px; _margin: 0 0 10px 5px/*IE6*/;}
.sns-skin ul li:hover, .sns-skin ul li.selected { border-color: #F90 !important;}
.sns-skin-button { background-color: #F7F7F7; display: block; clear: both; border-top: solid 1px #E7E7E7;}
.sns-skin-button a { font-size: 14px; line-height: 20px; border-style: solid; border-color: #ccc; border-width: 1px; padding: 2px 20px; font-weight: 600; margin: 10px 2px; _margin: 10px 1px/*IE6*/; display:inline-block; color: #555; cursor: pointer;}
.sns-skin-button a { *display:inline/*IE7*/;}
.sns-skin-button a:hover { text-decoration:none;}
.sns-skin-button a.save { background-color: #0066CC; border-color: #235994; color: #FFF; margin-left: 170px;}

/* =======================
 * SNS个人主页框架头部菜单样式
 * ======================= */
#header{ line-height: 20px; width: 100%; height: 50px; background: url(../images/sns/header_bg.png) repeat-x scroll center top;}
#header h1 { float: left; margin: 7px 0 3px 0;}
#header h1 img { max-width: 200px; max-height: 40px;}
#header h2 { font: 600 16px/40px arial,"microsoft yahei"; color: #555; height: 40px; float: left; margin: 10px 0 0 10px;}
#header .menu { margin-top: 18px; float:right; }
#header .menu li {  line-height: 14px; height: 14px; float:left; padding: 0 15px; border-left: solid 1px #E7E7E7; position: relative; z-index: 99;}
#header .menu li a { color: #555;}
#header .menu li a:hover { color: #60B3DD;}
#header .menu li a i { font-size: 0; line-height: 0; vertical-align: middle; display:inline-block; width: 0; height: 0; margin: 0 0 0 4px; border-color: #333333 transparent transparent; border-style: solid dashed dashed; border-width: 4px;}
#header .menu li .friend-menu { filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#E5FFFFFF', endColorstr='#E5FFFFFF');background:rgba(255,255,255,0.9); display: none; border: solid 1px #D8D8D8; position: absolute; z-index: 1; top: 20px; left: 0;  box-shadow: 2px 2px 0 rgba(0,0,0,0.1);}
:root #header .menu li .friend-menu {filter:none;}/*for IE9*/
#header .menu li .friend-menu dl { max-height: 125px; overflow: hidden;}
#header .menu li .friend-menu dl dd a { line-height: 24px; text-overflow: ellipsis; white-space: nowrap; display: block; min-width: 72px; max-width: 100px; padding: 3px 5px; overflow: hidden;}
#header .menu li .friend-menu dl dd a:hover { color: #FFF; text-decoration: none; background-color: #60B3DD;}
#header .menu li .friend-menu dl dd a img { vertical-align: middle; display: inline-block; max-width: 24px; max-height: 24px; margin-right: 5px; border-radius: 15px;}
#header .menu li .friend-menu p { line-height: 28px; padding: 0 5px;}

#header .search { display:inline; height:26px; float: right; margin: 12px 0 auto 10px; }
#header .search form { display:inline; height:26px; }
#header .search form .ncs-search-input-text { line-height: 18px; background-color: #FFF; float:left; width: 220px; *width: 210px;/* IE6~7 */ height: 18px; padding: 2px; border-right-width: 0; *border-right-width: 1px;/* IE6~7 */ }
#header .search form a { float:left; padding:1px; *margin-left:5px !important/* IE7 */; _margin-left:2px/* IE6 */; border-style: solid; border-width: 1px;}
#header .search form a.ncs-search-btn-mall { border-color: #F48000; border-width: 1px; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFCA00', endColorstr='#FFB901'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FFCA00), to(#FFB901)) ; background-image: -moz-linear-gradient(top, #FFCA00, #FFB901);}
#header .search form a span {  font-weight: 600; float:left; line-height: 20px; height: 20px; padding: 0 8px; *padding: 0 5px;/* IE6~7 */}
#header .search form a.ncs-search-btn-mall span { color: #FFF; _background-color:#F48000/*IF IE6*/; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFCC00', endColorstr='#FF9003'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FFCC00), to(#FF9003)) ; background-image: -moz-linear-gradient(top, #FFCC00, #FF9003); text-shadow: 1px 1px 1px #EA7B00;}
#header .search form a:hover.ncs-search-btn-mall span { filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FF9003', endColorstr='#FFCC00'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FF9003), to(#FFCC00)) ; background-image: -moz-linear-gradient(top, #FF9003, #FFCC00);text-shadow: -1px -1px 1px #EA7B00;}




/* ============================
 * SNS用户中心外层框架样式
 * ============================ */
/* 布局 */
.sns-main { width: 1000px; min-height: 480px; margin: 0; background: #FFF url(../images/sns/layout_bg.png) repeat-y scroll center top; overflow: hidden;}
.sns-main-all { display: block; width: 970px; min-height: 450px; background-color: #FFF; padding: 15px; overflow: hidden;}
.sns-main .sidebar { display: block; width: 230px; padding: 15px; float:right; overflow:hidden;}
.sns-main .left-content { display: block; width: 710px; float: left; padding: 15px; }
 /* 用户信息 */ 
.user-info { width: 1000px; height: 100px; position: relative; z-index: 9;}
.user-info .user-face { background-color: #FFF; width: 120px; height: 120px; padding: 4px; position: absolute; z-index: 1; top: 0; left: 10px; box-shadow: 0 1px 2px rgba(0,0,0,0.25);}
.user-info .user-face img { max-width: 120px; max-height: 120px;}
.user-info .user-face .hover-layout { position: relative; z-index: 1; width: 120px; height: 120px;}
.user-info .user-face .hover-layout a {  color: #FFF; line-height: 24px; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#CC000000', endColorstr='#CC000000');background:rgba(0,0,0,0.8); text-align: center; display: none; width: 120px; height: 24px; position: absolute ; z-index:1; bottom: 0px; left: 0px; }
:root .user-info .user-face .hover-layout a { filter:none;}/*for IE9*/
:root .user-info .user-face .hover-layout a:hover { text-decoration: none;}
.user-info .user-face .hover-layout:hover a { display: block;}
.user-info .user-data { color: #FFF; width: 600px; position: absolute; z-index: 2; top: 15px; left: 150px; text-shadow: 1px 1px 1px rgba(0,0,0,0.25);}
.user-info .user-data dt { height: 30px; padding-bottom: 10px; display: block; clear: both;}
.user-info .user-data dt h2 { font-size: 2.4em; line-height: 30px; display: inline-block;}
.user-info .user-data dt h2 { *display:inline/*IE7*/;}
.user-info .user-data dt h4 { line-height: 20px; font-weight: 600; display: inline-block; margin: 0 0 0 8px; }
.user-info .user-data dt h4 { *display:inline/*IE7*/;}
.user-info .user-data dt h4 span { font-weight: normal; margin: 0 0 0 4px; vertical-align: middle; opacity: 0.75;}
.add-friend a { color: #555; line-height: 16px; _background-color: #F7F7F7/*IE6*/; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FEFEFE', endColorstr='#E7E7E7'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FEFEFE), to(#E7E7E7)) ; background-image: -moz-linear-gradient(top, #FEFEFE, #E7E7E7); padding: 0 8px; border-radius: 1px; border: solid 1px #777;}
.add-friend a:hover { color: #FFF; text-decoration: none; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FF9003', endColorstr='#FFCC00'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FF9003), to(#FFCC00)) ; background-image: -moz-linear-gradient(top, #FF9003, #FFCC00); border-color: #F60;}
.add-friend a.selected { color: #FFF;; _background-color:#F48000/*IF IE6*/; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFCC00', endColorstr='#FF9003'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FFCC00), to(#FF9003)) ; background-image: -moz-linear-gradient(top, #FFCC00, #FF9003); border-color: #F60; cursor: default;}

.user-info .user-data dd { line-height: 16px; display: block; height: 16px; clear: both; padding: 4px;}
.user-info .user-data dd span { font-weight: 600; margin-right: 8px; float: left; }
.user-info .user-data dd span.male { font-size: 0; line-height: 0; background: url(../images/member-card/sex.png) no-repeat scroll 0 0; vertical-align: middle; text-indent: 999%; width: 16px; height: 16px; overflow: hidden;}
.user-info .user-data dd span.female { font-size: 0; line-height: 0; vertical-align: middle; text-indent: 999%; background: url(../images/member-card/sex.png) no-repeat scroll -16px 0px; width: 16px; height: 16px; overflow: hidden; }
.user-info .user-data dd span.tag { text-overflow: ellipsis; vertical-align: middle; white-space: nowrap; max-width: 360px; height: 16px; overflow: hidden; }
.user-info .user-data dd span.tag em { padding-right: 8px; margin: 0 4px; border-right: dotted 1px #CCC;}
.user-info .user-data dd span a { color:#FF9; text-decoration: underline;}

/* Copyright: 天津市网城天创科技有限责任公司	*/
/* 空间统计 */ 
.user-info .user-stat { padding: 6px 0; position: absolute; z-index: 3; top: 12px; right:0;}
.user-info .user-stat dl { color: #FFF; text-align: center; display: inline-block; padding: 0 15px; border-left: solid 1px #68ABD6; text-shadow: 1px 1px 1px rgba(0,0,0,0.25);}
.user-info .user-stat dl { *display:inline/*IE7*/;}
.user-info .user-stat dl dd { font-size: 2.2em; line-height: 24px;}
.user-info .user-stat dl dt { font-size: 1.4em; line-height: 24px; margin-top: 6px;}
/* 导航菜单 */
.sns-nav { width: 1000px; height: 40px; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#A5BEE1F5', endColorstr='#A5BEE1F5'); background:rgba(190,225,245,0.65); box-shadow: inset 0 1px 0 rgba(255,255,255,0.5); position: relative; z-index: 1;}
.sns-nav ul { height: 40px; margin-left: 150px; overflow:hidden; }
.sns-nav ul li { float: left; margin-left:-2px;}
.sns-nav ul li a { font: normal 14px/40px arial,"microsoft yahei"; color: #333; display: inline-block; padding: 0 20px 0 22px; border-left: solid 1px #60B3DD;  position: relative; z-index:1; box-shadow: inset 1px 0 0 rgba(255,255,255,0.5);}
.sns-nav ul li a { _display: inline;}
.sns-nav ul li a:hover { color: #FFF; background-color: #60B3DD; text-decoration: none; box-shadow: inset 1px 1px 0 rgba(255,255,255,0.5);}
.sns-nav ul li a.current, .sns-nav ul li a:hover.current { font-weight: 600; color: #60B3DD; background-color: #FFF; _height:28px/*IE6*/; _padding-top: 12px/*IE6*/; border-left: 0; z-index:9; box-shadow: none;}
.sns-nav ul li a.current i { background: url(../images/sns/nav.png) no-repeat scroll; vertical-align: middle; display: inline-block; margin-right: 6px;}

.sns-nav ul li a.current i.home { background-position: 0px -1px; width: 16px; height: 16px;}
.sns-nav ul li a.current i.goods { background-position: -16px 0; width: 18px; height: 18px;}
.sns-nav ul li a.current i.album { background-position: -34px -2px; width: 20px; height: 15px;}
.sns-nav ul li a.current i.store { background-position: -54px -1px; width: 18px; height: 18px;}
.sns-nav ul li a.current i.news { background-position: -72px -1px; width: 19px; height: 16px;}
.sns-nav ul li a.current i.circle { background-position: -91px -1px; width: 21px; height: 16px;}
.sns-nav div.skin { overflow: hidden; position: absolute; z-index: 1; top: 0px; right: 0px;}
.sns-nav div.skin a { font-size: 0; line-height: 0; text-indent: 999%; background: url(../images/sns/pics.png) no-repeat -38px -79px; display: block; width: 21px; height: 21px; overflow: hidden; cursor: pointer;}
.sns-nav div.skin a:hover {background-position: -61px -79px;}

#container {  line-height: 20px;}
#footer { border:none !important; }
#page { font-size: 12px;}

/* ============================
 * SNS用户中心内部页面样式
 * ============================ */
/* 内容部分标题及标题TAB选项卡切换样式 */ 
.tabmenu { display: block; width: 100%; height: 42px; margin-top: -5px; border-bottom: solid 3px #E7E7E7; position: relative; z-index: 1; }
.tabmenu ul { display: block; width: 400px; height: 40px; position: absolute; z-index: 9; top: 2px; left: 0; }
.tabmenu ul li { font: normal 12px/24px arial,"microsoft yahei"; display: inline-block;  height: 24px; padding: 10px 10px 6px 10px; margin-right: 10px; position: relative; z-index: 9; }
.tabmenu ul li { *display:inline/*IE7*/;}
.tabmenu ul li.active { border-bottom: solid 3px #60B3DD;}
.tabmenu ul li.active i { font-size: 0; line-height: 0; width: 0; height: 0; margin-left: -3px; border-color: #60B3DD transparent transparent; border-style: solid dashed dashed; border-width: 6px; position: absolute; left: 50%; bottom: -15px; z-index: 99;}
.tabmenu ul li a { font-size: 1.1em;  color: #777;}
.tabmenu ul li.active a {  font-weight: 600; color: #555;}
.tabmenu ul li a:hover { text-decoration: none; color: #60B3DD; }
.tabmenu span.more { position: absolute; z-index: 1; top: 15px; right: 10px;}
.tabmenu span.more a { line-height: 24px;}
/* 内容部分标题处发布新内容样式 */
.tabmenu .release-banner { display: block; float: right; position: relative; z-index: 1; width:580px;}
.tabmenu .release-banner i { }
.tabmenu .release-banner h3 { font-size: 1.5em; line-height: 32px; color: #777; display: inline-block; margin-right: 10px;}
.tabmenu .release-banner h3 { *display:inline/*IE7*/; _float: right;}
.tabmenu .release-banner h3 strong { font-weight: normal; color: #F60;}
.tabmenu .release-banner span.btn { background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0px 0px; display: inline-block; height: 24px; float:right; padding: 3px; border: solid 1px #D8D8D8; border-radius: 4px; cursor: pointer;}
.tabmenu .release-banner span.btn { *display: inline;}
.tabmenu .release-banner span.btn a { font-size: 14px; font-weight: 600; line-height: 24px; color: #555; text-decoration: none; background-color: #FFF; display: inline-block; height: 24px; float: left; padding: 0 12px;}
.tabmenu .release-banner span.btn:hover { background: #FF7F00 none; border-color: #FF4000;}
.tabmenu .release-banner span.btn:hover a, .tabmenu .release-banner span.btn:hover a:hover { color: #FFF ; background-color: #FF7F00;}

/* SNS首页部分样式 */
.sns-home-share { margin:20px 0 ; overflow: hidden;} 
.sns-home-share li { display: inline-block; width: 222px; max-height: 282px; float:left; padding-bottom: 10px; border: solid 1px #E7E7E7; margin:0 10px 0 0;}
.sns-home-share li { *display:inline/*IE7*/;}
.sns-home-share li a.pic { background: no-repeat scroll center center; display: block; width: 222px; height: 222px;}
.sns-home-share li p { color: #999; line-height: 20px; width: 210px; height: 20px; margin: 10px; _margin: 10px 5px/*IE6*/; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.sns-home-share li div.ops { overflow: hidden; margin: 0 10px;} 
.sns-home-album { margin:20px 0 ; clear:both;} 
.sns-home-album li { display: inline-block; width: 210px; height: 240px; padding: 6px; border: solid 1px #E7E7E7; margin:0 10px 0 0;}
.sns-home-album li { *display:inline/*IE7*/;}
.sns-home-album li a { background: no-repeat scroll center center; display: block; width: 210px; height: 210px;}
.sns-home-album li p { color: #999; line-height: 20px; height: 20px; margin: 10px 0 0 0;}
.sns-home-store { margin: 0px;}
.sns-home-store .shop-item { padding-bottom: 24px; margin: 16px 0px 10px 0px;}
.sns-home-store dl { border-bottom: solid 1px #E7E7E7;}
.sns-home-store dl dt { height: 32px; padding-left: 16px;}
.sns-home-store dl dt img { vertical-align: middle; display: inline-block; max-width: 30px; max-height: 30px; float: left; margin-right: 10px; border-radius: 15px; }
.sns-home-store dl dt h3 { color: #999; line-height: 32px; float: left;}
.sns-home-store dl dt h3 a {  font-weight: 600; margin-right: 8px;}
.sns-home-store dl dd { clear: both; padding-left: 20px; margin: 10px 0 20px 0; overflow: hidden;}
.sns-home-store dl dd i { background: url(../images/sns/pics.png) no-repeat scroll -25px -32px; display: inline-block; width: 25px; height: 18px; float: left;}
.sns-home-store dl dd p { font-size: 1em; line-height: 18px; color: #333; display: inline-block; width: 600px; float: left; margin-left: 6px;}
.sns-home-store dl dd p i { background-position: 0px -32px; vertical-align: middle; float: none; margin-left: 6px;}
.sns-home-store .shop-content { background-color: #F7F7F7; padding: 15px; border-bottom: #EBEBEB solid 1px; position: relative; z-index: 1; zoom:1;}
.sns-home-store .shop-content .arrow { line-height: 0; background: url(../images/sns/pics.png) no-repeat scroll -65px -32px; width: 17px; height: 10px; position: absolute; z-index: 1; top: -1px; left: 100px;}

.sidebar .title { line-height: 24px; overflow:hidden}
.sidebar .title h4 { font-size: 1em; font-weight: 600; color: #333; float:left; margin-left: 5px;}
.sidebar .title span { float: right;}
.visitors { overflow:hidden; }
.visitors h4 span { margin: 0 5px;}
.visitors h4 span.active { color:#555; font-weight: bold;}
.visitors h4 span.normal { color:#7FB8D2; font-weight:normal; cursor: pointer;}
.visitors h4 span.normal:hover { text-decoration: underline;}
.visitors h4 span.line { color: #999; font-size:10px; font-weight:100;}

.visitors ul {}
.visitors ul li { width: 60px; float:left; margin:8px;}
.visitor-pic { width:60px; height:60px; background-color:#FFF;}
.visitor-pic img { max-width: 60px; max-height: 60px;}
.visitor-name { line-height: 22px; text-overflow: ellipsis; white-space: nowrap; width: 60px; height: 22px; display:block; overflow: hidden;}
.visitor-time { line-height: 18px; color: #999;}

.side-goodslist { margin-top: 10px;}
.side-goodslist ul { width: 228px; height: 228px;}
.side-goodslist ul li { display: block; float: left; width: 75px; height: 75px; margin: 0 0 1px 1px; position: relative; z-index: 1; overflow:hidden;}
.side-goodslist ul li .goods-pic { background: #FFF;}
.side-goodslist ul li .goods-pic img { max-width: 75px; max-height: 75px;}
.side-goodslist ul li:hover .goods-pic img { opacity: 0.75;}
.side-goodslist ul li em.price { font-family:"microsoft yahei", Verdana ; font-size: 12px; font-weight: normal; color: #FFF; line-height: 16px; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#BF333333', endColorstr='#BF333333'); background:rgba(51,51,51,0.75); padding: 0 4px; position: absolute; z-index: 1; bottom: 1px; right: 1px; visibility: hidden;}
.side-goodslist ul li:hover em.price { visibility: visible;}
.side-message { margin-top: 20px;}
.guest-form, .message-list { width: 200px; padding: 5px 10px 0 10px;}
.guest-form .msg-content { line-height: 16px; width: 190px; height: 32px; padding: 5px;}
.side-message .action { height: 24px; margin: 5px 0;}
.side-message .action a.face { line-height: 24px; color: #555; float: left;}
.side-message .action span.charcount { line-height: 24px; color: #555; float: right; margin-right: 5px;}
.side-message .action a.btn { color: #555; line-height: 16px; _background-color: #F7F7F7/*IE6*/; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FEFEFE', endColorstr='#E7E7E7'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FEFEFE), to(#E7E7E7)) ; background-image: -moz-linear-gradient(top, #FEFEFE, #E7E7E7); float: right; padding: 3px 9px; border: solid 1px #D8D8D8; border-radius: 2px;}
.guest-form .action a:hover.btn { color: #FFF; _background-color: #FF7F00; text-decoration: none; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FF5300', endColorstr='#FF7F00'); background-image: -webkit-gradient( linear, left top, left bottom, from(#FF5300), to(#FF7F00)) ; background-image: -moz-linear-gradient(top, #FF5300, #FF7F00); border-color: #FF4300;}
.message-list dl { line-height: 20px; clear: both; margin-bottom: 10px; border-top: solid 1px #DDD; overflow:hidden;}
.message-list dl dt { color: #555; padding: 10px 0 0 0; border-top: solid 1px #F7F7F7;}
.message-list dl dt img { vertical-align: middle;}
.message-list dl dt a { margin-right: 5px;}
.message-list dl dd { overflow: hidden;}
.message-list dl dd span.time { color: #999; float: left;}
.message-list dl dd span.handle { color: #DDD; float: right; visibility: hidden;}
.message-list dl:hover .handle { visibility: visible;}
.message-list dl dd span.handle a { margin: 0 5px;}
.re-msg { filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#19000000', endColorstr='#19000000');background:rgba(0,0,0,0.1); width: 180px; padding: 10px; margin-top: 5px; position: relative; z-index: 1; overflow: visible !important; }
:root .re-msg { filter:none;}/*for IE9*/
.re-msg i { background: url(../images/sns/pics.png) no-repeat -70px -120px; display: block; width: 9px; height: 5px; position: absolute; z-index: 1; top: -5px; right: 55px;}
.re-msg-content { line-height: 16px; color: #777; width: 170px; height: 32px; padding: 5px;}
.re-content {filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#7FFFFFFF', endColorstr='#7FFFFFFF');background:rgba(255,255,255,0.5); width: 180px; padding: 5px 10px; margin: 5px 0 0 0 !important; border-top: 0!important;}
.re-content dt { padding:0!important; border-top: 0!important;}


.privacy-module { line-height:22px; font-size:12px; display:block; height:22px;  position: relative; z-index:99; cursor: pointer;}
.privacy-module:hover { }
.privacy-module .privacybtn { background-color:#FFF; color: #7FB8D2;  height: 22px; padding-left: 8px; margin: 1px; position: absolute; z-index: 99; top: 0; right:0;}
.privacy-module:hover .privacybtn { margin:0; border: solid 1px #80b8D2; border-bottom-color:#FFF;}
.privacybtn i { font-size: 0; line-height: 0; display: block; width: 0; height: 0; border-width: 4px; border-color: #80B8D2 transparent transparent transparent; border-style: solid dashed dashed dashed ; overflow: hidden; float:right; margin: 8px 4px; _margin: 8px 2px;}
.privacytab { background-color: #FFF; width: 102px; position: absolute; z-index: 1; top: 23px; right: 0px;}
.privacytab .menu-bd { background: none repeat scroll 0 0 white; text-align: left; width: 100px; max-height: 120px; border: 1px solid #80B8D2; overflow: hidden;}
.privacytab .menu-bd li { line-height:20px; color: #666666; background: none repea0t scroll 0 0 white; height:20px; padding: 6px 25px 4px 5px; margin: 0px;}
.privacytab .menu-bd li .selected{ background: url(../images/right.gif) no-repeat scroll 0 0 transparent; }
.privacytab .menu-bd li:hover { background-color:#f0f0f0}
.privacytab .menu-bd li span { vertical-align: top; text-overflow: ellipsis; white-space: nowrap; display: inline-block; max-width: 80px; width: 80px; padding-left:20px; overflow: hidden; cursor: pointer;}
.privacytab .menu-bd li span a { font-size: 12px !important; color: #666666; text-decoration: none; font-weight: normal !important;}

/* Copyright: shopnc */
/*记录为空样式*/
.sns-norecord { padding: 100px 0; margin-left: 320px; }
.left-content .sns-norecord { margin-left: 180px; }
.sns-norecord i { background: url(../images/sns/norecord_icons.png) no-repeat scroll; vertical-align: middle; width: 64px; height: 64px; float: left; margin-right: 10px;}
.sns-norecord i.pictures { background-position: 0 0;}
.sns-norecord i.store-ico { background-position: 0 -64px;}
.sns-norecord i.goods-ico { background-position : 0 -128px;}
.sns-norecord i.trace-ico { background-position : 0 -192px;}
.sns-norecord i.circle-ico { background-position : 0 -320px;}
.sns-norecord i.theme-ico { background-position : 0 -256px;}
.sns-norecord span { font: normal 14px/28px arial,"microsoft yahei"; color: #999; float: left;}
.sns-norecord span a, .sns-norecord span a:hover { color: #F60; text-decoration: none; cursor: default;}

/* 瀑布流商品列表页面 */
.sharelist-g .searchgoods {}
.nc-sns-pinterest { width: 980px;}
.nc-sns-pinterest li { width: 235px;  margin: 0 10px 20px 0; display: block; float: left; position: relative; z-index: 1; }
.nc-sns-pinterest li dl { width:233px; background-color: #FFF; border: solid 1px #E7E7E7; }
.nc-sns-pinterest li:hover dl { border-color: #D8D8D8; box-shadow: 4px 3px 4px rgba(102,102,102,0.1);}
.nc-sns-pinterest li dl dt.goodspic { width: 233px;}
.nc-sns-pinterest li dl dt.goodspic .thumb { text-align:left;}
.nc-sns-pinterest li dl dt.goodspic img { max-width: 233px !important;}
.nc-sns-pinterest li:hover dl dt.goodspic img { -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=80)"; /*ie8*/ filter:alpha(opacity=80); /*ie5-7*/ opacity: 1 ;}
.nc-sns-pinterest li dl dt .ap-pic { display: block; clear: both; border-top: solid 1px #D8D8D8; position: relative; z-index: 1px;}
.nc-sns-pinterest li dl dt .ap-pic .num { line-height: 20px; color: #FFF; background-color: #333; display: block; padding: 0 5px; border-radius: 2px; position: absolute; z-index: 1; top:4px; left:4px;  }
.nc-sns-pinterest li dl dt .ap-pic .num i { background: url(../images/sns/pics.png) no-repeat -70px -100px; vertical-align: middle; display: inline-block; width: 16px; height: 16px; margin-right: 4px;}
.nc-sns-pinterest li dl dd { line-height: 18px; padding: 4px 9px;}
.nc-sns-pinterest li dl dd.pinterest-cmt { color: #555; width: 215px; overflow: hidden; }
.nc-sns-pinterest li dl dd.pinterest-addtime { color: #999;}
.nc-sns-pinterest li dl dd.pinterest-ops { overflow:hidden; margin-bottom: 9px; }
.nc-sns-pinterest li ul.handle { display:none; height: 34px; position: absolute; z-index: 9; top: 6px; left: 8px;}
.nc-sns-pinterest li:hover ul.handle { display: block;}
.nc-sns-pinterest li ul.handle li { background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0px 0px; width: auto !important; display: inline-block; padding: 3px; margin: 0 3px 0 0; border: solid 1px #D8D8D8; border-radius: 4px;}
.nc-sns-pinterest li ul.handle li.set { position: relative; z-index:99; cursor: pointer; margin-left: 64px;}
.nc-sns-pinterest li ul.handle li.delete { margin-left: 58px;}
.nc-sns-pinterest li ul.handle li.set:hover { background: #FFF none; border-radius: 4px 4px 0 0 ;} 
.nc-sns-pinterest li ul.handle li a { line-height: 16px; color: #555; text-decoration: none; background-color: #FFF; display: inline-block; height: 16px; padding: 4px 6px;}
.nc-sns-pinterest li ul.handle li a i { background: url(../images/sns/pics.png) no-repeat scroll; vertical-align: middle; display: inline-block; height: 16px; margin-right: 8px; margin-top: -2px;}
.nc-sns-pinterest li ul.handle li.set a i { background-position: 0 0; width: 16px;}
.nc-sns-pinterest li ul.handle li.add-on a i { background-position: -20px 0; width: 16px;}
.nc-sns-pinterest li ul.handle li.buyer-show a i { background-position: -40px 0; width: 20px;}
.nc-sns-pinterest li ul.handle li.delete a i { background-position: -84px -80px; width: 14px;}
.nc-sns-pinterest li ul.handle li.cover a i { background-position: -20px -80px; width: 16px;}
.set-menu { background-color: #FFF; width: 66px; border-style: solid; border-color: #D8D8D8; border-width: 0 1px 1px 1px; position: absolute; z-index: 1; top: 30px; left:-1px; overflow: hidden; border-radius: 0 0 4px 4px; }
.set-menu li { line-height: 20px; color: #555; background: #FFF none !important; height: 20px; padding: 4px 0 4px 10px!important; margin: 0px!important; border: 0 !important; border-radius: 0!important;}
.set-menu li .selected { background: transparent url(../images/sns/pics.png) no-repeat scroll -84px -29px;}
.set-menu li:hover { background-color:#f7f7f7 !important;}
.set-menu li span { vertical-align: middle; background: url(../images/sns/pics.png) no-repeat scroll -84px 0px; display: inline-block; width: 34px; padding-left:22px; overflow: hidden; cursor: pointer;}
.set-menu li span a { font-size: 12px !important; color: #555; text-decoration: none; font-weight: normal !important; background-color: transparent !important; padding: 0 !important;}
.set-menu li span.del {	background: url(../images/sns/pics.png) no-repeat  -84px -80px;}

/* 我的空间喜欢模块 */
.ops-like { font-size: 0; border: solid 1px #FC8E5B; float: left; height: 18px; }
.ops-like a { font-size: 12px; background-color: #FC8E5B; color: #FFF; padding: 0 4px; vertical-align: middle; display: inline-block; *dispaly: inline; *zoom: 1; }
.ops-like a i { background: url(../images/sns/pics.png) no-repeat scroll -59px 0px; vertical-align: middle; display: inline-block; width: 12px; height: 12px; margin-right: 4px;}
.ops-like em { font-family: Tahoma, Geneva, sans-serif; font-size: 12px; line-height: 18px; color: #F60; background-color: #FFF; padding: 0 4px; font-weight: 600; vertical-align: middle; display: inline-block; *dispaly: inline; *zoom: 1;} 
.ops-like a.noaction i { background-position: -71px 0;}
.ops-like a.noaction { color: #FC8E5B; background-color:#FFF !important;}
.ops-comment { float: right; height: 20px;}
.ops-comment a {float:left; color: #FC8E5B;}
.ops-comment a i { line-height: 16px; background: url(../images/sns/pics.png) no-repeat scroll 0 -16px; vertical-align: middle; display: inline-block; width: 16px; height: 16px;}
.ops-comment a:hover i { background-position: -16px -16px;}
.ops-comment em { color: #999; padding-left:4px; }

/* 分享店铺列表页 */
.shoplist-module { margin: 0px;}
.shoplist-module .shop-item { padding-bottom: 24px; margin: 16px 0px 0px 0px; _margin-top:0/*IE6*/;}
.shoplist-module .shop-item:hover .set-btn { display: block;}
.shoplist-module dl { border-bottom: solid 1px #E7E7E7; padding: 0 0 20px 0; zoom:1;}
.shoplist-module dl dt { height: 32px; padding-left: 16px;}
.shoplist-module dl dt img { vertical-align: middle; display: inline-block; max-width: 30px; max-height: 30px; float: left; margin-right: 10px; border-radius: 15px; }
.shoplist-module dl dt h3 { color: #999; line-height: 32px; float: left;}
.shoplist-module dl dt h3 a {  font-weight: 600; margin-right: 8px;}
.shoplist-module dl dd { clear: both; padding-left: 20px; margin: 10px 0 0 0; overflow: hidden;}
.shoplist-module dl dd i { background: url(../images/sns/pics.png) no-repeat scroll -25px -32px; display: inline-block; width: 25px; height: 18px; float: left;}
.shoplist-module dl dd p { font-size: 1em; line-height: 18px; color: #333; display: inline-block; width: 880px; float: left; margin-left: 6px;}
.shoplist-module dl dd p i{ background-position: 0px -32px; vertical-align: middle; float: none; margin-left: 6px;}
.shoplist-module .shop-content { background-color: #F7F7F7; padding: 15px; margin:0; border-bottom: #EBEBEB solid 1px; position: relative; z-index: 1;}
.shoplist-module .shop-content .arrow { line-height:0; background: url(../images/sns/pics.png) no-repeat scroll -65px -32px; width: 17px; height: 10px; position: absolute; z-index: 1; top: -1px; left: 100px;}
.shoplist-module .info {}
.shoplist-module .info .title a { font-size: 1em; font-weight: 600; line-height: 20px; color: #06C; display: inline-block;}
.shoplist-module .info .title i.ico { background: url(../images/sns/pics.png) scroll no-repeat -32px -15px; display: inline-block; vertical-align: middle; width: 16px; height: 16px; margin-right: 6px;}
.shoplist-module .info .title span { display: inline-block; margin-left:8px; color: #999; }
.shoplist-module .info .title .privacy-module { display:inline-block; float:right; width:100px;}
.shoplist-module .detail { float:left; clear:both; }
.shoplist-module .detail li { display:block; background-color: #FFF; width:160px; height:160px; border: solid 1px #E7E7E7;}
.shoplist-module .operate{ color: #666; width:150px; float: right; margin-top: 30px; overflow: hidden;}
.shoplist-module .operate .status{ text-align: center; width: 150px; padding: 8px 0px;}
.shoplist-module .operate .status li {display:inline-block; width:65px; padding: 0px 4px; overflow:hidden;}
.shoplist-module .operate .status li { *display:inline;}
.shoplist-module .operate .status li .number{ font-family: arial; font-size: 24px; font-weight: 700; color: #FF5400; line-height:32px;}
.shoplist-module .operate .status li .kind { line-height:24px;}
.shoplist-module .operate .button { clear: both; margin-top: 20px; overflow:hidden;}
.shoplist-module .operate .button span { background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0 0; display: inline-block; height: 20px; padding: 2px; margin-left: 5px; _margin-left:4px; border: solid 1px #D8D8D8; border-radius: 4px; cursor: pointer;}
.shoplist-module .operate .button span { *display:inline/*IE7*/;}
.shoplist-module .operate .button span a { font-size: 1em; font-weight: 600; line-height: 20px; color: #555; text-decoration: none; background-color: #FFF; display: inline-block; height: 20px; padding: 0 6px;}
.shoplist-module .operate .button span:hover { background: #FF7F00 none; border-color: #FF4000;}
.shoplist-module .operate .button span:hover a, .shoplist-module .operate .button span:hover a:hover { color: #FFF ; background-color: #FF7F00;}

/* 商品详细页面 */
.goback { line-height: 20px; display: block;}
.goback a { color: #999;}
.snsgoods-info { padding: 10px 0px;}
.snsgoods-info .title { padding: 10px 0 30px 0; overflow: hidden; }
.snsgoods-info .title h3 { font-size: 1.4em; color: #555; line-height:24px; font-weight: 500; text-overflow: ellipsis; white-space: nowrap; width: 700px; height: 24px; overflow: hidden; }
.snsgoods-info .return-module { padding-bottom: 10px;  text-align: right;}
.snsgoods-info .gcontainer { overflow: hidden; }
.snsgoods-info .pic-module { vertical-align: top; text-align: left; min-width: 400px; min-height: 400px; float: left; margin-left: 20px; position: relative; z-index: 1;}
.snsgoods-info .pic-module .good-img { position: absolute; z-index: auto; top:0; left:0;}
.snsgoods-info .pic-module .good-img img { max-width: 400px; max-height: 400px;}
.snsgoods-info .pic-module .prev { display: block; width: 200px; height: 400px; position: absolute; left: 0; top: 0; z-index: 99; cursor: pointer;}
.snsgoods-info .pic-module .prev:hover { cursor: url("../images/sns/prev.cur"), pointer;}
.snsgoods-info .pic-module .next { display: block; width: 200px; height: 400px; position: absolute; right: 0; top: 0; z-index: 99; cursor: pointer;}
.snsgoods-info .pic-module .next:hover { cursor: url("../images/sns/next.cur"), pointer;}
.snsgoods-info .pic-module .whole{ width: 100%;}
.snsgoods-info .handle-module { width: 108px; min-height: 400px; float: left; margin-left: 80px}
.snsgoods-info .handle-module .operate {  font-weight: 600 !important; display: block; border: solid 1px #FC8E5B;}
.snsgoods-info .handle-module .operate span { line-height: 16px; color: #FFF; text-decoration: none; background-color: #FC8E5B; display: inline-block; width: 72px; height: 16px; padding: 6px 0;}
.snsgoods-info .handle-module .operate span i { background: url(../images/sns/pics.png) no-repeat scroll -59px 0px; vertical-align: middle; display: inline-block; width: 12px; height: 12px; margin: 0 8px;}
.snsgoods-info .handle-module .operate a { color: #FFF;}
.snsgoods-info .handle-module .operate em { font-family: Verdana, Geneva, sans-serif; color: #FC8E5B; text-align: center; display: inline-block; width: 24px; }
.snsgoods-info .handle-module .btn { background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0px 0px; display: block; padding: 3px; margin: 20px 0 0; clear: both; border: solid 1px #D8D8D8; border-radius: 4px;}
.snsgoods-info .handle-module .btn a { line-height: 16px; color: #555; text-decoration: none; background-color: #FFF; text-align: left; display: block; height: 16px; padding: 6px 0;}
.snsgoods-info .handle-module .btn:hover { background: #FF7F00 none; border-color: #FF4000;}
.snsgoods-info .handle-module .btn:hover a, .snsgoods-info .handle-module .btn:hover a:hover { color: #FFF ; background-color: #FF7F00;}
.snsgoods-info .handle-module .btn a i { background: url(../images/sns/pics.png) no-repeat scroll; vertical-align: middle; display: inline-block; height: 16px; margin: -2px 10px 0 20px; }
.snsgoods-info .handle-module .set a i { background-position: 0 0; width: 16px;}
.snsgoods-info .handle-module .add-on a i { background-position: -20px 0; width: 16px;}
.snsgoods-info .handle-module .buyer-show a i { background-position: -40px 0; width: 19px; margin-left: 16px;}
.snsgoods-info .handle-module .set:hover a i { background-position: 0 -55px; width: 16px;}
.snsgoods-info .handle-module .add-on:hover a i { background-position: -20px -55px; width: 16px;}
.snsgoods-info .handle-module .buyer-show:hover a i { background-position: -40px -55px; width: 19px; margin-left: 16px;}
.snsgoods-info .handle-module .set { position: relative; z-index: 9;}
.snsgoods-info .handle-module .set ul { background-color: #FFF; border-style: solid; border-color: #CCC; border-width: 0 1px 1px; position: absolute; z-index: 1; top: 35px; left: -1px; overflow: hidden;}
.snsgoods-info .handle-module .set li { height: 20px; padding: 4px 0; }
.snsgoods-info .handle-module .set li:hover { background-color:#E7E7E7;}
.snsgoods-info .handle-module .set li span { color: #555; line-height: 20px; background: url(../images/sns/pics.png) no-repeat scroll -84px 0px; display: block; width: 40px; padding-left: 26px; margin: 0 16px 0 24px; cursor: pointer;}
.snsgoods-info .handle-module .set li span.selected { background: transparent url(../images/sns/pics.png) no-repeat scroll -84px -29px;}
.snsgoods-info .handle-module .btn strong {  font-weight: 600; margin-left: 20px; }
.snsgoods-info .handle-module .price { font-size: 1.4em; font-weight: 600; line-height: 28px; color: #F60; text-align: center; display:block; margin-top: 20px; }
.snsgoods-info .share-content { width: 662px; margin: 20px auto 0 auto; overflow: hidden;}
.snsgoods-info .share-content i { background: url("../images/sns/pics.png") no-repeat scroll -25px -32px transparent; display: inline-block; float: left; width: 25px; height: 18px; }
.snsgoods-info .share-content p { font-size: 1em; color: #555; line-height: 18px; display: inline-block; float: left; width: 600px; margin-left: 6px;}
.snsgoods-info .share-content p i { background-position: 0 -32px; vertical-align: middle; float: none; margin-left: 6px;}
.snsgoods-info .interact-module { display: block; clear: both; padding: 10px; margin-top: 20px; border-top: solid 1px #D8D8D8;}
.snsgoods-info .interact-module .add-time { line-height: 20px; color: #777;}
.snsgoods-info .interact-module ul { display: inline-block; float: right; padding: 3px 0; overflow: hidden;}
.snsgoods-info .interact-module ul li { line-height: 14px; height: 14px; padding: 0 10px; display: inline-block; border-left: solid 1px #E7E7E7; margin-left:-1px;}
.snsgoods-info .comment-module { display: none; position: relative; z-index: 1;}
.snsgoods-info .comment-module .arrow { background: url(../images/sns/pics.png) no-repeat scroll -65px -42px; width: 19px; height: 7px; position: absolute; z-index: 1; top: -6px; right: 36px;}
.snsgoods-info .ap-pic-module { clear: both; margin: 20px auto; border-top: solid 4px #F7F7F7; }
.snsgoods-info .ap-pic-module .top { height: 48px; position: relative; z-index: 1; border-bottom: solid 1px #F7F7F7;}
.snsgoods-info .ap-pic-module .top h3 { font-size: 1.2em ; line-height: 24px; color: #777; padding: 12px 0 12px 80px;}
.snsgoods-info .ap-pic-module .top span { font-size: 1em; text-shadow: 1px 1px 0 rgba(0,0,0,0.1); line-height: 20px; color: #FFF; background: url(../images/sns/pics.png) no-repeat scroll 0px -100px; text-align: center; width: 66px; padding-top: 50px; padding-bottom: 10px; position: absolute; z-index: 1; left: 10px; top: -8px;}
.snsgoods-info .ap-pic-module .picture { text-align: center; padding: 10px; overflow: hidden;}
.snsgoods-info .ap-pic-module img { max-width: 680px;}


/* 表情模块 */
.smilies-module { background-color:#FFFFFF; display:none; border:1px solid #D5E5F5; height:94px; width:224px; position:absolute; z-index:999;  padding:6px;
}


/* Copyright: www.shopnc.net */
/* 动态列表 */
.fd-list { overflow: hidden; }
.fd-list li { vertical-align: top; _display:inline-block; min-height: 88px; margin: 15px; border-bottom: dotted 1px #D8D8D8; position: relative; z-index:1;}
.fd-aside { position: absolute; top: 0; left:0; z-index:1; }
.fd-wrap { word-wrap: break-word; margin-bottom: 20px; position: relative; z-index:auto}
.fd-wrap dt {  display: block; width: 100%; margin-bottom: 10px; overflow: hidden; }
.fd-wrap dt img { vertical-align: middle; display: inline-block; max-width: 30px; max-height: 30px; float: left; margin-right: 10px; border-radius: 15px; }
.fd-wrap dt h3 { color: #777; display: inline-block; float: left; width: 600px;}
.fd-wrap dt h3 a { font-weight: 600;}
.fd-wrap dt h3 img { float: none; vertical-align:middle; /*optional*/}
.fd-wrap dt .del-btn { display:none; }
.fd-wrap:hover .del-btn { display:block; }
.fd-wrap dt span p { border: solid 1px #80B8D2; width: 14px; height:12px; position: relative; z-index:1; cursor:pointer;}
.fd-wrap dt span p i { font-size: 0; line-height: 0; display: block; width: 0; height: 0; border-width: 4px; border-color: #80B8D2 transparent transparent transparent; border-style: solid dashed dashed dashed ; overflow: hidden; margin: 4px 3px; }
.fd-wrap dt span a { display: none}
.fd-wrap dt span:hover a { color:#80B8D2; background-color: #F4F7FB; white-space: nowrap; display: block;  padding: 2px 6px; margin:0; border: solid 1px #80B8D2; position: absolute; z-index: 1; top: 12px; right: -1px;}
.fd-wrap dd { clear:both; display:block; width: 100%; margin-top:10px; overflow:hidden;}
.fd-wrap .goods-time { color: #999;}

/* 动态列表中分享商品内容样式 */
.fd-media { background:#FBFBFB; clear:both; padding: 10px; border:1px solid #E7E7E7; overflow: hidden; _zoom: 1; }
.fd-media .goodsimg { line-height: 120px; background-color: #FFF; text-align: center; display: inline-block; width: 120px; height: 120px; padding: 4px; border: solid 1px #D8D8D8;}
.fd-media .goodsimg { *display:inline/*IE7*/; *float: left/*IE7*/;}
.fd-media .goodsimg img { max-width: 120px; max-height: 120px; vertical-align: middle;}
.fd-media .goodsinfo { vertical-align: top; display: inline-block; padding-left:10px; }
.fd-media .goodsinfo { *display:inline/*IE7*/; *float: left/*IE7*/;}
.fd-media .goodsinfo dl { border-bottom: 0px;clear: both; color: #777777; margin: 4px auto;overflow: hidden;padding: 0;}
.fd-media .goodsinfo dt { color: #404040; line-height:18px; text-align:left; text-overflow: ellipsis; white-space:nowrap; height: 20px; float: left; padding:0px; margin:0 0 10px 0; overflow: hidden;}
.fd-media .goodsinfo dd { color: #777; float: left; margin: 2px 0; text-align:left; }
.fd-media .goodsinfo i { font-family: MingLiU; font-size: 11px; line-height:14px; color:#FFF; letter-spacing: -1px; letter-spacing: 0\9\0/*IE9*/; display:inline-block; padding:1px 4px; padding:2px 4px 0 4px\9\0/*IE9*/; margin-right:10px; border:solid 1px; border-radius: 4px; box-shadow: inset 1px 1px 0 rgba(255,255,255,0.25); text-shadow: 1px 1px 0 rgba(0,0,0,0.25); -webkit-text-size-adjust:none;}
i.desc-type-new { background-color:#DE4B82; border-color:#9A2D56!important;} /*新品*/
i.desc-type-coupon { background-color:#F38419; border-color:#C35B14!important;} /*优惠券*/
i.desc-type-xianshi { background-color:#3BCFFD; border-color:#2FAAD4!important;} /*限时折扣*/
i.desc-type-mansong  { background-color:#4C6293; border-color:#364A6F!important;}/*满即送*/
i.desc-type-bundling { background-color:#44783A; border-color:#2C4B21!important;} /*组合销售*/
i.desc-type-groupbuy { background-color:#7C7C7E; border-color:#3F3D40!important;}/*团购*/
i.desc-type-recommend { background-color:#B22D00; border-color:#660000!important;} /*推荐*/
i.desc-type-hotsell { background-color:#8C008C; border-color:#300040!important;} /*热销*/
.fd-media .goodsinfo ul { padding:0;}
.fd-media .goodsinfo ul li { background-color: #FFF; line-height: 60px; text-align: center; display: inline-block; width: 60px; height: 60px; min-height: 60px; padding: 4px; margin: 0 15px 15px 0; border: solid 1px #D8D8D8;}
.fd-media .goodsinfo ul li { *display:inline/*IE7*/;}
.fd-media .goodsinfo ul li img { vertical-align: middle; max-width: 60px; max-height: 60px;}
.fd-media .goodsinfo p { font-size: 1em; line-height: 28px; color: #777;}
/* 动态列表中转帖中原帖内容模块 */
.fd-forward { background-color: #FAFAFA; border: solid 1px #E7E7E7; padding:10px; margin: 0 auto; overflow:hidden;}
.fd-forward .title{}
.fd-forward .title .uname{color: #336699; font-weight:bold;}
.fd-forward .stat{float:left; width:100%;padding-top:10px;color: #336699;}
.fd-forward .fd-media { background-color: #FFF; margin: 15px 0 0 0;}

/* 转播样式 */
/* 动态评论样式 */
.forward-widget, .comment-widget  { font-size:1em; background-color: #F7F7F7; padding: 10px; margin: 10px auto; border: solid 1px #E7E7E7; clear:both; overflow:hidden;}
.forward-widget .forward-edit { }
.forward-widget .forward-add { }
.forward-widget textarea, .comment-widget textarea { font-size: 12px; background-color: #FFF; word-wrap: break-word; width: 98%; height: 40px; border: solid 1px #D8D8D8; overflow: auto; resize: none;}
.forward-widget .forward-add .act, .comment-widget .comment-add .act { height:25px; padding-bottom: 5px; padding-top: 3px; clear:both;}
.forward-widget .skin-blue, .comment-widget .skin-blue { float: right;}
.forward-widget .skin-blue .btn, .comment-widget .skin-blue .btn { background: none repeat scroll 0 0 transparent; display: inline-block; width: 49px; height:24px; margin:0 0 0 5px; border-radius: 2px;}
.forward-widget span.btn a, .comment-widget span.btn a { font-size: 12px; font-weight: normal; line-height: 24px; color: #000; background: -moz-linear-gradient(center top , #FFFFFF, #E5E5E5) repeat scroll 0 0 transparent; display: block; height: 24px; padding: 0 11px; border: 1px solid #999; cursor: pointer;}
.forward-widget span.btn a:hover, .comment-widget span.btn a:hover { text-decoration: none;}
.comment-widget .comment-list { margin-top:10px;}
.forward-widget li, .comment-widget li { padding: 7px 7px 2px; margin-top: 5px; border-top: 1px dashed #D5D5D5; overflow: hidden; zoom:1;}
.forward-widget li .clogo, .comment-widget li .clogo { float: left; margin-right: 10px; border: 0 none;}
.forward-widget .forward-list .detail, .comment-widget .comment-list .detail { color: #404040; padding-left: 40px;}
.forward-widget .forward-list .name, .comment-widget .comment-list .name { margin-right: 5px;}
.forward-widget .opt, .comment-widget .opt { float: right;}
.forward-widget .opt a, .comment-widget .opt a { display: inline-block; vertical-align: middle;}
.forward-widget .more, .comment-widget .more { background-image: none; text-align: right; padding: 7px;}
.face {line-height: 20px; color: #06C; text-decoration: none; background: url(../images/smile.gif) no-repeat left center; padding-left: 20px; }



.lazymore { background-color: #F4F7FB; margin: 20px 0 0 0; text-align: center; border: solid 1px #C4D5E0; height: 32px; line-height: 32px; font-weight:700; }
#weibocharcount .counter { color:#999;}
#weibocharcount label.error { position: absolute; z-index: 1; bottom:20px; left:110px;}
#weibocharcount .warning { color: orange; margin:0; padding:0; border:none; background:none; width:auto; clear:none;}
#weibocharcount .exceeded { color: red;}


/* 版权：shopnc	*/
/* 翻页 */
.pagination{ border-top: solid 3px #E7E7E7; margin: 10px auto;}
.pagination ul{ margin: 0; padding: 16px 0 0 0; text-align: center;}
.pagination ul li { display: inline-block; list-style-type: none;}
.pagination ul li { *display: inline;}
.pagination li span { font-size: 14px; line-height: 32px; color: #777; background-color: #FFF; list-style-type: none; display: inline; height: 32px; float: left; padding: 0px 12px; margin: 0px 1px; border:solid 1px #F7F7F7; }
.pagination li a span , .pagination li a:visited span { color: #60B3DD; text-decoration: none; cursor:pointer;}
.pagination li a:hover span, .pagination li a:active span{ color: #DB7C1E; background-color: #AFD9EE; border-color: #60B3DD; cursor:pointer;}
.pagination li span.currentpage { color: #FFF; font-weight: 600;  background-color:#60B3DD; border-color:#60B3DD;}



.set-btn { position: relative; z-index:99; cursor: pointer; background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0px 0px; width: auto !important; display: none; padding: 3px; margin: 0 3px 0 0; border: solid 1px #D8D8D8; border-radius: 4px;}
.set-btn a { line-height: 16px; color: #555; text-decoration: none; background-color: #FFF; display: inline-block; height: 16px; padding: 4px 6px;}
.set-btn a i { background: url(../images/sns/pics.png) no-repeat scroll; background-position: 0 0; width: 20px; margin-top: -2px; vertical-align: middle; display: inline-block; height: 16px; margin-right: 4px;}
.set-btn:hover { background: #FFF none; border-width: 1px 1px 0 1px; border-radius: 4px 4px 0 0;}
.set-btn a.del i { background-position: -86px -80px;}

.del-btn { cursor: pointer; background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0px 0px; width: auto !important; padding: 2px; margin: 0 3px 0 0; border: solid 1px #D8D8D8; border-radius: 4px;}
.del-btn a { line-height: 16px; color: #555; text-decoration: none; background-color: #FFF; display: inline-block; height: 16px; padding: 3px;}
.del-btn a i { background: url(../images/sns/pics.png) no-repeat scroll -82px -80px; width: 16px; margin-top: -2px; vertical-align: middle; display: inline-block; height: 16px; }



/* ====================
 * SNS个人主页相册部分样式
 * ==================== */
.picture-index { margin: 20px 0 0 0;}
.album-info { width: 218px; height: 210px; text-align: center; display: inline-block; float: left; border: dashed 1px #E7E7E7; margin: 0 11px 0 11px; position: relative; z-index: 1;}
.album-info .stat { font-size: 1em; line-height: 24px; margin: 20px 0 0 0;}
.album-info .stat em { color: #06C; margin: 0 4px;}
.album-info .button { background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0px 0px; display: inline-block; height: 24px; padding: 3px; margin: 0 auto; border: solid 1px #D8D8D8; border-radius: 4px; cursor: pointer;}
.album-info .button { *display:inline/*IE7*/; }
.album-info .button a { font: bold 14px/24px arial,"microsoft yahei"; color: #555; text-decoration: none; background-color: #FFF; display: inline-block; height: 24px; padding: 0 12px;}
.album-info .button:hover { background: #FF7F00 none; border-color: #FF4000;}
.album-info .button:hover a, .album-info .button:hover a:hover { color: #FFF ; background-color: #FF7F00;}
.build-album { margin: 20px 0;}
.upload-con { background: #F7F7F7; width: 198px; max-height: 174px !important; border: solid 1px #E7E7E7; padding: 10px; margin: 10px 0 0 0; position: absolute; z-index: 1; top: 108px; left: -1px; overflow: hidden;}

.ncsc-upload-btn { display: inline-block; margin-right: 5px; width: 168px; height: 48px; position: relative; z-index: 1;}
.ncsc-upload-btn a { display: block; position: relative; z-index: 1;}
.ncsc-upload-btn span { width: 168px; height: 48px; position: absolute; left: 0; top: 0; z-index: 2; cursor: pointer;}
.ncsc-upload-btn .input-file { width: 168px; height: 48px; padding: 0; margin: 0; border: none 0; opacity:0; filter: alpha(opacity=0); cursor: pointer; }
.ncsc-upload-btn p { font: normal 16px/48px arial,"microsoft yahei"; color: #39C; background: #FFF url(../images/sns/picture_add.png) no-repeat 10px 10px!important; text-align: center; width: 148px !important; height: 48px !important; border: solid 1px #CCC; border-radius: 4px; padding: 0 0 0 20px!important; margin: 0 auto; float:none !important; box-shadow: 2px 2px 0 rgba(0,0,0,0.1); position: absolute; left: 0; top: 0; z-index: 1;}
.ncsc-upload-btn a:hover p { background-color: #E6E6E6; color: #333; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF;} 
.upload-pmgressbar {}
.upload-pmgressbar div { background-color: #F7F7F7; width: 146px; height: 24px; margin-top: 4px; padding: 4px 14px;}
.upload-pmgressbar div p { font: 10px/12px Arial; width: 146px; height: 12px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}
.upload-pmgressbar div p.loading { background:url(../images/member/upload_loading.gif) no-repeat 0 0; height:8px; border-radius: 4px;}
#uploader .arrow { background:url(../images/sns/pics.png) no-repeat scroll -65px -42px; display: block; width: 17px; height: 9px; margin-left: -9px; position: absolute; z-index: 9; top:110px; left: 50%;}
.upload-txt { font-size: 0.85em; line-height: 16px; padding-top: 8px; clear: both;}
.upload-wrap { line-height: 20px; margin-bottom: 10px; }

/* 版权：天津市网城天创科技有限责任公司	*/
/* 相册专辑列表页 */
.album-cover { display: inline-block; width: 220px; height: 280px; float: left; margin: 0 11px;}
	.album-cover .cover { background: url(../images/sns/album.png) no-repeat scroll 0px 0px; width: 190px; height: 190px; padding: 11px 19px 19px 11px;}
		.album-cover .cover img { max-width: 190px; max-height: 190px;}	
	.album-cover .title {  line-height: 20px; color: #555; display: block; width: 210px; height: 20px; margin: 5px 0 5px 10px;}
		.album-cover .title h3 { text-overflow: ellipsis; white-space: nowrap; display: inline-block; max-width: 170px; height: 20px; float: left; overflow: hidden; }
		.album-cover .title em { font-size: 0.9em; color: #999; margin-left: 4px; display: inline-block;  float: left;}
	.album-cover .handle { font-size: 1em; line-height: 20px; display: none; width: 210px; height: 20px; margin: 5px 0 5px 10px;}
		.album-cover:hover .handle { display: block;}
		.album-cover .handle a { display:inline-block; margin-right: 20px;}
			.album-cover .handle a i { background: url(../images/sns/pics.png) no-repeat scroll; vertical-align: text-top; display: inline-block; width: 17px; height: 16px; margin-right: 3px;}
		.album-cover .handle a.edit i { background-position: 0px -80px}
		.album-cover .handle a.del i { background-position: -83px -80px}
/* 相册图片列表页 */
.album {}
	.album .intro { height: 72px; padding: 15px 0; border-bottom: solid 1px #E7E7E7; position: relative; z-index: 99;}
		.album .intro .covers { background: url(../images/sns/album.png) no-repeat scroll 0px -228px; display: block; width: 60px; height: 60px; float: left; padding: 4px 8px 8px 4px;}
			.album .intro .covers img { max-width: 60px; max-height: 60px;}
	.album .intro dl { display: block; margin-left:20px; float: left;}
		.album .intro dl dt { font-size: 14px; font-weight: 600; line-height: 24px; height: 24px;}
		.album .intro dl dd {line-height: 18px; color: #999;}
	.album .intro .button  { background: #F7F7F7 url(../images/sns/repeat_bg.gif) repeat scroll 0px 0px; display: inline-block; height: 24px; padding: 3px; border: solid 1px #D8D8D8; border-radius: 4px; cursor: pointer; float:right; margin: 10px 20px 0 0;}
	.album .intro .button a {  font-weight: 600; line-height: 24px; color: #555; text-decoration: none; background-color: #FFF; display: block; height: 24px; padding: 0 12px;}
	.album .intro .button:hover { background: #FF7F00 none; border-color: #FF4000;}
		.album .intro .button:hover a, .album .intro .button:hover a:hover { color: #FFF ; background-color: #FF7F00;}
	.album .intro .upload-con { width:213px !important; top: 60px; right: 0 !important; left: auto;}
	.album .intro .arrow { top: 62px !important; left: auto !important; right: 80px;}
/* 相册图片内容页 */
.ad-gallery { background-color: #FFF; }
.ad-gallery, .ad-gallery * { padding: 0; margin: 0;}
	.ad-gallery .ad-nav { width: 670px; margin: 0 auto 15px auto; position: relative;}
		.ad-gallery .ad-forward, .ad-gallery .ad-back { height: 100%; position: absolute; top: 3px; z-index: 10;}
		/* IE 6 doesn't like height: 100% */
		* html .ad-gallery .ad-forward, .ad-gallery .ad-back { height: 90px;}
		.ad-gallery .ad-back {background: url(../images/sns/album.png) no-repeat 0 -320px; display: block; width: 17px;  left: -20px; cursor: pointer;}
		.ad-gallery .ad-forward { background: url(../images/sns/album.png) no-repeat -16px -320px; display: block; width: 17px; right: -20px; cursor: pointer}
		.ad-gallery .ad-nav .ad-thumbs { width: 100%; overflow: hidden;}
		.ad-gallery .ad-thumbs .ad-thumb-list { list-style: none; width: 9000px; height: 94px; float: left;}
        .ad-gallery .ad-thumbs li { display: inline-block; float: left; padding: 0 9px 0 0; margin: 0; overflow: hidden;}
		.ad-gallery .ad-thumbs li a { background-color:#FFF; display: inline-block; width: 60px; height: 60px; margin:15px 0; border: solid 1px #E7E7E7; /*IE7/8/9*/*text-align: center; overflow: hidden;}
		.ad-gallery .ad-thumbs li a .thumb { width: 60px; height: 60px;}
		 .ad-gallery .ad-thumbs li a.ad-active .thumb { width: 90px; height: 90px;}
		.ad-gallery .ad-thumbs li a.ad-active { display: block; width: 90px; height: 90px; border: 2px solid #0099FF; margin: 0px; overflow: hidden;} 			
		.ad-gallery .ad-thumbs li a img { max-width:60px; max-height: 60px; }
		.ad-gallery .ad-thumbs li a.ad-active img { max-width: 90px; max-height: 90px; }
		
	/*.ad-image-date { width: 200px; float: right;}
		.ad-image-date dt { line-height: 20px; font-weight: 600; color: #555; width: 100%; float: left; border-bottom: solid 1px #E7E7E7; font-size: 13px; padding: 5px 0;}
		.ad-image-date dd {  color: #999; float: left; width: 100%; padding : 5px 0 25px 0;}
			.ad-image-date dd p { line-height: 20px; display: block; width: 100%; clear: both; padding-top: 4px; padding-bottom: 4px;}
			.ad-image-date dd p b {  font-weight: normal; text-align: right; color: #555; width: 75px; float: left;}
			.ad-image-date dd p span { background-image: none;	padding-left: 10px;	float: left;}
				.ad-image-date dd p span a { line-height: 20px; text-decoration: underline; color: #999; background: url(../images/sns/album_bg.gif) no-repeat; width: 34px; height: 20px; padding-left: 26px; margin-right: 5px; display: block; float:left;}
				.ad-image-date dd p span a:hover { color: #FFF;	text-decoration: none;}
				.ad-image-date dd p span a.copy { background-position: -148px -400px;}
				.ad-image-date dd p span a:hover.copy {	background-position: 0px -400px;}
				.ad-image-date dd p span a.view { background-position: -148px -420px;}
				.ad-image-date dd p span a:hover.view { background-position: -60px -400px;}
*/
	.ad-gallery .ad-image-wrapper { width: 704px; height: 704px; padding: 0; margin: 0; border: 3px solid #E7E7E7; position: relative;overflow: hidden;}
    .ad-gallery .ad-image-wrapper .ad-loader { margin: -8px auto auto -8px; position: absolute; z-index: 1; top: 50%; left: 50%;}
    .ad-gallery .ad-image-wrapper .ad-next { display: block; width: 50%; height: 100%; position: absolute; z-index: 100; top: 0; right: 0; cursor: pointer;}
    .ad-gallery .ad-image-wrapper .ad-prev { display: block; width: 50%; height: 100%; position: absolute; z-index: 100; top: 0; left: 0; cursor: pointer;}
    
      .ad-gallery .ad-image-wrapper .ad-prev .ad-prev-image, .ad-gallery .ad-image-wrapper .ad-next .ad-next-image { background: url(../images/sns/album.png) no-repeat -92px -355px; display: none; width: 128px; height: 128px; margin-top: -64px; position: absolute; top: 50%; left: 0; z-index: 101;}
      .ad-gallery .ad-image-wrapper .ad-next .ad-next-image { background-position: -92px -225px; right: 0; left: auto;}
	.ad-gallery .ad-image-wrapper .ad-image { position: absolute; z-index: 9; top: 0; left: 0; overflow: hidden;}
	.ad-gallery .ad-image-wrapper .ad-image a img { border: 0;}
	.ad-gallery .ad-image-wrapper .ad-image .ad-image-description { color: #000; background: url(../images/sns/opa75.png); text-align: left; width: 100%; padding: 7px; position: absolute; z-index: 2; bottom: 0px; left: 0px; }
	* html .ad-gallery .ad-image-wrapper .ad-image .ad-image-description { background: none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader (enabled=true, sizingMethod=scale, src='../images/sns/opa75.png'); }
	.ad-gallery .ad-image-wrapper .ad-image .ad-image-description .ad-description-title { display: block;}
	
	.ad-gallery .ad-controls { line-height: 16px; width: 700px; height: 16px; padding: 5px; margin-top: 10px; }
    	.ad-gallery .ad-info { width: 100px; float: left;}
		.ad-gallery .ad-slideshow-controls { width: 130px; float: right;}
		.ad-gallery .ad-slideshow-running .ad-slideshow-start { color: #36C; background-position: 0px -456px; cursor: default;}
	/* Can't do display none, since Opera won't load the images then */
	.ad-preloads {  position: absolute; top: -9000px; left: -9000px;}
	.ad-showmode { width: 100%; clear: both; position: relative;}
		.ad-showmode p { position: absolute; top: -20px; right: 0; }
/* 秀商品弹出框 */

.addshare { font-size: 1em; display: inline-block; width:578px;}
.addshare { *display: inline;}
.addshare .goods-pic { width: 120px; height: 120px; float: left; margin: 10px;}
.addshare .goods-pic img { max-width: 120px; max-height: 120px;}
.addshare ul { float: left; padding-bottom: 8px; }
.addshare ul li { display: inline-block; width: 80px; height: 138px; position: relative; z-index: 99;}
.addshare .picture { background-color: #FFF; /* if IE7/8/9*/ *text-align: center; width: 60px; height: 60px; float: left; padding: 1px; margin:1px; border: solid 2px; border-color: #E7E7E7 #F7F7F7 #F7F7F7 #E7E7E7; position: absolute; z-index: 1; top: 15px; left:15px;}
.addshare .picture span i { *float: left/* IE7 */;}
.addshare .picture span img { max-height: 60px; max-width: 60px;}
.addshare .arrow-left { font-size: 0px; line-height: 0; width: 0px; height: 0px; border: 6px solid; border-color: transparent #D8D8D8 transparent transparent; position: absolute; z-index: 1; top: 45px; left:4px;}
.addshare .arrow-right { font-size: 0px; line-height: 0; width: 0px; height: 0px; border: 6px solid; border-color: transparent transparent transparent #D8D8D8; position: absolute; z-index: 1; top: 45px; right:4px;}
.addshare .bg { height: 68px; position: absolute; top: 15px; left: 17px; z-index:2;}
.addshare .arrow-left-op { font-size: 0px; line-height: 0; display: inline; width: 0px; height: 0px; float:left; margin: 30px 6px 0 0; border: 6px solid; border-color: transparent #0099CC transparent transparent; cursor: pointer; }
.addshare .delete { color: #FFF; line-height: 60px; background-color: #09F; vertical-align: middle; text-align: center; width: 60px; height: 60px; float:left; margin: 4px 2px; filter: alpha(opacity=85); -moz-opacity: 0.85; opacity: .85; cursor: pointer; text-shadow: 1px 1px 1px rgba(0,0,0,1);}
.addshare .arrow-right-op { font-size: 0px; line-height: 0; display:inline; width: 0px; height: 0px; float:left; margin: 30px 0 0 6px; border: 6px solid; border-color: transparent transparent transparent #0099CC; cursor: pointer;}
.addshare .upload-btn  { width:66px; height: 28px; padding: 0px; position: absolute; *z-index: -1; z-index:1; left: 15px; bottom: 15px;}

.addshare a .upload-button { line-height: 26px; text-decoration: none; color: #555; background: url(../images/member/bg_repeat_x.png) repeat-x 50% 0px;  text-align:center; display: block; width:64px; height: 26px; border: solid 1px; border-color: #E7E7E7 #D8D8D8 #D8D8D8 #E7E7E7; padding: 0px; position: absolute; z-index:1; top: 0px; left: 0px;}
.addshare a:hover .upload-button { color:#09C; background-position: 50% -26px; border-color: #A7CAED;}

/* 验证错误提示 */
#warning { display: none; background: url(../images/member/warning_bg.png) no-repeat scroll 0px 0px; padding: 6px 12px 12px 100px; min-height: 60px;}
#warning label { display: block; margin: 4px 0 0 0;}
#warning label.error { font-family: "microsoft yahei"; font-size: 1.4em; line-height: 20px; color: #D50000; padding-left: 12px;}


/* 版权：www.shopnc.net	*/
/*圈子列表*/
.circle-group-list {}
.circle-group-list ul { overflow: hidden; margin: 20px 0;}
.circle-group-list ul li { width: 48%; float: left; margin: 10px 1%;}
.circle-group-list dl.group-info { display: block; width: 230px; height: 76px; padding: 8px 10px 12px 95px; border: solid 2px #FFFFFF; position: relative; z-index: 1;}
.circle-group-list dl.group-info:hover { background-color: #DEF0FA; border-color: #60B3DD; box-shadow: 2px 2px 0 rgba(204,204,204,0.25);}
.circle-group-list dt.group-name { font-size: 16px; line-height: 20px; margin: 0 0 5px 0;}
.circle-group-list dt.group-name a { }
.circle-group-list dd.group-pic { background-color: #F7F7F7; width: 64px; height: 64px; padding: 5px; border: solid 1px #E7E7E7; position: absolute; z-index: 1; top: 10px; left: 8px; }
.circle-group-list dd.group-pic img { max-width: 64px; max-height: 64px;}
.circle-group-list dd { line-height: 18px; color: #666;}
.circle-group-list dd.group-intro { color: #999; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}

.circle-theme-list {}
.circle-theme-list ul { overflow: hidden; margin: 10px 0;}
.circle-theme-list ul li { padding: 20px 10px; border-bottom: solid 1px #E7E7E7;}
.circle-theme-list dl.theme-info {}
.circle-theme-list dt.theme-title { font-size: 16px; line-height: 24px; color: #333; margin-bottom: 6px;}
.circle-theme-list dt.theme-title span { font-size: 12px; color: #999; margin-left: 8px;}
.circle-theme-list dd.theme-file { display: block;}
.circle-theme-list dd.theme-file .thumb-cut { width: 100px; height: 100px; margin-right: 5px; float: left;}
.circle-theme-list dd.theme-file .thumb-cut a { width: 100px; height: 100px;}
.circle-theme-list dd.theme-txt { color: #555; display: block; clear: both; margin: 10px 0;}
.circle-theme-list dd.theme-txt p { display: block; max-height: 60px;  overflow: hidden;}
.circle-theme-list dd.theme-txt a { white-space: nowrap; margin-left: 8px;}
.circle-theme-list dd.theme-date { color: #999;}
.circle-theme-list dd.theme-date span { margin-left: 24px;}