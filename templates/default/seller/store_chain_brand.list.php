<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="tabmenu">

  <?php include template('layout/submenu');?>

    <a href="<?php echo urlShop('store_chain', 'chain_brand_add');?>" class="ncbtn ncbtn-mint" title="添加品牌"><i class="icon-plus-sign"></i>添加品牌</a>
  <a href="<?php echo CHAIN_SITE_URL;?>" class="ncbtn ncbtn-aqua" style="right:90px" target="_blank" title="进入门店系统"><i class="icon-building"></i>进入门店系统</a>
</div>
<table class="ncsc-default-table">
    <thead>
    <tr>
        <th class="w200 ">ID</th>
        <th class="w200 ">品牌名</th>
        <th class="w200 ">品牌广告词</th>

        <th class="w200 ">品牌logo</th>
        <th class="w200">图片</th>
        <th class="w200">小程序品牌logo</th>
        <th class="w200"><?php echo $lang['nc_handle'];?><a href="javascript:;" onclick="" ></a></th>
    </tr>
  </thead>
  <tbody>
    <?php if (!empty($output['chain_list'])) { ?>
    <?php foreach($output['chain_list'] as $val) { ?>
            <tr class="bd-line" title="">
                <td><?php echo $val['chain_brand_id']; ?></td>
                <td><?php echo $val['brand_name']; ?></td>
                <td><?php echo $val['brand_text']; ?></td>
                <td><img src="<?php echo getChainImage($val['logo_url'], $val['store_id']); ?>" alt=""
                         style="max-width: 100px; max-height: 40px;"></td>
                <td><img src="<?php echo getChainImage($val['image_url'], $val['store_id']); ?>" alt=""
                         style="max-width: 100px; max-height: 40px;"></td>
                <td><img src="<?php echo getChainImage($val['xcx_image_url'], $val['store_id']); ?>" alt=""
                         style="max-width: 100px; max-height: 40px;"></td>
                <td class="nscs-table-handle">

                    <span><a href="<?php echo urlShop('store_chain', 'chain_brand_edit', array('chain_brand_id' => $val['chain_brand_id'])); ?>"
                             class="btn-grapefruit"><i class="icon-edit"></i><p><?php echo $lang['nc_edit']; ?></p></a></span>

                </td>
            </tr>
        <?php } ?>
    <?php } else { ?>
    <tr>
      <td colspan="20" class="norecord"><div class="warning-option"><i class="icon-warning-sign"></i><span><?php echo $lang['no_record'];?></span></div></td>
    </tr>
    <?php } ?>
  </tbody>
  <tfoot>
    <?php if (!empty($output['chain_list'])) { ?>
    <tr>
      <td colspan="20"><div class="pagination"><?php echo $output['show_page']; ?></div></td>
    </tr>
    <?php } ?>
  </tfoot>
</table>
<script>
    var curpage = 1;
    var lng_curpage = 1;
    var hasmore = true;
    var lng_hasmore = true;
    var param = {};

var layer;
    $(function() {
        layui.use('layer', function () { //独立版的layer无需执行这一句
            layer = layui.layer; //独立版的layer无需执行这一句
            //layer.msg("正在更新数据！",{ shadeClose: true, time:100000});
        });
    });
    $('#update_doctor_list').click(function(){
        update_doctor();

            layer.open({
                type: 1
                ,title: false //不显示标题栏
                ,closeBtn: false
                ,area: '300px;'
                ,shade: 0.8
                ,id: 'LAY_layuipro' //设定一个id，防止重复弹出

                ,btnAlign: 'c'
                ,moveType: 1 //拖拽模式，0或者1
                ,content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">正在更新数据，请不要刷新，页面。</div>'
            });
    });
    $('#clear_city').click(function(){
        $.ajax({
            url: '/index.php?act=store_chain&op=clear_chain',
            data: param,
            success : function(result){
                result = eval('('+result+')');
                layer.msg(result.msg,{icon: 1})
            }
        });

    });

    /**
     * 更新医生信息
     */
    function update_doctor(){

        hasmore = false;
        param = {};
        param.curpage = curpage;
        $.ajax({
            url: '/index.php?act=store_chain&op=update_doctor_list',
            data: param,
            success : function(result){
                if(!result) {

                }
                result = eval('('+result+')');
                curpage++;
                hasmore = result.hasmore;

                if (!hasmore) {
                    layer.closeAll();
                    layer.msg('更新完成',{icon: 1});
                    curpage=1;
                    hasmore = true;
                    return false;
                }else{
                    update_doctor();
                }
            }
        });
    }
    $('#lnglat').click(function(){
        lnglat();

        layer.open({
            type: 1
            ,title: false //不显示标题栏
            ,closeBtn: false
            ,area: '300px;'
            ,shade: 0.8
            ,id: 'LAY_layuipre' //设定一个id，防止重复弹出

            ,btnAlign: 'c'
            ,moveType: 1 //拖拽模式，0或者1
            ,content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">正在更新数据，请不要刷新，页面。</div>'
        });
    });
    /**
     * 更新医生信息
     */
    function lnglat(){

        lng_hasmore = false;
        param = {};
        param.curpage = lng_curpage;
        $.ajax({
            url: '/index.php?act=store_chain&op=lngTransverter',
            data: param,
            success : function(result){
                if(!result) {

                }
                result = eval('('+result+')');
                lng_curpage++;
                lng_hasmore = result.hasmore;

                if (!lng_hasmore) {
                    layer.closeAll();
                    layer.msg('转换完成',{icon: 1});
                    lng_curpage=1;
                    lng_hasmore = true;
                    return false;
                }else{
                    lnglat();
                }
            }
        });
    }
</script>