<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="ncsc-flow-layout">
  <div class="ncsc-flow-container">
    <div class="title">
      <h3>退货退款服务</h3><span class="fr">下单渠道：<?php echo is_wx_live($output['order_info']) ? '视频号': '阿闻' ?></span>
    </div>

      <?php if ($output['return']['refund_state'] == 4 && is_wx_live($output['order_info'])): ?>
          <p style="line-height: 5.5;height:200px;text-align: center;font-size: 18px;font-weight: 700">买家取消申请</p>
      <?php elseif($output['return']['seller_state'] == 1 && is_wx_live($output['order_info'])): ?>
          <p style="height:200px;text-align: center;font-size: 18px;font-weight: 700;margin-top: 50px;">待商家审核<br/><br/>
              <span style="font-size: 12px;font-weight: 400">商家还有<span id="span-hour">00</span>小时<span id="span-minute">00</span>分<span id="span-second">00</span>秒审核，超时不处理，系统自动审核通过</span>
          </p>
      <?php else: ?>

    <div id="saleRefundReturn">
      <div class="ncsc-flow-step">
        <dl class="step-first current">
          <dt>买家申请退货</dt>
          <dd class="bg"></dd>
        </dl>
        <dl class="<?php echo $output['return']['seller_time'] > 0 ? 'current':'';?>">
          <dt>商家处理退货申请</dt>
          <dd class="bg"> </dd>
        </dl>
        <dl class="<?php echo ($output['return']['ship_time'] > 0 || $output['return']['return_type']==1) ? 'current':'';?>">
          <dt>买家退货给商家</dt>
          <dd class="bg"> </dd>
        </dl>
        <dl class="<?php echo $output['return']['admin_time'] > 0 ? 'current':'';?>">
          <dt>确认收货，平台审核</dt>
          <dd class="bg"> </dd>
        </dl>
      </div>
        <?php endif ?>
      <div class="ncsc-form-default">
        <h3>买家退货退款申请</h3>
        <dl>
          <dt>退货退款编号：</dt>
          <dd><?php echo $output['return']['refund_sn']; ?> </dd>
        </dl>
        <dl>
          <dt>申请人（买家）：</dt>
          <dd><?php echo $output['return']['buyer_name']; ?></dd>
        </dl>
        <dl>
          <dt><?php echo $lang['return_buyer_message'].$lang['nc_colon'];?></dt>
          <dd> <?php echo $output['return']['reason_info']; ?> </dd>
        </dl>
        <dl>
          <dt>退款金额：</dt>
          <dd><?php echo $lang['currency'];?><?php echo $output['return']['refund_amount']; ?> <?php echo $output['refund_pre_msg']; ?> </dd>
        </dl>
        <dl>
          <dt><?php echo $lang['return_order_return'].$lang['nc_colon'];?></dt>
          <dd><?php echo $output['return']['return_type']==2 ? $output['return']['goods_num']:'无'; ?></dd>
        </dl>
        <dl>
          <dt>退货说明：</dt>
          <dd> <?php echo $output['return']['buyer_message']; ?> </dd>
        </dl>
        <dl>
          <dt>凭证上传：</dt>
          <dd>
            <?php if (is_array($output['pic_list']) && !empty($output['pic_list'])) { ?>
            <ul class="ncsc-evidence-pic">
              <?php foreach ($output['pic_list'] as $key => $val) { ?>
              <?php if(!empty($val)){ ?>
              <li><a href="<?php echo refund_image($val);?>" nctype="nyroModal" rel="gal" target="_blank"> <img class="show_image" src="<?php echo refund_image($val);?>"></a></li>
              <?php } ?>
              <?php } ?>
            </ul>
            <?php } ?>
          </dd>
        </dl>
        <form id="post_form" method="post" action="index.php?act=store_return&op=edit&return_id=<?php echo $output['return']['refund_id']; ?>">
          <input type="hidden" name="form_submit" value="ok" />
          <h3>商家处理意见</h3>
          <dl>
            <dt><i class="required">*</i><?php echo $lang['return_seller_confirm'].$lang['nc_colon'];?></dt>
            <dd>
              <div>
                <label class="mr20">
                  <input type="radio" class="vm" name="seller_state" value="2" />
                  同意</label>
                <label>
                  <input name="return_type" class="vm" type="checkbox" value="1" />
                  弃货</label>
                <p class="hint">如果选择弃货，买家将不用退回原商品，提交后直接由管理员确认退款。</p>
              </div>
              <div class="mt10">
                <label>
                  <input type="radio" class="vm" name="seller_state" value="3" />
                  拒绝</label>
              </div>
              <span class="error"></span>
            </dd>
          </dl>
          <dl>
            <dt><i class="required">*</i><?php echo $lang['return_message'].$lang['nc_colon'];?></dt>
            <dd>
              <textarea name="seller_message" rows="2" class="textarea w300"></textarea>
              <span class="error"></span>
              <p class="hint"> 如是同意退货，请及时关注买家的发货情况，并进行收货（发货5天后可以选择未收到，超过7天不处理按弃货处理）。<br>
              </p>
            </dd>
          </dl>
          <div class="bottom">
              <label class="submit-border">
                <a class="submit" id="confirm_button"><?php echo $lang['nc_ok'];?></a>
              </label>
              <label class="submit-border">
                <a href="javascript:history.go(-1);" class="submit"><i class="icon-reply"></i>返回列表</a>
              </label>
          </div>
        </form>
      </div>
    </div>
      <?php require template('seller/store_refund_right');?>
  </div>
</div>
<script type="text/javascript">
$(function(){
    $("#confirm_button").click(function(){
        $("#post_form").submit();
    });
    $('#post_form').validate({
		errorPlacement: function(error, element){
			error.appendTo(element.parentsUntil('dl').find('span.error'));
        },
         submitHandler: function(form) {
			    	ajaxpost('post_form', '', '', 'onerror');
				 },
        rules : {
            seller_state : {
                required   : true
            },
            seller_message : {
                required   : true
            }
        },
        messages : {
            seller_state  : {
                required  : '<i class="icon-exclamation-sign"></i><?php echo $lang['return_seller_confirm_null'];?>'
            },
            seller_message  : {
                required   : '<i class="icon-exclamation-sign"></i><?php echo $lang['return_message_null'];?>'
            }
        }
	    });


    $(function (){
        //倒计时工具函数封装
        function countDown(date) {
            var starttime = Date.parse(new Date(date)); //4月30号18点
            var nowtime = Date.parse(new Date());
            var time = starttime - nowtime;
            var day = parseInt(time / 1000 / 60 / 60 / 24);
            var hour = parseInt(time / 1000 / 60 / 60 % 24);
            var minute = parseInt(time / 1000 / 60 % 60);
            var seconds = parseInt(time / 1000 % 60);
            // return day + "天" + hour + "小时" + minute + "分" + seconds + "秒";
            return [day, hour, minute, seconds]
        }

        //调用封装好的倒计时工具函数
        var obj = setInterval(() => {
            var date = countDown("<?php echo date('Y/m/d H:i:s', $output['return']['add_time'] + 48*3600); ?>");
            if (date.indexOf('-') != -1 || date[3] < 0) {
                clearInterval(obj)
                console.log('倒计时完了');
            } else {
                $("#span-hour").text(date[0] * 24 + date[1])
                $("#span-minute").text(date[2])
                $("#span-second").text(date[3])
            }
        }, 1000)
    })

});
</script>
