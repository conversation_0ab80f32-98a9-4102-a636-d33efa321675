<?php defined('InShopNC') or exit('Access Invalid!');?>
<div class="tabmenu">
  <?php include template('layout/submenu');?>
</div>
<div class="alert alert-info alert-block">
  <div class="faq-img"></div>
  <h4>说明：</h4>
  <ul>
    <li>1.请不要重复选择同一个商品。</li>
    <li>2.特殊商品（如：虚拟商品、F码商品、定金预售、全款预售）不能作为赠品。</li>
    <li>3.赠品为正常销售中的商品，赠品送出后会记录销量。</li>
    <li>4.如赠品库存不足或已经下架，不会被作为赠品加入到订单流程。</li>
    <li>6.虚拟商品不能添加赠品。</li>
  </ul>
</div>
<form method="post" id="goods_gift" action="<?php echo urlShop('store_goods_online', 'save_gift');?>">
  <input type="hidden" name="form_submit" value="ok">
  <input type="hidden" name="ref_url" value="<?php echo $_GET['ref_url'];?>" />
  <input type="hidden" name="commonid" value="<?php echo intval($_GET['commonid']);?>" />
  <?php if (!empty($output['goods_array'])) {?>
  <?php foreach ($output['goods_array'] as $value) {?>
  <div class="ncsc-form-goods-gift" data-gid="<?php echo $value['goods_id'];?>">
    <div class="goods-pic"> <span><img src="<?php echo thumb($value, 240);?>"/></span></div>
    <div class="goods-summary">
      <h2><?php echo $value['goods_name'];?><em>SKU：<?php echo $value['goods_id'];?></em></h2>
      <dl>
        <dt>商品价格：</dt>
        <dd>￥<?php echo ncPriceFormat($value['goods_price']);?></dd>
      </dl>
      <dl>
        <dt>库&nbsp;&nbsp;存&nbsp;&nbsp;量：</dt>
        <dd><?php echo $value['goods_storage'];?></dd>
      </dl>      
      <dl>
        <dt>赠品捆绑：</dt>
        <dd>
          <ul class="goods-gift-list" nctype="choose_goods_list">
            <?php if (!empty($output['gift_array'][$value['goods_id']])) {?>
            <?php foreach ($output['gift_array'][$value['goods_id']] as $gift) {?>
            <li>
              <div class="pic-thumb"><span><img src="<?php echo cthumb($gift['gift_goodsimage'], '60', $_SESSION['store_id']);?>"></span></div>
              <dl class="goods_name" style="height: 56px;">
                <dt><?php echo $gift['gift_goodsname'];?></dt>
                <dd>赠品数量：
                  <input class="text" type="text" value="<?php echo $gift['gift_amount'];?>" name="gift[<?php echo $value['goods_id'];?>][<?php echo $gift['gift_goodsid'];?>][gift_amount]">
                </dd>
                <dd>赠送时间：<span><?php echo $gift['start_time'] ? substr($gift['start_time'],0, 10) :'';?>至<?php echo $gift['end_time'] ? substr($gift['end_time'],0, 10) :'';?></span></dd>
                <input type="hidden" value="<?php echo $gift['start_time'] ? substr($gift['start_time'],0, 10) :'';?>" name="gift[<?php echo $value['goods_id'];?>][<?php echo $gift['gift_goodsid'];?>][start_time]">
                <input type="hidden" value="<?php echo $gift['end_time'] ? substr($gift['end_time'],0, 10) :'';?>" name="gift[<?php echo $value['goods_id'];?>][<?php echo $gift['gift_goodsid'];?>][end_time]">
              </dl>
              <a class="gift-del" nctype="del_choosed" href="javascript:void(0);" title="删除赠品">X</a></li>
            <?php }?>
            <?php }?>
          </ul>
          <?php if ($value['is_virtual'] == 0) {?>
          <a class="ncbtn-mini ncbtn-aqua" onclick="choiceGoods(this,<?php echo $value['goods_id']; ?>)" href="javascript:void(0);">
              <i class="icon-gift"></i>选择赠品
          </a>
          <?php } else {?>
          <a class="ncbtn-mini" href="javascript:void(0);"><i class="icon-gift"></i>不能添加赠品</a>
          <?php }?>
        </dd>
      </dl>
    </div>
    <div class="div-goods-select" style="display: none;">
      <table class="search-form">
        <thead>
          <tr>
            <th style="float: right;width: 85px;">
              <select name="search_type">
                <option value="0">商品名称</option> 
                <option value="1">sku</option>
              </select>
            </th>
            <!-- <th>商品名称</th> -->
            <td class="w160"><input class="text" type="text" name="search_gift"></td>
            <td class="tc w70"><a class="ncbtn" href="javascript:void(0);" nctype="search_gift"><i class="icon-search"></i>搜索</a></td>
            <td class="w10"></td>
          </tr>
        </thead>
      </table>
      <div class="search-result" nctype="gift_goods_list"></div>
      <a class="close" href="javascript:void(0);" nctype="btn_hide_goods_select">X</a> </div>
  </div>
  <?php }?>
  <?php }?>
  <div class="bottom tc">
    <label class="submit-border">
      <input type="submit" class="submit" value="确认提交" />
    </label>
  </div>
</form>

<div id="ncsc-form-goods-gift-mask" class="ncsc-form-goods-gift-mask"></div>
<div id="ncsc-form-goods-gift-alert" class="ncsc-form-goods-gift-alert">
  <div class="goods-gift-title">添加赠品</div>
  <ul>
    <li>
      <dd><strong>设置赠送时间：</strong></dd>
    </li>
    <li>
      <dd>
        开始时间：
        <input id="start_time" name="start_time" type="text" placeholder="请选择起始时间" class="text w200" /><em class="add-on"><i class="icon-calendar"></i></em>
        <div id="start_time_error" class="error-message"></div>
      </dd>
    </li>
    <li>
      <dd>
        结束时间：
        <input id="end_time" name="end_time" type="text" placeholder="请选择结束时间" class="text w200"/><em class="add-on"><i class="icon-calendar"></i></em>
        <div id="end_time_error" class="error-message"></div>
      </dd>
    </li>
    <li>
      <dd>
        赠送件数：<input id="gift_number" name="gift_number" class="gift-number" type="text" placeholder="请选填写1-999的数字" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')">
        <div id="gift_number_error" class="error-message"></div>
      </dd>
    </li>
  </ul>
  <div class="add-gift-button">
    <button id="add-gift-button-cancel" class="add-gift-button-cancel">关闭</button>
    <button id="add-gift-button-submit" class="add-gift-button-submit">提交</button>
  </div>
</div>
<link rel="stylesheet" type="text/css" href="<?php echo RESOURCE_SITE_URL;?>/js/jquery-ui/themes/ui-lightness/jquery.ui.css"  />
<script src="<?php echo RESOURCE_SITE_URL; ?>/js/jquery-ui/i18n/zh-CN.js"></script>
<script src="<?php echo RESOURCE_SITE_URL; ?>/js/jquery-ui-timepicker-addon/jquery-ui-timepicker-addon.min.js"></script>
<link rel="stylesheet" type="text/css"
      href="<?php echo RESOURCE_SITE_URL; ?>/js/jquery-ui-timepicker-addon/jquery-ui-timepicker-addon.min.css"/>
<script type="text/javascript">
    // 当前选中的商品id
    let goodsId = 0;
    function choiceGoods(_this, id) {
        goodsId = id;
        $(_this).parents('.goods-summary:first').nextAll('.div-goods-select').show()
            .find('input[name="search_gift"]').val('').end()
            .find('a[nctype="search_gift"]').click();
    }

$(function(){
  $('#start_time').datepicker({dateFormat: "yy-mm-dd"});
  $('#end_time').datepicker({dateFormat: "yy-mm-dd"});

	//凸显鼠标触及区域、其余区域半透明显示
	$("#goods_gift > div").jfade({
        start_opacity:"1",
        high_opacity:"1",
        low_opacity:".25",
        timing:"200"
    });

    // 关闭按钮
    $('a[nctype="btn_hide_goods_select"]').click(function(){
        $(this).parent().hide();
    });

    // 所搜商品 
    $('a[nctype="search_gift"]').click(function(){
        _url = "<?php echo urlShop('store_goods_online', 'search_goods');?>";
        _name = $(this).parents('tr').find('input[name="search_gift"]').val();
        _searchType = $(this).parents('tr').find('select[name="search_type"]').val();
        $(this).parents('table:first').next().load(_url + '&name=' + _name+'&search_type='+_searchType);
    });

    // 分页
    $('div[nctype="gift_goods_list"]').on('click', 'a[class="demo"]', function(){
        $(this).parents('div[nctype="gift_goods_list"]').load($(this).attr('href'));
        return false;
    });

    // 删除
    $('ul[nctype="choose_goods_list"]').on('click', 'a[nctype="del_choosed"]', function(){
        $(this).parents('li:first').remove();
    });

    // 选择商品
    var select_gift = {};
    $('div[nctype="gift_goods_list"]').on('click', 'a[nctype="a_choose_goods"]', function(){
        select_gift = this;
        $('#ncsc-form-goods-gift-alert').show();
        $('#ncsc-form-goods-gift-mask').show();
    });

    $('#add-gift-button-cancel').on('click', function(){
      select_gift = null;
      $('#ncsc-form-goods-gift-alert').find('input').val("");
      $('#ncsc-form-goods-gift-alert').find('div[class="error-message"]').text("");
      $('#ncsc-form-goods-gift-alert').hide();
      $('#ncsc-form-goods-gift-mask').hide();
    });

    $('#add-gift-button-submit').on('click', function(){
      var gift_number = Number($('#gift_number').val());
      var start_time = $('#start_time').val();
      var end_time = $('#end_time').val();
      var st = Number(start_time.replace(/-/g, ''));
      var et = Number(end_time.replace(/-/g, ''));
      var ct = Number(getCurDate());
      if (st > et) {
        $("#start_time_error").text("开始日期不能大于结束日期");
        return
      }
      if (ct > st) {
        $("#start_time_error").text("开始日期不能小于当前日期");
        return
      }
      if (gift_number <= 0 || gift_number > 999) {
        $("#gift_number_error").text("赠送数量必须是1-999之间");
        return
      }
      $('#ncsc-form-goods-gift-alert').find('input').val("");
      $('#ncsc-form-goods-gift-alert').find('div[class="error-message"]').text("");

      eval('var data_str = ' + $(select_gift).attr('data-param'));
      _li = $('<li></li>')
          .append('<div class="pic-thumb"><span><img src="' + data_str.gimage + '"></span></div>')
          .append('<dl class="goods_name" style="height: 56px;"><dt>' + data_str.gname + '</dt>' +
            '<dd>赠品数量：<input class="text" type="text" value="'+ gift_number +'" name="gift[' + goodsId + '][' + data_str.gid + '][gift_amount]"></dd>' +
            '<dd>赠送时间：<span>'+ start_time +'至'+ end_time +'</span></dd>'+
            '<input type="hidden" value="'+ start_time +'" name="gift[' + goodsId + '][' + data_str.gid + '][start_time]">'+
            '<input type="hidden" value="'+ end_time +'" name="gift[' + goodsId + '][' + data_str.gid + '][end_time]">')
          .append('</dl><a class="gift-del" nctype="del_choosed" href="javascript:void(0);" title="删除赠品">X</a>');
      $(select_gift).parents('.div-goods-select:first').prev().find('ul[nctype="choose_goods_list"]').append(_li);
      $('#ncsc-form-goods-gift-alert').hide();
      $('#ncsc-form-goods-gift-mask').hide();
    });
    
    $('#goods_gift').submit(function(){
        ajaxpost('goods_gift', '', '', 'onerror');
    });

    function getCurDate() {
      var nowDate = new Date();
      var year = nowDate.getFullYear();
      var month = nowDate.getMonth() + 1 < 10 ? "0" + (nowDate.getMonth() + 1):nowDate.getMonth() + 1;
      var day = nowDate.getDate() < 10 ? "0" + nowDate.getDate() : nowDate.getDate();
      return year + "" + month + "" + day;
    }
});
</script> 
