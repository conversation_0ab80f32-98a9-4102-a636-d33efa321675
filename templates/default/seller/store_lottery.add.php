<link type="text/css" rel="stylesheet" href="<?php echo RESOURCE_SITE_URL."/js/jquery-ui/themes/ui-lightness/jquery.ui.css";?>"/>
<link rel="stylesheet" href="<?php echo RESOURCE_SITE_URL;?>/js/jquery-ui-timepicker-addon/jquery-ui-timepicker-addon.min.css">

<style>
.act-imglist .ncsc-store-slider{border:none}
.act-imglist .ncsc-store-slider li{width: 160px;opacity: 0; border: 1px solid #eee;border-left: none;margin: 0;position:relative;animation:imgadded .3s 0.05s forwards;}
@keyframes imgadded {
    0% {opacity: 0; }
    100% {opacity: 1;}
}
.act-imglist .ncsc-store-slider li:nth-child(n+5){border-top:none}
.act-imglist .ncsc-store-slider li:nth-child(4n+1){border-left:1px solid #eee;}
.act-imglist .ncsc-store-slider li:before{content:"";display:block;left: -1px;top: -1px;width: 100%;height:100%;position:absolute;border: 1px solid #27A9E3;z-index:20;overflow: auto; pointer-events: none; opacity: 0;transition: all 0.2s;}
.act-imglist .ncsc-store-slider li .img-remove{position: absolute;display: flex; transition: all 0.2s;opacity: 0; top: 0;font-size: 12px;right: 0;width: 26px;height: 24px;font-weight: 700;justify-content: center;align-items: center;color: #fff;background: #27A9E3;}
.act-imglist .ncsc-store-slider li:hover:before,.act-imglist .ncsc-store-slider li:hover .img-remove{opacity: 1;}
.ncsc-form-default dl:hover .act-imglist .ncsc-store-slider li{background:#FFF;}
.act-imglist .ncsc-store-slider li .picture:hover { border-color: #f5f5f5;}
.act-imglist .ncsc-store-slider li .url label{margin-top: 6px; display: block;}

.btn-imgadd-div .ncsc-store-slider li{width: 400px;}
.btn-imgadd-div .ncsc-store-slider{border: none;}

.btn-skuadd-div .bsd-close,.btn-giftadd-div .bsd-close{position: absolute; width: 100%; height: 100%; z-index: 998; background: rgba(255,255,255,0.15);}
.btn-skuadd-div,.btn-giftadd-div{display: none; position: absolute; left: 0; right: 0;top: 0; bottom: 0; z-index: 99; border: 1px solid #ddd; }
.btn-skuadd-div .bsd-close,.btn-giftadd-div .bsd-close{position: absolute; width: 100%; height: 100%; z-index: 998; background: rgba(255,255,255,0.15);}
.btn-skuadd-box,.btn-giftadd-box{position: fixed; overflow: hidden; min-height: 300px;max-height: 480px; max-height:  border-radius: 4px; left: calc(50% - 200px); width: 560px; top: calc(50% - 240px); z-index: 999;background: #fff; padding-bottom: 60px; box-shadow: 0 0 12px rgba(0,0,0,0.2)}
.btn-skuadd-div h3, .btn-giftadd-div h3 { font-size: 14px; font-weight: 600; line-height: 22px; color: #000; clear: both; background-color: #F5F5F5; padding: 5px 0 5px 12px; border-bottom: solid 1px #E7E7E7;}
.btn-skuadd-box .bsb-close,.btn-giftadd-box .bsb-close{display: block; float: right; width: 20px; text-align: center; line-height: 20px; height: 20px; margin-right: 10px; background: #f5f5f5; font-size: 20px; font-weight: 700;}
.btn-skuadd-table,.btn-giftadd-table{padding: 0 20px 20px; max-height: 400px; overflow: hidden; overflow-y: auto;}
.bsd-bottom{display: flex; justify-content: center; bottom: 0; position: absolute; left: 0; width: 100%;}
.bsd-bottom .errtext{position: absolute;top: calc(50% - 25px); left: 20px; color: #d23; height: 50px; line-height: 20px; font-style: 12px; width: 200px; box-sizing:border-box;}
.btn-skuadd-table .errtext,.btn-giftadd-table .errtext{ color: #f57; line-height: 20px; font-size: 12px; }
.btn-skuadd-box .search-form{margin: 0 20px;width:unset; border: none;}
</style>
  <div class="tabmenu">
    <?php include template('layout/submenu');?>
  </div>
	<div class="ncsc-form-default">
	  <form id="add_form" method="post" enctype="multipart/form-data" action="index.php?act=store_lottery&op=<?php echo $output['type']=='add'?'lottery_add':'lottery_edit'; ?>">
	  	<input type="hidden" id="act" name="act" value="store_lottery"/>
	  	<?php if ($output['type'] == 'add'){?>
	  	<input type="hidden" id="op" name="op" value="lottery_add"/>
	  	<?php }else {?>
	  	<input type="hidden" id="op" name="op" value="lottery_edit"/>
	  	<input type="hidden" id="lottery_id" name="lottery_id" value="<?php echo $output['lottery_info']['id'];?>"/>
	  	<?php }?>
	  	<input type="hidden" id="form_submit" name="form_submit" value="ok"/>
	    <dl>
	      <dt><?php echo '活动标题'.$lang['nc_colon']; ?></dt>
	      <dd>
	        <input type="text" class="w300 text" name="lottery_title" value="<?php echo $output['lottery_info']['lottery_title'];?>" maxlength=50 />
	        <span></span>
	      </dd>
	    </dl>
          <dl>
              <dt><i class="required">*</i><em class="pngFix"></em><?php echo '活动时间'.$lang['nc_colon']; ?></dt>
              <dd>
                  <input type="text" class="text w100" id="lottery_start_time" name="lottery_start_time" value="" readonly <?php if($output['lottery_info']['lottery_state']>0){echo 'disabled';}?> ><em class="add-on"><i class="icon-calendar"></i></em>

                  <em class="pngFix">至</em>
              <input type="text" class="text w100" id="lottery_end_time" name="lottery_end_time" value="" readonly <?php if($output['lottery_info']['lottery_state']>1){echo 'disabled';}?>><em class="add-on"><i class="icon-calendar"></i></em>
                  <span></span>
              </dd>
          </dl>
	    <dl>
            <dl>
                <dl>
                    <dt><?php echo '分享卡图片'.$lang['nc_colon']; ?></dt>
                    <dd>
                        <div id="share_preview" class="ncsc-upload-thumb voucher-pic"><p><?php if ($output['lottery_info']['share_image_url']){?>
                                    <img src="<?php echo $output['lottery_info']['share_image_url'];?>"/>
                                <?php }else {?>
                                    <i class="icon-picture"></i>
                                <?php }?></p>
                        </div>
                        <div class="ncsc-upload-btn"><a href="javascript:void(0);"><span>
          <input type="file" hidefocus="true" size="1" class="input-file" onchange="imageShareShow()" name="share_image_url" id="share_image_url" />
          </span>
                                <p><i class="icon-upload-alt"></i>图片上传</p>
                            </a> </div>
                        <span></span>
                        <p class="hint"><?php printf($lang['store_sns_normal_tips'],intval(C('image_max_filesize'))/1024);?></p>
                    </dd>
                </dl>
            </dl>
            <dl>
                <dt><i class="required">*</i><?php echo '活动图片'.$lang['nc_colon']; ?></dt>
                <dd>
                    <div id="customimg_preview" class="ncsc-upload-thumb voucher-pic"><p><?php if ($output['lottery_info']['draw_image_url']){?>
                                <img src="<?php echo $output['lottery_info']['draw_image_url'];?>"/>
                            <?php }else {?>
                                <i class="icon-picture"></i>
                            <?php }?></p>
                    </div>
                    <?php if($output['lottery_info']['lottery_state']!=2){?>
                    <div class="ncsc-upload-btn"><a href="javascript:void(0);">

                            <span>
          <input type="file" hidefocus="true" size="1" class="input-file" onchange="imageShow()" name="draw_image_url" id="draw_image_url" />

                            </span>

                            <p><i class="icon-upload-alt"></i>图片上传</p>

                        </a> </div>
                    <?php }?>
                    <span></span>
                    <p class="hint"><?php printf($lang['store_sns_normal_tips'],intval(C('image_max_filesize'))/1024);?></p>
                </dd>
            </dl>
	    </dl>

	    <dl>
                      <dt><i class="required">*</i><?php echo '登录上方图片'.$lang['nc_colon']; ?></dt>
                      <dd>
                          <div id="login_preview" class="ncsc-upload-thumb voucher-pic"><p><?php if ($output['lottery_info']['login_image_url']){?>
                                      <img src="<?php echo $output['lottery_info']['login_image_url'];?>"/>
                                  <?php }else {?>
                                      <i class="icon-picture"></i>
                                  <?php }?></p>
                          </div>
                          <div class="ncsc-upload-btn"><a href="javascript:void(0);"><span>
                  <input type="file" hidefocus="true" size="1" class="input-file" onchange="imageLoginShow()" name="login_image_url" id="login_image_url" />
                  </span>
                                  <p><i class="icon-upload-alt"></i>图片上传</p>
                              </a> </div>
                          <span></span>
                          <p class="hint"><?php printf($lang['store_sns_normal_tips'],intval(C('image_max_filesize'))/1024);?></p>
                      </dd>
                  </dl>
                      <dl>
                          <dt><i class="required">*</i><?php echo '分享海报图片'.$lang['nc_colon']; ?></dt>
                          <dd>
                              <div id="s_preview" class="ncsc-upload-thumb voucher-pic"><p><?php if ($output['lottery_info']['s_image_url']){?>
                                          <img src="<?php echo $output['lottery_info']['s_image_url'];?>"/>
                                      <?php }else {?>
                                          <i class="icon-picture"></i>
                                      <?php }?></p>
                              </div>
                              <div class="ncsc-upload-btn"><a href="javascript:void(0);"><span>
                  <input type="file" hidefocus="true" size="1" class="input-file" onchange="imageShShow()" name="s_image_url" id="s_image_url" />
                  </span>
                                      <p><i class="icon-upload-alt"></i>图片上传</p>
                                  </a> </div>
                              <span></span>
                              <p class="hint"><?php printf($lang['store_sns_normal_tips'],intval(C('image_max_filesize'))/1024);?></p>
                          </dd>
                      </dl>


        <dl>
            <dl>
                <dt><i class="required">*</i><?php echo '广告图片&路径'.$lang['nc_colon']; ?></dt>
                <dd>

<div class="act-imglist">

  <!-- 图片上传部分 -->
  <ul class="ncsc-store-slider" id="act_images_box">
      <?php if($output['lottery_info']['adv_content']){
          foreach ($output['lottery_info']['adv_content'] as $value){?>
      <li class="act_imgaes_li">
          <a href="javascript:;" class="img-remove" title="移除">X</a>
          <div class="picture">
              <img src="<?php echo $value['image_url']?>">
              <input class="adv_image_value" type="hidden" name="adv_image[]" value="<?php echo $value['image']?>">
          </div>
          <div class="url">
              <label>跳转路径：</label>
              <input type="text" placeholder="请输入图片跳转路径" class="text w150"  name="link_path[]" value="<?php echo $value['link_path']?>">
          </div>
      </li>
      <?php }}?>
  </ul>
  <a class="ncbtn ncbtn-mint" href="javascript:;" onclick="newActImg()"><i class="icon-plus-sign"></i>新增广告图</a>
</div>

<div class="add-show-div btn-imgadd-div" style="display: none;">
    <div class="bsd-close"></div>
    <div class="btn-skuadd-box">
          <h3>添加图片<a class="bsb-close" href="javascript:;">×</a></h3>
          <ul class="ncsc-store-slider">                        
            <li class="act_imgaes_li">
              <input type="hidden" id="add_image_path" name="image_path" data-imgurl="" value="">
              <div class="picture"><i class="icon-picture"></i></div>
              <div class="ncsc-upload-btn">
                <a href="javascript:void(0);">
                  <span><input type="file" name="adv_image" onchange="imageAdvListShow(this)" hidefocus="true" size="1" class="input-file" id="adv_image"></span>
                  <p><i class="icon-upload-alt"></i>图片上传</p>
                </a>
              </div>
              <div class="url">
                <label>跳转路径：</label>
                <input type="text"  id="add_link_path" placeholder="请输入图片跳转路径" class="text w400" value="">
              </div>
            </li>
          </ul>
        <div class="bottom bsd-bottom">
          <label class="submit-border"><input id="" onclick="newActImgAdd()" type="button" class="submit" value="提交"></label>
        </div>
    </div>
</div>
<script type="text/html" id="imgListHtml">
  <li class="act_imgaes_li">
    <a href="javascript:;" class="img-remove" title="移除">X</a>
    <div class="picture">
      <img src="{{imgPath}}">
      <input class="adv_image_value" type="hidden" name="adv_image[]" value="{{imgName}}">
    </div>
    <div class="url">
      <label>跳转路径：</label>
      <input type="text" placeholder="请输入图片跳转路径" class="text w150"  name="link_path[]" value="{{linkPath}}">
    </div>
  </li>
</script>

                </dd>
            </dl>
        </dl>


	      <?php if ($output['lottery_info']['lottery_state'] == 2){?>
              <dl>
                  <dt><?php echo '开奖结果'.$lang['nc_colon']; ?></dt>
                  <?php if($output['lottery_info']['member_id']){?>
                  <dd>
                      <span><label for="adv_title" ><?php echo $output['lottery_info']['member_name'].'|手机号：'. $output['lottery_info']['member_mobile']?></label></span>
                  </dd>
                  <?php }?>
              </dl>
	      <dl>
	      	<dt><i class="required">*</i><em class="pngFix"></em><?php echo '开奖号码'.$lang['nc_colon']; ?></dt>
	      	<dd>
                <input type="text" class="w300 text" name="lottery_number" id="lottery_number" value="" maxlength=50 />
                <a id="btn_lottery_number" class="layui-btn  layui-btn-danger layui-btn-sm" href="javascript:;"><i></i>搜索获奖用户</a>
	      	</dd>
	    </dl>
              <dl>
                  <dt><em class="pngFix"></em><?php echo '获奖用户'.$lang['nc_colon']; ?></dt>
                  <dd>
                      <input type="hidden" class="w300 text" name="lottery_member_id" id="lottery_member_id" value="" maxlength=50 />
                      <span id="member_info"></span>

                  </dd>
              </dl>
	    <?php }?>
	    <div class="bottom">
	      <label class="submit-border">
	      <a id='btn_add' class="submit" href="javascript:void(0);"><?php echo $lang['nc_submit'];?></a>
	      </label>
	      </div>
	  </form>
	</div>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/jquery-ui/i18n/zh-CN.js"></script>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/common_select.js"></script>
<script type="text/javascript" src="<?php echo RESOURCE_SITE_URL;?>/js/fileupload/jquery.iframe-transport.js" charset="utf-8"></script>
<script type="text/javascript" src="<?php echo RESOURCE_SITE_URL;?>/js/fileupload/jquery.ui.widget.js" charset="utf-8"></script>

<script type="text/javascript" src="<?php echo RESOURCE_SITE_URL;?>/js/fileupload/jquery.fileupload.js" charset="utf-8"></script>

<script>

    var layer;
function imageShow(){
    var file = document.getElementById("draw_image_url");

    for(var i=0;i<file.files.length;i++){
        let reader = new FileReader();
        var file1 = file.files[i];
        reader.readAsDataURL (file1);
        reader.onload = function (result) {
            //reader对象的result属性存储流读取的数据

            $('#customimg_preview').show();
            $('#customimg_preview').children('p').html('<img src="'+reader.result+'">');
        }
    }
}
function imageLiveShow(){
    var file = document.getElementById("live_image_url");

    for(var i=0;i<file.files.length;i++){
        let reader = new FileReader();
        var file1 = file.files[i];
        reader.readAsDataURL (file1);
        reader.onload = function (result) {
            //reader对象的result属性存储流读取的数据

            $('#live_preview').show();
            $('#live_preview').children('p').html('<img src="'+reader.result+'">');
        }
    }
}
function imageAdvShow(){
    var file = document.getElementById("adv_image_url");

    for(var i=0;i<file.files.length;i++){
        let reader = new FileReader();
        var file1 = file.files[i];
        reader.readAsDataURL (file1);
        reader.onload = function (result) {
            //reader对象的result属性存储流读取的数据

            $('#adv_preview').show();
            $('#adv_preview').children('p').html('<img src="'+reader.result+'">');
        }
    }
}
function imageLoginShow(){
    var file = document.getElementById("login_image_url");

    for(var i=0;i<file.files.length;i++){
        let reader = new FileReader();
        var file1 = file.files[i];
        reader.readAsDataURL (file1);
        reader.onload = function (result) {
            //reader对象的result属性存储流读取的数据

            $('#login_preview').show();
            $('#login_preview').children('p').html('<img src="'+reader.result+'">');
        }
    }
}
function imageShShow(){
    var file = document.getElementById("s_image_url");

    for(var i=0;i<file.files.length;i++){
        let reader = new FileReader();
        var file1 = file.files[i];
        reader.readAsDataURL (file1);
        reader.onload = function (result) {
            //reader对象的result属性存储流读取的数据

            $('#s_preview').show();
            $('#s_preview').children('p').html('<img src="'+reader.result+'">');
        }
    }
}
//图片列表上传
function newActImg(){
  $(".btn-imgadd-div").show();
}

function addDivHide(){
  $(".btn-imgadd-div").hide();
  $("#add_image_path,#add_link_path").val('').removeAttr("data-imgurl");

  $(".btn-imgadd-div .picture").html('<i class="icon-picture"></i>')
}
$(document).on('click','.btn-imgadd-div .bsd-close,.btn-imgadd-div .bsb-close',function () {
  addDivHide();
})

function newActImgAdd(){
    if($("#add_image_path").val() == '') {
        showError('请上传图片');
        return false;
    }

  var imgPath = $("#add_image_path").attr("data-imgurl") || ''

  var imgName = $("#add_image_path").val()
  var linkPath = $("#add_link_path").val()
  var imgListHtml = $("#imgListHtml").html().replace('{{imgPath}}',imgPath).replace('{{imgName}}',imgName).replace('{{linkPath}}',linkPath)
  $(".act-imglist .ncsc-store-slider").append(imgListHtml);
  addDivHide();
}
$(document).on('click','.act_imgaes_li .picture',function () {
  $(this).parent().find(".input-file").click();
})
$(document).on('click','.act_imgaes_li .img-remove',function () {
  $(this).parent(".act_imgaes_li").remove();
})
function imageAdvListShow(e){
    var file = e;
    for(var i=0;i<file.files.length;i++){
        let reader = new FileReader();
        var file1 = file.files[i];
        reader.readAsDataURL (file1);
        reader.onload = function (result) {
            $(e).parents(".act_imgaes_li").children('.picture').html('<img src="'+reader.result+'">');
        }
    }
}


function imageShareShow(){
    var file = document.getElementById("share_image_url");

    for(var i=0;i<file.files.length;i++){
        let reader = new FileReader();
        var file1 = file.files[i];
        reader.readAsDataURL (file1);
        reader.onload = function (result) {
            //reader对象的result属性存储流读取的数据

            $('#share_preview').show();
            $('#share_preview').children('p').html('<img src="'+reader.result+'">');
        }
    }
}
$(document).ready(function(){
    var lottery_id = '<?php echo $output['lottery_info']['id']?>';
        //图片上传
        $('#adv_image').on('click',function(){
            $(this).fileupload({
                dataType: 'json',
                url: '<?php echo urlShop('store_lottery','uploadImage')?>',
                formData: {lottery_id: lottery_id},
                add: function(e, data) {

                    data.submit();
                },
                pasteZone: null,
                done: function (e, data) {
                    var result = data.result;
                    if(typeof result.error === 'undefined') {
                        $("#add_image_path").val(data.result.image_name).attr("data-imgurl",data.result.image_url);
                        $(".btn-imgadd-div .act_imgaes_li .picture").html('<img src="'+data.result.image_url+'">')
                    } else {

                        showError(result.error);
                    }
                }
            });
        });




    layui.use('layer',function(){

        layer = layui.layer;
    });
   /* //日期控件
    $('#lottery_start_time').datepicker({
        controlType: 'select'
    });
    $('#txt_template_startdate').datepicker();*/

   /* $('#lottery_start_time').datetimepicker({
        controlType: 'select'
    });

    $('#lottery_end_time').datetimepicker({
        controlType: 'select'
    });
*/

    $('#lottery_start_time').datetimepicker({
        controlType: 'select',
        /*onSelect: function( startDate ) {
            var $startDate = $( "#lottery_start_time" );
            var $endDate = $('#lottery_end_time');
            var endDate = $endDate.datepicker( 'getDate' );
            if(endDate < startDate){
                $endDate.datepicker('setDate', startDate - 3600*1000*24);
            }
            //$endDate.datepicker( "option", "minDate", startDate );
        }*/
    });
    $('#lottery_end_time').datetimepicker({
        controlType: 'select',
        /*onSelect: function( endDate ) {
            var $startDate = $( "#lottery_start_time" );
            var $endDate = $('#lottery_end_time');
            var startDate = $startDate.datepicker( "getDate" );
            if(endDate < startDate){
                $startDate.datepicker('setDate', startDate + 3600*1000*24);
            }
            //$startDate.datepicker( "option", "maxDate", endDate );
        }*/
    });
    var currDate = new Date();
    var date = currDate.getDate();
    console.log(date);
    date = date ;
    currDate.setDate(date);
    $('#lottery_start_time').datepicker( "option", "minDate", currDate);
    $('#lottery_end_time').datepicker( "option", "minDate", currDate);

    $('#lottery_start_time').val("<?php echo $output['lottery_info']['lottery_start_time']?@date('Y-m-d H:i',$output['lottery_info']['lottery_start_time']):@date('Y-m-d H:i');?>");

    $('#lottery_end_time').val("<?php echo $output['lottery_info']['lottery_end_time']?@date('Y-m-d H:i',$output['lottery_info']['lottery_end_time']):'';?>");
    $("#btn_add").click(function(){
        var adv_image = $('.adv_image_value').val();
        console.log(adv_image);
        if(adv_image === undefined){
            layer.msg('广告图片必须有一张',{icon:5});
            return false
        }

        if($("#add_form").valid()){
        	ajaxpost('add_form', '', '', 'onerror');
    	}
	});

    //表单验证
    $('#add_form').validate({
        errorPlacement: function(error, element){

	    	var error_td = element.parent('dd').children('span');
           if(element.attr('type')=='file'){
               error_td = element.parents('dd').children('span')
           }
			error_td.append(error);
	    },
        rules : {
            adv_title: {
                required : true,
                stringMaxLength:['6']
            },
            live_mini_path: {
                required : true
            },
            <?php if($output['type']=='add'){?>
            live_image_url: {
                required : true
            },
            draw_image_url: {
                required : true,

            },
            <?php }?>
            lottery_end_time: {
                required : true,
                greaterThanStartDate : true

            },
            lottery_start_time: {
                required : true,

            },
            adv_path: {
				required : true
			},
            <?php if($output['type']=='add'){?>
                          login_image_url: {
                              required : true
                          },
                          s_image_url: {
                              required : true
                          },
            <?php }?>

        },
        messages : {

            live_mini_path: {
                required : '<i class="icon-exclamation-sign"></i>直播跳转路径必填'
            },

            draw_image_url: {
                required : '<i class="icon-exclamation-sign"></i>活动图片未上传'
            },
            lottery_end_time: {
				required : '<i class="icon-exclamation-sign"></i>结束时间必填',
                <?php if ($output['type']=='edit') { ?>
                lessThanDate:'<i class="icon-exclamation-sign"></i>结束时间必须大于开始时间',
            <?php }?>
                greaterThanStartDate:'<i class="icon-exclamation-sign"></i>结束时间必须大于开始时间',
            },
            lottery_start_time: {
				required : '<i class="icon-exclamation-sign"></i>开始时间必填',

			},
            adv_path: {
                required : '<i class="icon-exclamation-sign"></i>广告跳转路径必填'
            },
                         login_image_url: {
                             required : '<i class="icon-exclamation-sign"></i>登录上方图片未上传'
                         },
                         s_image_url: {
                             required : '<i class="icon-exclamation-sign"></i>分享海报图片未上传'
                         }
        }
    });
    jQuery.validator.methods.greaterThanDate = function(value, element, param) {
        var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
        var date2 = new Date(Date.parse(value.replace(/-/g, "/")));
        return date1 < date2;
    };
    jQuery.validator.methods.lessThanDate = function(value, element, param) {
        var date1 = new Date(Date.parse(param.replace(/-/g, "/")));
        var date2 = new Date(Date.parse(value.replace(/-/g, "/")));

        return date1 > date2;
    };
    jQuery.validator.methods.greaterThanStartDate = function(value, element) {
        var start_date = $("#lottery_start_time").val();
        var date1 = new Date(Date.parse(start_date.replace(/-/g, "/")));
        var date2 = new Date(Date.parse(value.replace(/-/g, "/")));

        return date1 < date2;
    };

    $.validator.addMethod("stringMaxLength",function(value,element,params){

        var length = value.length;

        for( var i = 0; i < value.length; i++ ) {
            if( value.charCodeAt(i) > 19967 ) {
                length++;
            }
        }

        return length>params[0]*2?false:true;
    },"最大长度不能超过{0}个字符")

    $('#btn_lottery_number').on('click',function(){

        var lottery_number = $('#lottery_number').val();
        if(lottery_number == ''){
            layer.msg('获奖码不能为空',{icon:5});
        }
        var lottery_id = $('#lottery_id').val();
        var ajaxurl = 'index.php?act=store_lottery&op=getLotteryMember';
        $.ajax({
            type: "POST",
            url: ajaxurl,
            data:{lottery_id:lottery_id,lottery_number:lottery_number},
            async: false,
            success: function(data){
                data = eval('('+data+')');

                if(data.status == 200) {
                    var html ='<label for="adv_title" >'+data.member_name+'|手机号:'+data.member_mobile+'</label>';
                  $('#member_info').html(html);
                  $('#lottery_member_id').val(data.member_id);
                }else{
                    layer.msg(data.msg,{icon:5});
                }
            }
        });

    });

});


/**
 *
 * @param common_id
 */
function change_commis(common_id){
    var obj = $("#"+common_id);
    var num = parseInt($.trim(obj.val()));
    if (num>0 && num <= 30) {
        var ajaxurl = 'index.php?act=store_lottery&op=getLotteryMember';
        $.ajax({
            type: "POST",
            url: ajaxurl,
            data:{},
            async: false,
            success: function(state){
                if(state == '1') {
                    obj.val(num);
                    showDialog('保存成功','succ','','','','','','','',2);
                }
            }
        });
    }
}



</script>