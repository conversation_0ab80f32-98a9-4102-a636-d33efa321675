@charset "utf-8";
/* CSS Document */

.saleP { font-weight: 600; color: #F32613; vertical-align: middle;}/*价格*/



/* =========================== */
/* 商品详情购买页面 -> goods.php */
/* =========================== */

/* 商品信息购买 */
.ncs-detail { min-height: 460px; margin-bottom: 20px; border: solid 1px #EEE; position: relative; z-index: 2;}
.ncs-detail .ncs-info { display: block;}
.ncs-lal { width: 210px; display: block;}
.ncs-lal .title { font-size: 12px; font-weight: 600; line-height: 20px; background-color: #f5f5f5; display: block; padding: 5px 10px; border-bottom: solid 1px #E6E6E6;}
.ownshop .ncs-lal .title { margin: 1px;}
.ncs-lal .content { width: 190px; margin: 0 auto;}
.ncs-lal .content ul { font-size: 0; word-spacing:-1em;}
.ownshop .ncs-lal .content ul { padding-top: 10px;}
.ncs-lal .content ul li { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; width: 95px; *zoom: 1;}
.ncs-lal .content ul li .goods-pic { width: 60px; height: 60px; margin: 5px auto;}
.ownshop .ncs-lal .content ul li .goods-pic { margin: 10px auto;}
.ncs-lal .content ul li .goods-pic a { background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 60px; height: 60px; border: 1px solid transparent; overflow: hidden;}
.ncs-lal .content ul li .goods-pic a:hover { border-color: #F32613;}
.ncs-lal .content ul li .goods-pic a img { max-width: 60px; max-height: 60px; margin-top:expression(60-this.height/2); *margin-top:expression(30-this.height/2);}
.ncs-lal .content ul li .goods-price { font-weight: 600; text-align: center; color: #F32613;}

/* 商品图片放大镜 */
.ncs-goods-picture { background-color: #FFF; position: absolute; z-index:99; top: 0; left: 0;}
.ncs-goods-picture .gallery_wrap { width: 100%; height: 100%; position: absolute; top: 0; left: 0;} 
.ncs-goods-picture .gallery { width: 100%; height: 100%; position: absolute; top: 0; left: 0;}
.ncs-goods-picture .place_gallery { margin-left: -180px; top: 4%; left: 50%;}
.ncs-goods-picture img { display: block; -ms-interpolation-mode: bicubic;}
.ncs-goods-picture .levelB, 
.ncs-goods-picture .levelC { cursor: url(../images/shop/zoom.png), -moz-zoom-in; }
.ncs-goods-picture .levelD { cursor: url(../images/shop/hand.png), -moz-grab; }
.ncs-goods-picture .controller_wrap { filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#B2FFFFFF', endColorstr='#B2FFFFFF'); background:rgba(255,255,255,0.7); padding: 0; position: absolute; left: 12px; right: 20px; bottom: -42px;}
.ncs-goods-picture .controller { width: 350px !important; height: 52px; position: relative; overflow: hidden;}
.ncs-goods-picture .controller ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; width: 100%; height: 52px; overflow: hidden; position: absolute; top: 0; left: 0;}
.ncs-goods-picture .controller li { vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; width: 58px;}
.ncs-goods-picture .controller li { *display: inline/*IE6,7*/;}
.ncs-goods-picture .controller a { background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; padding: 1px; border: 1px solid #EEE; overflow: hidden;}
.ncs-goods-picture .controller a img { max-width: 48px; max-height: 48px; margin-top:expression(48-this.height/2); *margin-top:expression(24-this.height/2);}
.ncs-goods-picture .controller a:hover, .ncs-goods-picture .controller .current { padding: 0; border-width: 2px; border-color: #F32613;}
.ncs-goods-picture .prev, .ncs-goods-picture .next { filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#66FFFFFF', endColorstr='#66FFFFFF');background:rgba(255,255,255,0.4); display: none; border-radius: 0 56px 56px 0; width: 28px; height: 56px; position: absolute; top: -190px; left: 0;}
.ncs-goods-picture:hover .prev, .ncs-goods-picture:hover .next { display: block;}
.ncs-goods-picture .prev:hover, .ncs-goods-picture .next:hover { filter:progid:DXImageTransform.Microsoft.gradient(enabled='true',startColorstr='#CCFFFFFF', endColorstr='#CCFFFFFF');background:rgba(255,255,255,0.8);}
.ncs-goods-picture .prev span, .ncs-goods-picture .next span { text-indent: -99px; background: url(../images/shop/2014_ncs_public_img.png) no-repeat -12px 0; display: block; margin: 18px 0 0 4px; width: 10px; height: 20px; overflow: hidden; cursor: pointer;}
.ncs-goods-picture .hide, .ncs-goods-picture:hover .hide { display: none;}
.ncs-goods-picture .next { border-radius: 56px 0 0 56px; left: auto; right: 0;}
.ncs-goods-picture .next span { background-position: -22px 0; margin-left: 9px;}
.ncs-goods-picture .close_wrap { position: absolute; top: -10px; right: -10px;}
.ncs-goods-picture .close_wrap a { font: lighter 20px/20px Verdana; color: #CCC; background-color: #FFF; text-align: center; display: none; width: 22px; height: 22px; border: solid 1px #CCC; border-radius: 24px; overflow: hidden;}
.ncs-goods-picture .close_wrap a:hover { text-decoration: none; color: #333; border-color: #333;}

.ncs-goods-picture .controller a video{max-width:48px;max-heigth:48px;}
.video-play{ font-size:0.6rem;position: absolute;top: 1.2rem;left: 1.2rem;color: #fff; background: #f40;padding:0.3rem 0.15rem 0.1rem 0.25rem;border-radius: 50%; width: 1rem;height: 1rem;}
.movie-lock{width:48px; height:48px; overflow:hidden !important;display:inline-block !important;}
/* 商品名称 */

/* product_read */
.ncs-goods-summary { background-color: #FFF; float: right; width: 622px; min-height: 460px; margin-right: 208px; border-right: solid 1px #EEE; border-left: solid 1px #EEE;}

/* hx修改*/
.ncs-goods-summary .name { 
  padding: 15px 20px;  
  position: relative;
  margin-bottom: 21px;
}
.ncs-goods-summary .name:after {
  position: absolute;
  height: 32px;
  width: 551px;
  content: '';
  background: url(http://www.upetmart.com/templates/default/images/shopCategory/shopbanner.jpg)0 0 no-repeat;
  top: 54px;
}
/* hx修改*/


.ncs-goods-summary .name h1:before{content:"自营"; display:block; float:left; font-size:12px; font-weight:400; font-family:宋体; background:#f30; padding:0px 6px; margin-top:2px; border-radius:3px; line-height:18px; margin-right:6px; color:#fff;}
.ncs-goods-summary .name h1, .ncs-goods-summary .name strong { font: 700 16px/21px "Microsoft Yahei"; color: #333; text-overflow: ellipsis; white-space: nowrap; display: block; overflow: hidden;} 
.ncs-goods-summary .name strong { font-weight: normal; font-size: 12px; line-height: 17px; color: #777; white-space: normal; margin-top: 5px;}
/*.ownshop .ncs-goods-summary { width: 830px; margin-right: 0; border: none;}*/
/* 销售信息 */
.ncs-meta { color: #FFF; background: url(../images/shop/goods_meta_bg.png) no-repeat 0 50%; background-size:cover; padding: 15px 0; position: relative; z-index: 3;/* box-shadow: inset 4px 0 1px rgba(153,153,153,0.15);*/}
.ncs-sale { background-color: #FAFAFA; border-bottom: solid 1px #E6E6E6; position: relative; z-index: 3;}
.ncs-sale .goods-gift { max-height: 120px; overflow: hidden; position: relative; z-index: 1;}
.ncs-sale .goods-gift ul {}
.ncs-sale .goods-gift ul li { font-size: 0; *word-spacing:-1px/*IE6、7*/; margin-bottom: 4px;}
.ncs-sale .goods-gift .goods-gift-thumb, 
.ncs-sale .goods-gift .goods-gift-name,
.ncs-sale .goods-gift ul li em { font-size: 12px; vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline; *zoom: 1;}
.ncs-sale .goods-gift .goods-gift-thumb {}
.ncs-sale .goods-gift .goods-gift-thumb span { background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 24px; height: 24px; padding: 1px; border: 1px solid #EEE; overflow: hidden;}
.ncs-sale .goods-gift .goods-gift-thumb span img { max-width: 24px; max-height: 24px; margin-top:expression(24-this.height/2); *margin-top:expression(12-this.height/2);}
.ncs-sale .goods-gift .goods-gift-name { color: #005EA6; margin-left: 5px;}
.ncs-sale .goods-gift ul li em { font-family: Arial; color: #F60; margin-left: 5px;}
.ncs-plus { padding: 0;}

.ncs-key { position: relative; z-index: 1; }
.ncs-goods-summary hr { font-size: 0; background-color: transparent; line-height: 0; width: 90%; margin: 0 auto; border: none; border-top: dotted 1px #E6E6E6;}
.ncs-goods-summary dl { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.ncs-goods-summary dl dt{ color: #777; }
.ncs-goods-summary dl dt, 
.ncs-goods-summary dl dd { font-size: 12px; line-height: 24px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/* IE6,7 */; min-height: 24px; padding: 6px 0 0; *zoom:1;}
.ncs-meta dl dt,
.ncs-meta dl dd { line-height: 30px; height: 30px;}
.ncs-meta dl.rate dd{ color:#777;  }
.ncs-sale dl dt,
.ncs-sale dl dd { padding: 10px 0;}
.ncs-goods-summary dt { text-align: left; width: 70px; margin-left: 17px;}
.ncs-goods-summary dd { text-align: left; width: 510px; *width: 509px/*IE7*/;}
.ncs-meta dl dd i.ver-line { display: inline-block; *display: inline; zoom: 1;}
.ncs-meta dl dd a { color: #777; text-decoration: underline; /*vertical-align: middle;*/ display: inline-block; *display: inline/*IE7*/; *zoom: 1;}
.ncs-meta .price strong  { font: 700 30px/25px Tahoma; vertical-align: middle;/* text-shadow: 0 1px 0 #6C0900;*/ color:#ff5300;}
.ncs-meta .price strong i { font-family: Arial; font-size: 18px;}
.ncs-meta .price em { color: #333; vertical-align: middle; margin-left: 8px;}
.ncs-meta .cost-price strong  { text-decoration: line-through; color:#a1a1a1; font-weight:normal;}





.ncs-meta02{  border-top: 1px solid #ff7300; background-color: #fff5ec; padding: 15px 0;  }
.ncs-meta02 .price-title, .ncs-meta02 .price-list {display: inline-block;}
.ncs-meta02 .price-title {width: 76px;}
.ncs-meta02 dl dt{ color:#a5988f; text-align:left; margin-left:17px; margin-right:0px;  }
.ncs-meta02 dl dt,
.ncs-meta02 dl dd { line-height: 30px; height: 30px;}
.ncs-sale02 dl dt,
.ncs-sale02 dl dd { padding: 10px 0;}
.ncs-meta02 dl dd i.ver-line { display: inline-block; *display: inline; zoom: 1;}
.ncs-meta02 dl dd a { color: #444; text-decoration: underline; vertical-align: middle; display: inline-block; *display: inline/*IE7*/; *zoom: 1;}
.ncs-meta02 .price strong  { font: 700 30px/25px Tahoma; vertical-align: middle; text-shadow: 0 1px 0 #6C0900;}
.ncs-meta02 .price strong i { font-family: Arial; font-size: 18px;}
.ncs-meta02 .price em { color: #FFFF00; vertical-align: middle; margin-left: 8px;}
.ncs-meta02 .cost-price strong  { text-decoration: line-through;}


.ncs-meta02 .price-title{ width: 76px; }
.ncs-meta02 .price-title,.ncs-meta02 .price-list{ display: inline-block; }
.ncs-meta02 .price-list{ width: 540px; }
.ncs-meta02 .price-list div.price-info{ width: 108px; display: inline-block;}
.ncs-meta02 .price-list div.price-info dt{ text-align: left; width:108px;color:#444444;}
.ncs-meta02 .price-list div.price-info span.fd{
    font-size: 16px;  color: #ff7300;
}
.ncs-meta02 .price-list div.price-info span.F-key {
    font-size:16px; color: #ff7300;
}


/* 商品二维码 */
.ncs-goods-code { width: 100px; height: 130px; position: absolute; z-index: 1; top:35px; right:-1px; background:#FDFDFD; display:none; border: 1px solid #eee; padding:8px;}
.ncs-goods-code p { vertical-align: middle; text-align: center; display: table-cell; *display: block; width: 100px; height:100px; padding: 0; overflow: hidden;}
.ncs-goods-code img { max-width: 100px; max-height: 100px; margin-top:expression(100-this.height/2); *margin-top:expression(50-this.height/2);}
.ncs-goodscode-box{  background: url(../images/shop/goods_coda.png) no-repeat 115px 6px; width:160px; font-size:12px;height:36px;line-height:36px;float: right;  position:relative; }
.ncs-goodscode-box a{ 	border-right:none;color: #989898;padding: 0 50px 0 0;  display: block; text-align: right;height: 100%; cursor:pointer;}
.ncs-goodscode-box a:hover{ text-decoration:none;color: #989898;}

.ncs-goods-code-note{ line-height:20px;}
/*预售定金*/
.ncs-book-down { font-size: 0; *word-spacing:-1px/*IE6、7*/; width: 360px; margin: 5px auto 5px 100px; position: relative; z-index: 1;}
.ncs-book-down .rule-price,
.ncs-book-down .rule-symbol { font-size: 12px; text-align: center; vertical-align: middle; display: inline-block; *display: inline/*IE6,7*/; *zoom:1;}
.ncs-book-down .rule-price { color: #F32613; line-height: 20px; background-color: #FFF; min-width: 60px; height: 40px; padding: 4px 9px; border: dashed 1px #C92000;}
.ncs-book-down .rule-price strong { font-weight: 600; display: block; color: #000;}
.ncs-book-down .rule-symbol { font-size: 16px; margin: 0 10px;}
.ncs-book-down .rule-time { font-size: 12px; display: block; clear: both; padding: 5px 0 0 0;}
.ncs-book-down .rule-info { font-size: 12px; display: block; position: absolute; z-index: 1; top: 0; right: 0;}
.ncs-book-down .rule-info a { color: #FFF; display: block; position: relative; z-index: 1; cursor: pointer;}
.ncs-book-down .rule-info a:hover { color: #F32613; text-decoration: none;}
.ncs-book-down .rule-info a i { font-size: 16px; vertical-align: middle; margin-left: 6px; color: #6DD900;}
.ncs-book-down .rule-info a ul { background-color: #FFF; display: none; width: 300px; padding: 0 10px 10px; position: absolute; z-index: 1; top: 25px; left: -100px; box-shadow: 4px 4px 0 #F5F5F5;}
.ncs-book-down .rule-info a:hover ul { display: block;}
.ncs-book-down .rule-info a ul .arrow { font-size: 0;  line-height: 0;  display: block; width: 0; height: 0; margin-left: 95px; margin-top: -15px; border-color: transparent transparent #FFFFFF transparent; border-style: dashed dashed solid dashed; border-width: 8px;}
.ncs-book-down .rule-info a ul li { color: #777; line-height: 16px; padding: 10px 0 0;}



.ncs-sale .promotion-info { font-size: 0; word-spacing:-1em; position: relative; z-index: 4;}
.ncs-sale .promotion-info span { font-size: 12px; vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; *zoom:1;}
.ncs-sale .promotion-info .sale-name { line-height: 16px; color: #FFF; background-color: #FF875A; height: 16px; padding: 2px 5px; margin-right: 10px;}
.ncs-sale .promotion-info .sale-rule { color: #555; width: 345px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}
.ncs-sale .promotion-info .sale-rule em { font-family: Arial; font-weight: 600; color: #333; margin: 0 1px;}
.ncs-sale .promotion-info .sale-rule a.gift { background-color: #FFF; text-align: center; vertical-align: middle; display: inline-block; width: 20px; height: 20px; padding: 1px; overflow: hidden;}
.ncs-sale .promotion-info .sale-rule a.gift img { max-width: 20px; max-height: 20x;}
.ncs-sale .promotion-info .sale-rule-more {}
.ncs-sale .promotion-info .sale-rule-more i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -50px -100px; vertical-align: middle; display: inline-block; width: 7px; height: 4px; margin-left: 4px;}
.ncs-sale .promotion-info .sale-rule-more strong { font-weight: normal; color: #FF875A; margin: 0 2px;}
.ncs-sale .promotion-info .sale-rule-more a { color: #999;}
.ncs-sale .promotion-info .sale-rule-more a:hover { color: #FF875A; text-decoration: none;}
.sale-rule-content { font-size: 12px; background-color: #FFF; width: 518px; border: solid 1px #D7D7D7; position: absolute; z-index: 1; top: 5px; left: -5px; box-shadow: 4px 4px 0 rgba(0,0,0,0.05);}
.sale-rule-content .title { line-height: 20px; background-color: #F5F5F5; height: 20px; padding: 5px; border-bottom: solid 1px #D6D6D6;}
.sale-rule-content .title span { vertical-align: top;}
.sale-rule-content .title strong { font-weight: normal; color: #FF875A; margin: 0 2px;}
.sale-rule-content .title a { float: right;}
.sale-rule-content .content { padding: 5px 0 5px 60px;}
.sale-rule-content .bottom { border-top: solid 1px #D7D7D7; padding: 8px 0; overflow: hidden;}
.sale-rule-content .bottom a { color: #005EA6; text-decoration: none; float: right; margin-right: 10px;}
.sale-rule-content .mjs-tit { font-weight: 600;}
.sale-rule-content .mjs-tit time { font-weight: normal; color: #999;}
.sale-rule-content .mjs-info {}
.sale-rule-content .mjs-info li { line-height: 24px; display: block; height: 24px; margin-bottom: 2px;}
.ncs-mansong, .ncs-jjg { padding: 2px 0;}

/*加价购规则详情*/
.cou-rule-list { background-color: #FFF; padding: 5px 0 5px 60px ;}
.couRuleScrollbar { position: relative; max-height: 180px; padding-right: 10px; overflow: hidden;}
.cou-rule { display: block; padding: 0 0 5px 0; border-bottom: dashed 1px #D6D6D6; margin-bottom: 5px;}
.cou-rule h4 { color: #000; line-height: 20px; margin-bottom: 5px;}
.cou-rule ul {}
.cou-rule ul li { display: block; width: 100%; height: 20px; padding: 2px 0;}
.cou-rule ul li img { width: 20px; height: 20px; float: left; margin-right: 5px;}
.cou-rule ul li h5 { color: #777; white-space: nowrap; text-overflow: ellipsis; width: 300px; height: 20px; float: left; overflow: hidden;}
.cou-rule ul li h6 { color: #690; display: block; width: 80px; text-align: right; float: right;}

/* 物流运费 */
.ncs-freight { padding: 6px 0;}
.ncs-freight dt { line-height: 28px;}
.ncs-freight_box { display: block; position: relative; z-index: 80;}
.ncs-freight-select { height: 28px; float: left; margin-right: 6px; position: relative; z-index: 3;}
.ncs-freight-select .text { line-height: 26px; background-color: #FFF; height: 26px; float: left; padding: 0 20px 0 15px; border: solid 1px #E6E6E6; position: relative; z-index: 1; overflow: hidden; cursor: pointer;}
.ncs-freight-select.hover .text { display: none;}
.ncs-freight-select .text b { font-size: 0; line-height: 0; background: url(../images/shop/2014_ncs_public_img.png) no-repeat -50px -100px; display: block; width: 8px; height: 4px; position: absolute; top: 10px; right: 6px; overflow: hidden;}
.ncs-freight-select.hover .close, 
.ncs-freight-select.hover .content { display: block;}
.ncs-freight-select .content { background-color: #FFF; display: none; width: 512px; padding: 0; border: 1px solid #D7D7D7; position: absolute; z-index: 2; top: 0; left: 0; box-shadow: 4px 4px 0 rgba(0,0,0,0.05);}
.ncs-freight-select .ncs-stock { position: relative;}
.ncs-freight-select .ncs-stock .tab { background-color: #FAFAFA; width: 100%; height: 26px; float: left; border-bottom: solid 1px #E6E6E6; overflow: visible;}
.ncs-freight-select .ncs-stock .tab li { float: left; clear: none; padding: 0;}
.ncs-freight-select .ncs-stock .tab .current a.hover, 
.ncs-freight-select .ncs-stock .tab a { font-size: 12px; line-height: 26px; color: #999; text-align: center; float: left; height: 26px; padding: 0 15px 0 15px; border-style: solid; border-width: 0 1px 0 0; border-color: #E6E6E6; position: relative; cursor: pointer; -moz-border-colors: none;}
.ncs-freight-select .ncs-stock .tab a.hover { line-height: 26px; color: #000; text-decoration: none; background-color: #FFF; height: 26px; padding: 0 15px 1px 15px; border-color: #E6E6E6; border-style: solid; border-width: 0 1px 0 0;}
.ncs-freight-select .ncs-stock .tab a i { font-size: 0; line-height: 0; background: url(../images/shop/2014_ncs_public_img.png) no-repeat -50px -100px; vertical-align: middle; display: inline-block; width: 8px; height: 4px; margin-left: 6px;}
.ncs-freight-select .ncs-stock .area-list { display: block; clear: both; padding: 10px 15px; overflow: hidden;}
.ncs-freight-select .ncs-stock .area-list li { line-height: 20px; white-space: nowrap; text-overflow: ellipsis; width: 112px; height: 20px; padding: 4px 0 4px 4px; float:left; overflow: hidden;}
.ncs-freight-select .ncs-stock .area-list li.longer-area { width: 228px;}
.ncs-freight-select .ncs-stock .area-list li a { line-height: 16px; color: #555; padding: 2px 5px;}
.ncs-freight-select .ncs-stock .area-list li a:hover { color: #FFF; text-decoration: none; background-color: #FF875A;}
.ncs-freight-select .close { font-size: 12px; line-height: 20px; display: none; width: 24px; height: 20px; position: absolute; z-index: 2; top: 4px; left: 480px; cursor: pointer;}
#ncs-freight-prompt { line-height: 28px; color: #999; float: left;}
#ncs-freight-prompt strong { font-size: 16px; color: #333; margin: 0 8px;}
#ncs-freight-prompt a { }

/* 门店自提 */
.ncs-logistics { position: relative; z-index: 2; }
.ncs-logistics .ncs-chain { padding: 6px 0;}
.ncs-logistics .ncs-chain i.icon-chain { background: url(../images/shop/2014_ncs_public_img.png) no-repeat 0 -100px; vertical-align: middle; display: inline-block; *display: inline; width: 20px; height: 20px; padding-right: 5px; *zoom: 1;}
.ncs-logistics .ncs-chain dd { color: #999;}
.ncs-logistics .ncs-chain a { font-size: 14px; color: #BA7538; margin-right: 5px;}
.ncs-chain-show { display: block; padding: 10px;}
.ncs-chain-show dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; margin-bottom: 10px;}
.ncs-chain-show dt, 
.ncs-chain-show dd { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/* IE6,7 */; *zoom:1;}
.ncs-chain-show dt { font-size: 14px; line-height: 28px; margin-right: 10px;}
.ncs-chain-show dd select { margin-right: 4px;}
.ncs-chain-list { background: #F5F5F5 url(../images/shop/ncs_chain_show.png) no-repeat 50% 40%; min-height: 300px; border: solid 1px #E6E6E6;}
.ncs-chain-no-date { font-size: 16px; font-weight: 600; text-align: center; margin: 180px auto 0 auto;}
.ncs-chain-list ul { padding: 10px;}
.ncs-chain-list ul li { line-height: 20px; background-color: #FFF; padding: 5px 5px 5px 15px; margin-bottom: 10px; border: solid 1px #E6E6E6; border-radius: 5px;}
.ncs-chain-list ul li a { color: #2272c8;}
.ncs-chain-list ul li a:hover { text-decoration: underline;}
.ncs-chain-list ul li h5 i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -30px -100px; vertical-align: middle; display: inline-block; *display: inline; width: 10px; height: 12px; margin-right: 5px; *zoom: 1;}
.ncs-chain-list ul li p { color: #999;}
.ncs-chain-list ul li .handle { line-height: 20px; height: 20px; float: right; padding: 10px; border-left: solid 1px #E6E6E6;}
.ncs-chain-list ul li .handle-img {background: url(../images/cart-coupon-icons.png) no-repeat 0 0; width: 72px; height: 24px;display:block; float: right; margin: 10px 15px 0px 0;}

.ncs-chain-detail { background: url(../images/shop/ncs_chain_bg.jpg) no-repeat 50% 50%; width: 1160px; padding: 20px; margin: 20px auto; overflow: hidden;}
.ncs-chain-detail .chain-img { float: left; width: 360px; height: 360px; overflow: hidden;}
.ncs-chain-detail .chain-info { float: right; width: 760px;}
.ncs-chain-detail .chain-info .chain-name { padding: 10px; border-bottom: dotted 1px #CCC;}
.ncs-chain-detail .chain-info a { color: #FFDB60; background-color: #F32613; float: right; padding: 4px 10px; border-radius: 5px;}
.ncs-chain-detail .chain-info a i {background: url(../images/shop/2014_ncs_public_img.png) no-repeat -30px -100px; vertical-align: middle; display: inline-block; *display: inline; width: 10px; height: 12px; margin-right: 5px; *zoom: 1;}
.ncs-chain-detail .chain-info h1 { font-size: 24px; font-weight: 600; line-height: 30px; color: #333; display: inline-block;}
.ncs-chain-detail .chain-info dl { padding: 5px 5px 10px 5px;}
.ncs-chain-detail .chain-info dt { font-size: 14px; line-height: 32px; color: #555;}
.ncs-chain-detail .chain-info dd { font-size: 12px; line-height: 24px; color: #777;}

/*规格值的选择*/
.ncs-key dl { padding: 8px 0 0;}
.ncs-key ul { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.ncs-key ul li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; margin: 0 6px 6px 0; position: relative; z-index: 1;}
.ncs-key ul li { *display: inline/*IE6,7*/; *zoom:1;}
.ncs-key ul li a { white-space: nowrap; display: block; min-height: 24px; padding: 1px; border: 1px solid #DDD; cursor: pointer;}
.ncs-key ul li.sp-img a { background-color: #FFF; font-size: 12px; color: #999; padding-right: 5px;}
.ncs-key ul li.sp-img a img { vertical-align: middle; display: inline-block; max-width: 24px; max-height: 24px; margin-right: 5px;}
.ncs-key ul li.sp-txt a { font-size: 12px; line-height: 24px; color: #000; text-decoration: none; background: #FFF none; text-align: center; white-space: nowrap; min-width: 20px; height: 24px; padding: 0 5px !important; margin: 1px;}
.ncs-key ul li.sp-img a:hover, 
.ncs-key ul li.sp-img a.hovered { color: #F32613; text-decoration: none; border: 2px solid #F32613; padding: 0 4px 0 0;}
.ncs-key ul li.sp-txt a:hover, 
.ncs-key ul li.sp-txt a.hovered { border: 2px solid #F32613; margin: 0;}
.ncs-key ul li a i { display: none;}
.ncs-key ul li a.hovered i { font-size: 0; line-height: 0; background: url(../images/shop/2014_ncs_public_img.png) no-repeat 0 0; display: block; width: 11px; height: 11px; position: absolute; z-index: 1; right: 2px; bottom: 2px;}

/*购买数量和库存*/
.ncs-buy { display: block; clear: both; padding: 20px 0 20px 30px; position: relative; z-index: 1;}
.ncs-figure-input { vertical-align: top; display: inline-block; width: 65px; position: relative; z-index: 1;}
.ncs-figure-input .input-text { color: #333; font-family: Tahoma; font-size: 16px; font-weight: 600; line-height: 41px; text-align: center; height: 41px; width: 41px; padding: 0; border: solid 1px #E6E6E6;}
.ncs-figure-input a { font-size: 0; background-color: #FFF; background: url(../images/shop/2014_ncs_public_img.png) no-repeat; display: block; width: 20px; height: 20px; border-style: solid; border-color: #E6E6E6; border-width: 1px 1px 1px 0; position: absolute; z-index: 1; left: 42px; -webkit-text-size-adjust:none;}
.ncs-figure-input a:hover { color: #F32613; text-decoration: none;}
.ncs-figure-input a.increase { background-position: -100px -100px; top: 0;}
.ncs-figure-input a.decrease { background-position: -120px -100px; top: 21px;}
.ncs-figure-input span { white-space: nowrap; display: block; position: absolute; z-index: 1; top: 50px; left: 0;}
.ncs-figure-input span em { margin: 0 2px;}
.ncs-figure-input span strong { color: #F60; margin: 0 2px;}

/* 购买提示信息 */
.ncs-point { font-size: 14px; color: 666; background-color: #FFF7D1; display: block; height: 24px; padding: 5px 9px; border: solid 1px #E5DEBC; position: absolute; z-index: 99; top: 74px; left: 85px; box-shadow: 3px 3px 0 rgba(0,0,0,0.10);}
.ncs-point i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -70px -100px; display: block; width: 8px; height: 8px; margin-top: -13px; margin-left: 8px; margin-bottom: 5px;}
.ncs-point span { line-height: 24px;}
.ncs-point span strong { font-weight: 600; color: #FF5C4D; margin: 0 2px;}
.ncs-point span a { color: #0066CC; text-decoration: underline; margin: 0 2px;}
.ncs-point span.look { font-weight: 600; color: #FF5C4D;}

/* 到货通知 */
.ncs-goods-summary .ncs-btn a.arrival { color: #690; vertical-align: top; display: inline-block; margin-top: 5px;}
.ncs-goods-summary .ncs-btn a.arrival i { font-size: 14px;}

/* 立即购买和加入购物车按钮 */
.ncs-goods-summary .ncs-btn { vertical-align: top; display: inline-block; height: 42px; position: relative; z-index: 70; *display: inline/*IE6,7*/; zoom: 1;}
.ncs-goods-summary .ncs-btn a.buynow,
.ncs-goods-summary .ncs-btn a.addcart,
.ncs-goods-summary .ncs-btn a.no-buynow,
.ncs-goods-summary .ncs-btn a.no-addcart { font: bold 16px/32px "Microsoft Yahei"; color: #FFF; text-align: center; display: inline-block; height: 32px; padding: 5px 12px; margin-right: 5px; border-radius: 2px; position: relative; overflow: hidden;}
.ncs-goods-summary .ncs-btn a.buynow:hover,
.ncs-goods-summary .ncs-btn a.addcart:hover,
.ncs-goods-summary .ncs-btn a.no-buynow:hover,
.ncs-goods-summary .ncs-btn a.no-addcart:hover  { text-decoration: none;}
.ncs-goods-summary .ncs-btn a.buynow { background-color: #BA7538;}
.ncs-goods-summary .ncs-btn a:hover.buynow { background-color: #96602E;}
.ncs-goods-summary .ncs-btn a.addcart { background-color: #F32613;}
.ncs-goods-summary .ncs-btn a:hover.addcart { background-color: #CF0010;}
.ncs-goods-summary .ncs-btn a.no-buynow, 
.ncs-goods-summary .ncs-btn a.no-addcart,
.ncs-goods-summary .ncs-btn a:hover.no-buynow, 
.ncs-goods-summary .ncs-btn a:hover.no-addcart { background-color: #AAA; cursor: not-allowed;}
.ncs-goods-summary .ncs-btn a i { font-size: 17px; margin-right: 6px;}

/* 加入购物车弹出提示框 */
.ncs-cart-popup { background-color: #F5F5F5; display: none; width: 320px; height: 120px; border: solid 1px #E6E6E6; box-shadow: 0 0 3px rgba(153,153,153,0.25); position: absolute; z-index: 1; top: 72px; left: -1px;}
.ncs-cart-popup dl { display: block; }
.ncs-cart-popup dl dt { font: lighter 16px/20px "Microsoft Yahei"; color: #333; text-align: center; width: 100%; margin: 10px 0 5px 0;}
.ncs-cart-popup dl dt a { font: 10px/12px Verdana; color: #999; text-align: center; display: inline-block; width: 12px; height: 12px; float: right; margin: -5px 5px 0 0; cursor: pointer;}
.ncs-cart-popup dl dt a:hover { text-decoration: none; color: #333;}
.ncs-cart-popup dl dd { text-align: center; width: 100%; margin: 0 0 5px 0;}

/*服务承诺*/
.ncs-cti { margin-bottom: 10px;}
.ncs-cti dd { overflow: hidden;}
.ncs-cti dd span { margin-right: 10px; color: #666666; white-space: nowrap;}
.ncs-cti dd span img { vertical-align: middle; display: inline-block; width: 16px; height: 16px; margin-right: 4px;}


.ncs-handle { width: 350px; height: 24px; padding-top: 10px; border-top: dotted 1px #EEE; position: absolute; z-index: 1; left: 10px; top: 415px;}
.ncs-handle a { color: #777; background-color: #FFF; float: left; padding: 1px 6px; margin-right: 5px; border: solid 1px #F5F5F5; border-radius: 4px;}
.ncs-handle a:hover { text-decoration: none; color: #333; background-color: #F5F5F5;}
.ncs-handle a.inform { float: right;}
.ncs-handle a.selected { color: #FFF; background-color: #F32613; border-color: #F32613;}
.ncs-handle a span { font-family: Arial; color: #AAA; margin-left: 4px;}
.ncs-handle a.compare i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -100px -70px; vertical-align: middle; width: 12px; height: 12px; display: inline-block; margin-right: 4px;}
.ncs-handle a.compare.selected i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -120px -70px; vertical-align: middle; width: 12px; height: 12px; display: inline-block; margin-right: 4px;}

.ncs_share { background-color: #FAFAFA; display: inline-block; *display: inline/*IE6,7*/; padding: 5px 0; margin: 10px 0 0 20px; border-radius: 3px; position: relative; overflow: hidden; border: solid 1px #E6E6E6; box-shadow: 0 0 0 2px rgba(204,204,204,0.10); overflow: hidden; zoom:1;}
.ncs_share a { color: #005EA6; display: inline-block; height: 20px; padding: 0 8px; margin-left: -1px; border-left: solid 1px #E6E6E6;}
.ncs_share a i { font-size: 14px; margin-right: 4px; color: #999; vertical-align: middle;}
.ncs_share a em { font-weight: 600; color: #999; vertical-align: middle; display: inline-block; margin-left: 2px }
.ncs_share a:hover i, .ncs_share a:hover em { text-decoration: none;} 



/* 商品已下架状态提示 */
.ncs-saleout { background-color: #FAFAFA; width: 86%; padding: 10px 20px; margin: 20px 0; border: dotted 1px #E6E6E6;}
.ncs-saleout dt { font-size: 16px !important; line-height: 24px; font-weight: 600; color: #F32613; width: auto; height: 24px !important; margin: 0 !important;  }
.ncs-saleout dt i { margin-right: 6px;}
.ncs-saleout dd { color: #777; clear: both; line-height: 20px !important; margin: 6px 0 !important; padding: 0 0 0 18px !important;}



/*优惠套餐销售*/
.ncs-bundling-tab { display: block; height: 20px; padding: 10px 0; margin-left: 20px; overflow: hidden;}
.ncs-bundling-tab span { border-left: solid 1px #E6E6E6; margin-left: -1px;}
.ncs-bundling-tab span a { line-height: 20px; padding: 0 15px; }
.ncs-bundling-tab span.selected a { font-weight: 600; color: #F32613;}
.ncs-bundling-container { background: #FFF; height: 230px;}
.ncs-bundling-container ul.ncs-bundling-list { font-size: 0; *word-spacing:-1px/*IE6、7*/; display: block; width: 980px; height: 230; float: left; margin: 0; overflow: hidden;}
.ncs-bundling-container ul li { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; width: 156px; padding: 20px 20px 0 20px; margin: 0; *zoom: 1; position: relative; z-index: 1;}
.ncs-bundling-container .goods-thumb { background-color: #FFF; width: 120px; height: 120px; margin: 0 auto;}
.ncs-bundling-container .goods-thumb a { line-height: 0; background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 120px; height: 120px; overflow: hidden;}
.ncs-bundling-container .goods-thumb img { max-width: 120px; max-height: 120px; margin-top:expression(120-this.height/2); *margin-top:expression(60-this.height/2);}
.ncs-bundling-container dl { display:block; padding: 0; margin: 4px 0 0 0;}
.ncs-bundling-container dl dt { line-height: 18px; width: 100%; height: 36px; overflow:hidden;}
.ncs-bundling-container dl dt a { color: #555;}
.ncs-bundling-container dl dd { line-height: 16px; color: #999; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
.ncs-bundling-container dl dd .o-price { text-decoration: line-through;}
.ncs-bundling-container dl dd .b-price { font-weight: 600; color: #F32613;}
.ncs-bundling-container .plus { background: url(../images/shop/2014_ncs_public_img.png) no-repeat 0 -130px; display: block; width: 19px; height: 19px; position: absolute; z-index: 1; top: 70px; left: -19px;}
.ncs-bundling-price { color: #777; background-color: #FAFAFA; display: block; width: 177px; height: 220px; float: right; padding: 50px 15px 0 15px; margin-top: -40px; border-left: solid 1px #E6E6E6;}
.ncs-bundling-price ul {}
.ncs-bundling-price ul li { line-height: 28px; color: #777; white-space: nowrap; text-overflow: ellipsis; width: 100%; padding: 0!important; overflow: hidden;}
.ncs-bundling-price ul li strong { color: #005EA6; font-weight: 600;}
.ncs-bundling-price ul li em { font-family: Arial;}
.ncs-bundling-price ul li .bundling-price { font-weight: 600; color: #D00;}
.ncs-bundling-price ul li .bundling-save { color: #468C00;}

/*推荐组合*/
.ncs-combo-box { height: 270px;}
.ncs-combo-box .default-goods { width: 170px; padding: 40px 19px 0 20px; float: left; position: relative; z-index: 1;}
.ncs-combo-box .default-goods .goods-thumb { background-color: #FFF; width: 140px; height: 140px; margin: 0 auto;}
.ncs-combo-box .default-goods .goods-thumb a { line-height: 0; background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 140px; height: 140px; overflow: hidden;}
.ncs-combo-box .default-goods .goods-thumb img { max-width: 140px; max-height: 140px; margin-top:expression(140-this.height/2); *margin-top:expression(70-this.height/2);}
.ncs-combo-box .default-goods dt { color: #555; line-height: 18px; height: 36px; margin-top: 5px; overflow: hidden;}
.ncs-combo-box .default-goods .goods-price { font-weight: 600; color: #D00;}
.ncs-combo-box .default-goods .plus { background: url(../images/shop/2014_ncs_public_img.png) no-repeat 0 -130px; display: block; width: 19px; height: 19px; position: absolute; z-index: 1; top: 100px; right: 10px;}

.ncs-combo-box .ncs-combo-tab { display: block; height: 20px; padding: 10px 0; margin-left: 20px; overflow: hidden;}
.ncs-combo-box .ncs-combo-tab span { border-left: solid 1px #E6E6E6; margin-left: -1px;}
.ncs-combo-box .ncs-combo-tab span a { line-height: 20px; padding: 0 15px; }
.ncs-combo-box .ncs-combo-tab span.selected a { font-weight: 600; color: #F32613;}
.ncs-combo-box .combo-goods { width: 779px; float: left;}
.ncs-combo-box .combo-goods-box { width: 778px; height: 210px; padding: 10px 0; margin: 0; border: solid #E6E6E6; border-width: 1px 0 0 1px; position: relative; z-index: 1;}
.ncs-combo-box .combo-goods-list { width: 684px; height: 210px; margin: 0 47px; overflow: hidden; position: relative; z-index: 1;}
.ncs-combo-box .combo-goods-list ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; white-space: nowrap; overflow: hidden; position: absolute; z-index: 1; top: 0; left: 0;}
.ncs-combo-box .combo-goods-list li { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; width: 130px; padding: 10px 0 10px 18px; margin-right: 27px; border: solid 1px transparent; *zoom: 1; position: relative; z-index: 1;}
.ncs-combo-box .combo-goods-list li:hover { border: solid 1px #D00;}
.ncs-combo-box .combo-goods-list li .goods-thumb { background-color: #FFF; width: 80px; height: 80px; margin: 0 auto;}
.ncs-combo-box .combo-goods-list li .goods-thumb a { line-height: 0; background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 80px; height: 80px; overflow: hidden;}
.ncs-combo-box .combo-goods-list li .goods-thumb img { max-width: 80px; max-height: 80px; margin-top:expression(80-this.height/2); *margin-top:expression(40-this.height/2);}
.ncs-combo-box .combo-goods-list li dl { margin-top: 10px;}
.ncs-combo-box .combo-goods-list li dt { line-height: 16px; white-space: normal; height: 48px; overflow: hidden;}
.ncs-combo-box .combo-goods-list li dt a { color: #777;}
.ncs-combo-box .combo-goods-list li dd.goods-price { font-weight: 600; color: #D00; margin-top: 5px;}
.ncs-combo-box .combo-goods-list li dd.rp { color: #999; text-decoration: line-through;}
.ncs-combo-box .combo-goods-list li .checkbox { position: absolute; z-index: 1; top: 5px; right: 5px;}
.ncs-combo-box .combo-goods-list li .plus { background: url(../images/shop/2014_ncs_public_img.png) no-repeat 0 -130px; display: block; width: 19px; height: 19px; position: absolute; z-index: 1; top: 45px; left: -20px;}
.ncs-combo-box .combo-goods-box .F-prev { background-color: #FFF; width: 46px; height: 229px; border-right: solid 1px #E6E6E6; position: absolute; z-index: 2; top: 0; left: 0;}
.ncs-combo-box .combo-goods-box .F-next { background-color: #FFF; width: 46px; height: 229px; border-left: solid 1px #E6E6E6; position: absolute; z-index: 2; top: 0; right: 0;}

.ncs-combo-box .combo-goods-box .F-prev i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -12px 0; display: block; width: 10px; margin: 105px 0 0 17px; height: 20px; cursor: pointer;  opacity: 0.5;}
.ncs-combo-box .combo-goods-box .F-next i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -22px 0; display: block; width: 10px; margin: 105px 0 0 17px; height: 20px; cursor: pointer;  opacity: 0.5;}
.ncs-combo-box .combo-goods-box .F-prev:hover i,
.ncs-combo-box .combo-goods-box .F-next:hover i { opacity: 1;}
.ncs-combo-box .combo-goods-box .no-slider i { opacity: 0.1 !important; cursor: no-drop !important;}


.combo-price { color: #777; background-color: #FAFAFA; display: block; width: 177px; height: 220px; float: right; padding: 50px 15px 0 15px; border-left: solid 1px #E6E6E6;}
.combo-price ul {}
.combo-price ul li { line-height: 28px; color: #777; white-space: nowrap; text-overflow: ellipsis; width: 100%; padding: 0!important; overflow: hidden;}
.combo-price ul li strong { color: #005EA6; font-weight: 600;}
.combo-price ul li em { font-family: Arial;}
.combo-price ul li .price { font-weight: 600; color: #D00;}


/* 商品内容处TabBar */
.tabbar { background: #FFF;}
.ncs-goods-title-bar { background-color: #FFF; border-style: solid; border-color: #F32613 #E6E6E6 #F5F5F5 #E6E6E6; border-width: 2px 1px 1px 1px; }
.ncs-goods-title-bar h4 { font: normal 14px/20px "Microsoft Yahei"; text-decoration:none; color:#777; display: block; padding: 6px 15px 5px 15px;}
.ncs-goods-title-nav { margin-top: 0;}
.ncs-goods-title-nav ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; background-color: #FFF; border: solid #E6E6E6 1px;}
.ncs-goods-title-nav ul li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block;}
.ncs-goods-title-nav ul li { *display: inline/*IE6,7*/;}
.ncs-goods-title-nav ul li a { font: normal 14px/20px "Microsoft Yahei"; text-decoration:none; color:#777; background-color: #FFF; display: block; padding: 8px 35px 7px 35px; border-style: solid; border-color: #E6E6E6; border-width: 0 1px 0 0;}
.ncs-goods-title-nav ul li.current { margin: -1px 0 -1px -1px;}
.ncs-goods-title-nav ul li.current a { color: #333; background-color: #FFF; padding: 8px 35px 6px 35px; border-style: solid; border-color: #F32613 #DDD transparent #DDD; border-width: 3px 1px 0 1px ;}
.ncs-goods-info-content { padding-bottom: 23px;}
.ncs-goods-info-content .top { padding: 9px; margin-bottom: 20px; border: solid #E6E6E6; border-width: 0 1px 1px;}
.ncs-promotion { margin-bottom: 20px;}
.ncs-promotion .ncs-goods-info-content { padding: 0; border: solid #E6E6E6; border-width: 0 1px 1px;}


/*商品属性值*/
.nc-goods-sort { font-size: 0; *word-spacing:-1px/*IE6、7*/; background-color: #FFF; border: solid #DDD; border-width: 0 1px 1px; padding:10px 0; margin-bottom: 10px;}
.nc-goods-sort li { font-size: 12px; line-height: 20px; letter-spacing: normal; word-spacing: normal; text-overflow : ellipsis; white-space: nowrap; display: inline-block; width: 23%; padding: 0 0 0 2%; margin: 0; overflow: hidden;}
.nc-goods-sort li { *display: inline; }


/* 商品详情内容 */
.default,
.top-template,
.bottom-template { padding: 0; margin: 0; border: 0; overflow: hidden;}
.default img,
.top-template img,
.bottom-template img { vertical-align: top;}

#main-nav { width: auto;}
.ncs-goods-layout { width: 100%; margin-bottom: 10px; position: relative; z-index: 1; overflow: hidden;}
.sticky #main-nav { width: 1200px; position:fixed; _position:relative; top:0; z-index: 999;}
.sticky #main-nav ul { margin:0 auto;}

.switch-bar{ background: transparent url(../images/shop/switch_bar.png) no-repeat -15px 0; width:13px; height: 237px; position:absolute; z-index:999; top:200px; left: 0px; _block:none/*if IE6*/;}
.switch-bar a { display:block; width: 13px; height: 42px; margin-top: 97px; cursor: pointer;}
.switch-bar a:hover { text-decoration: none;}

.ncs-sidebar { display: none; width: 210px _position:relative/*IE6*/}
.expanded .ncs-goods-main { float: right; width: 980px;  _position:relative/*IE6*/ }
.expanded #main-nav { width: 980px;  _position:relative/*IE6*/}
.expanded .switch-bar{ background-position: 0 0; left: -23px;  _block:none/*if IE6*/}
.expanded .ncs-sidebar { width: 210px; display: block; float:left; _position:relative/*IE6*/}

/*虚拟商品实体店地址地图*/
.ncs-store-map-content { margin: 20px; overflow: hidden;}
.ncs-store-map-baidu { float: left;}
.ncs-store-map-info { width: 300px; height: 400px; float: right; padding-left: 20px; border-left: solid 1px #E6E6E6;}
.ncs-store-map-info .store-district { font-size: 16px; margin-bottom: 20px;}
.ncs-store-map-info .address-box { width: 100%; height: 360px; position: relative; z-index: 1; overflow: hidden;}
.ncs-store-map-info .address-list {}
.ncs-store-map-info .address-list dl { border: solid 1px #E6E6E6; padding-bottom: 5px; margin-bottom: 10px; }
.ncs-store-map-info .address-list dt { font-size: 12px; line-height: 20px; font-weight: 600; background-color: #FAFAFA; padding: 2px 10px; border-bottom: solid 1px #E6E6E6;}
.ncs-store-map-info .address-list dd { font-size: 12px; line-height: 20px; margin: 5px 10px 0 10px}

/*评价详情*/
.ncs-comment .rate { line-height: 20px; color: #F32613; vertical-align: middle; display: inline-block; *display: inline; *zoom:1; margin: 10px 40px 10px 20px;}
.ncs-comment .rate strong { font: lighter 40px/40px arial; vertical-align: bottom;}
.ncs-comment .rate sub { font: 16px/20px arial; vertical-align: bottom; margin-right: 6px;}
.ncs-comment .rate span { color: #999; display: block; clear: both;}
.ncs-comment .percent { vertical-align: middle; display: inline-block; *display: inline; *zoom:1;}
.ncs-comment .percent dl { font-size: 0; *word-spacing:-1px/*IE6、7*/;}
.ncs-comment .percent dt { font-size: 12px; line-height: 20px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; width: 80px; height: 20px;}
.ncs-comment .percent dt { *display: inline/*IE6,7*/;}
.ncs-comment .percent dt em { color: #999; margin-left: 4px;}
.ncs-comment .percent dd { background-color: #F5F5F5; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; width: 100px; height: 14px; margin: 3px 0;}
.ncs-comment .percent dd { *display: inline/*IE6,7*/;}
.ncs-comment .percent dd i { background-color: #F32613; display: block; height: 14px;}
.ncs-comment .btns { vertical-align: middle; display: inline-block; *display: inline; *zoom:1; height: 60px; padding-left: 30px; margin-left: 400px; border-left: dotted 1px #E6E6E6;}

/*评价详情-列表*/
.ncs-commend-main { padding: 20px 0 0 0; border: solid #E6E6E6; border-width: 0 1px 1px;}
.ncs-commend-floor { margin: 0 40px 0 60px; border-left: solid 3px #F5F5F5; position: relative; z-index: 1;}
.ncs-commend-floor .user-avatar { background-color: #F2F2F2; width: 40px; height: 40px; border-radius: 20px; position: absolute; z-index: 1; top: 0; left: -20px; }
.ncs-commend-floor .user-avatar a { text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 40px; height:40px; border-radius: 20px; overflow: hidden;}
.ncs-commend-floor .user-avatar a img { max-width: 40px; max-height: 40px; margin-top:expression(40-this.height/2); *margin-top:expression(20-this.height/2); border-radius: 20px;}
.ncs-commend-floor .detail { margin: 10px 0 0 30px;}
.ncs-commend-floor .detail dt { line-height: 24px; display: block; height: 24px; margin-bottom: 10px; overflow: hidden;}
.ncs-commend-floor .detail dt .user-name { font: bold 12px/20px "Microsoft Yahei"; color: #AAA; float: left;}
.ncs-commend-floor .detail dt .goods-raty { color: #777; float: right;}
.ncs-commend-floor .detail dd { font-size: 14px; line-height: 18px; color: #555; margin-bottom: 10px;}

.ncs-commend-floor .detail .photos-thumb { font-size: 0; *word-spacing:-1px/*IE6、7*/; vertical-align: middle; display: inline-block;}
.ncs-commend-floor .detail .photos-thumb li { vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; text-align: center; width: 34px; margin-right: 6px; *zoom: 1;}
.ncs-commend-floor .detail .photos-thumb a { line-height: 0; background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 30px; height: 30px; padding: 1px; border: solid 1px #E6E6E6; overflow: hidden;}
.ncs-commend-floor .detail .photos-thumb a:hover { border-color: #F32613;}
.ncs-commend-floor .detail .photos-thumb a img { max-width: 30px; max-height: 30px; margin-top:expression(30-this.height/2); *margin-top:expression(15-this.height/2)/*IE6,7*/;}
.ncs-commend-floor .detail .pubdate { font-size: 12px; color: #AAA;}
.ncs-commend-floor .detail .explain { font-size: 12px; color: #DA542E; background-color: #FFC; padding: 9px; border: dashed 1px #FEF4B1;}
.ncs-commend-floor .detail hr { font-size: 0; line-height: 0; padding: 0; margin: 10px 0; height: 0; width: 100%; border: none 0; border-top: dashed 1px #E6E6E6;}
.more-commend { text-decoration: underline !important; position: absolute; z-index: 1; top: 10px; left: 30px; color: #F32613}

/*购买记录*/
.ncg-salelog .price { background-color: #FFF;}
.ncg-salelog .price strong { font: 600 14px/20px arial; color: #F32613; margin: 0 4px;}
.ncg-salelog .price span { line-height: 16px; color: #FFF; background-color: #C8C8C8; vertical-align: middle; display: inline-block; height: 16px; padding: 1px 4px; margin-left: 20px;}
.ncg-salelog .bd table { background-color: #FFF;}
.ncg-salelog .bd thead th { font-weight:600; text-align:center; padding: 8px 0; border-bottom: solid 2px #E7E7E7;}
.ncg-salelog .bd tbody td { text-align:center; padding: 15px 0; border-bottom: dashed 1px #E7E7E7;}

/*咨询留言*/

.ncs-cosult-tips { width: 780px; height: 65px; float: left; margin: 10px 0 5px 10px; position: relative; z-index: 1;}
.ncs-cosult-tips i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -80px 0; width: 147px; height: 65px; position: absolute; z-index: 1; top: 0; left: 0;}
.ncs-cosult-tips p { line-height: 18px; color: #9B827D; width: 700px; height: 36px; position: absolute; z-index: 1; top: 28px; left: 64px;}
.ncs-cosult-askbtn { float: right; padding: 10px;}
.ncs-cosult-main { padding: 20px 0 0 0; border: solid #E6E6E6; border-width: 0 1px 1px;}
.ncs-cosult-main .more { margin: 10px 15px;}
.ncs-cosult-list { padding: 6px; border-bottom: dotted 1px #D6D6D6;}
.ncs-cosult-list dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; display: block; margin-bottom: 4px;}
.ncs-cosult-list dl.asker { color: #999;}
.ncs-cosult-list dl.ask-con { color: #555;}
.ncs-cosult-list dl.reply { color: #F32613;}
.ncs-cosult-list dt { font-size: 12px; line-height: 20px; vertical-align: top; letter-spacing: normal; word-spacing: normal; text-align: right; display: inline-block; *display: inline/*IE7*/; *zoom: 1; width: 7%; }
.ncs-cosult-list dd { font-size: 12px; line-height: 20px; vertical-align: top; letter-spacing: normal; word-spacing: normal; text-align: left; display: inline-block; *display: inline/*IE7*/; *zoom: 1; width: 93%; }
.ncs-cosult-list dd p { display: inline-block; width: 760px;}
.ncs-cosult-list dd time { text-align: right; display: inline-block; color:#999; }
.ncs-consult-form { display: block; padding: 10px 15px; border: solid #E6E6E6; border-width: 0 1px 1px;}
.ncs-consult-form dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; margin-bottom: 4px;}
.ncs-consult-form dt, .ncs-consult-form dd { font-size: 12px; line-height: 20px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; *zoom: 1;}
.ncs-consult-form dt { font-weight: 600; width: 65px;}
.ncs-consult-form dd { position: relative; z-index: 1;}
.ncs-consult-type-intro { margin-bottom: 4px;}
.ncs-consult-form  label { font-size: 12px; vertical-align: top; display: inline-block; margin-right: 20px;}
.ncs-consult-form  label .text { display: inline-block; vertical-align: middle; padding: 2px 4px;}
.ncs-consult-form  label .radio { display: inline-block; vertical-align: middle; margin-right: 4px;}
.ncs-consult-form  label img { display: inline-block; vertical-align: middle; margin: 0 4px ; cursor: pointer;}
.ncs-consult-form  label span { color: #09C;}
.ncs-consult-form .counter { line-height: 20px; color: #999; vertical-align: top; display: inline-block; margin-left: 10px; }
.ncs-consult-form .counter em { font-weight: 700; margin: 0 2px;}
.ncs-consult-form .counter em.warning { color: #F60; background-color: transparent; width: auto; padding: 0; border: none;}
.ncs-consult-form .counter em.exceeded { color: #F00;}
.ncs-consult-form .code { background-color: #FFFFFF; width: 114px; height: 34px; border: solid 1px #555; position: absolute; z-index: 9; top: -40px; left: -15px; display: none; box-shadow: 0 3px 3px 0 rgba(0,0,0,0.2);}
.ncs-consult-form .code .arrow { background:url(../images/shop/2014_ncs_public_img.png) no-repeat -40px 0; display: block; width: 14px; height: 7px; position: absolute; left: 21px; bottom: -7px;}
.ncs-consult-form .code img { width: 90px; height: 26px; position: absolute; z-index: 1; top: 4px; left: 4px;}
.ncs-consult-form .code .close { display: block; width: 10px; height: 10px; padding: 1px; position: absolute; z-index: 1; top: 4px; right: 4px;}
.ncs-consult-form .code .close:hover, 
.ncs-consult-form .code .change:hover { background-color: #CCC; border-radius: 5px; -webkit-border-radius: 5px/*webkit*/;}
.ncs-consult-form .code .close i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -40px -7px; display: block; width: 10px; height: 10px; opacity: 0.5;}
.ncs-consult-form .code .change { display: block; width: 10px; height: 10px; padding: 1px; position: absolute; z-index: 1; bottom: 4px; right: 4px;}
.ncs-consult-form .code .change i { background: url(../images/shop/2014_ncs_public_img.png) no-repeat -50px -7px; display: block; width: 10px; height: 10px; opacity: 0.5;}
.ncs-consult-form .code .close:hover i , 
.ncs-consult-form .code .change:hover i { opacity: 1;}


/* 推荐商品列表 */
.ncs-recommend { clear:both;}
.ncs-recommend .title { background-color: #FFF; height: 20px; padding: 5px 10px; border-bottom: solid 2px #F32613;}
.ncs-recommend .title h4 { font: 14px/20px "Microsoft Yahei"; color: #333; margin-left: 6px;}
.ncs-recommend .content { overflow: hidden; }
.ncs-recommend .content ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; white-space: nowrap; padding: 10px 0 5px 0; margin-left: -5px;}
.ncs-recommend .content ul li { font-size: 12px; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/; width: 20%; padding: 10px 0 5px 0;  border-left: dotted 1px #E6E6E6; *zoom:1;}
.ncs-recommend .content ul li dl { text-align: center; width: 100%; padding-top:120px; margin:0px auto; position:relative; z-index:1;}
.ncs-recommend .content ul li .goods-pic { background:#FFF; width:120px; height:120px; margin-left: -60px; position:absolute; top: 0px; left: 50%; }
.ncs-recommend .content ul li .goods-pic a { background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 120px; height:120px; overflow: hidden;}
.ncs-recommend .content ul li .goods-pic a img { max-width: 120px; max-height: 120px; margin-top:expression(120-this.height/2); *margin-top:expression(60-this.height/2);}
.ncs-recommend .content ul li .goods-name { line-height:18px; white-space: pre-wrap; height: 36px; width: 90%; overflow: hidden; margin: 8px auto;}
.ncs-recommend .content ul li .goods-name em { color: #F32613;}
.ncs-recommend .content ul li .goods-price { font-weight: 600; color: #F32613; height: 20px;}

/* 无内容 */
.ncs-norecord { color: #999; text-align: center; height:70px; line-height: 70px; padding: 15px 0 ; background-color: #FFF; border: none !important;}

/* (nc-s-c-s) full name -> ShopNC Store Container Style */
.ncs-sidebar-container { margin-bottom: 10px;}
.ncs-sidebar-container .title { background-color: #F7F7F7 ; height: 20px; padding: 5px 10px; border: solid #DDD; border-width: 1px 1px 0 1px;}
.ncs-sidebar-container .title h4 { font: 600 14px/20px "Microsoft Yahei"; color: #666;}
.ncs-sidebar-container .content { border: solid 1px #DDD; background:#FFF; }


/* ====================== */
/* 店铺简介边栏 -> info.php */
/* ====================== */
.ncs-info { width: 210px;}
.ncs-info .title { background-color: #F5F5F5; padding: 8px 10px; border: solid 1px #E6E6E6;}
.ncs-info .title h4 { font: 600 14px/20px "Microsoft Yahei"; color: #555;}
.ncs-info .content { border: solid #E6E6E6; border-width: 0 1px 1px;}
.ncs-info .content dl { font-size: 0; *word-spacing:-1px/*IE6、7*/; display: block; padding: 4px 0;}
.ncs-info .content dl dt { font-size: 12px; color: #666; vertical-align: top; letter-spacing: normal; word-spacing: normal; text-align: right; display: inline-block; *display: inline/*IE7*/; width: 68px; *zoom:1;}
.ncs-info .content dl dd { font-size: 12px; color: #333; vertical-align: top; letter-spacing: normal; word-spacing: normal; white-space: nowrap; text-overflow: ellipsis; display: inline-block; *display: inline/*IE7*/; width: 130px; *width: 125px; overflow: hidden; zoom: 1;}
.ncs-info .all-rate .rating { background: url(../images/2014grate.png) no-repeat 0 -18px ; vertical-align: middle; display: inline-block; *display: inline/*IE7*/; width: 79px; height: 17px; *zoom:1;}
.ncs-info .all-rate .rating span { background: url(../images/2014grate.png) no-repeat 100% 0; display: block; height: 18px;}
.ncs-info .all-rate em { color: #DA542E; font-weight: 600; vertical-align: middle; margin-right: 2px;}
.ncs-info .content .detail-rate { clear: both;}
.ncs-info .store-name { font-weight: 600; color: #555; height: 20px; padding: 6px 9px; border-bottom: solid 1px #E6E6E6;}

.ncs-detail-rate { color: #999; width: 182px; padding: 5px; margin: 0 auto;}
.ncs-detail-rate ul { font-size: 0; *word-spacing:-1px/*IE6、7*/; padding: 5px 0;}
.ncs-detail-rate li { font-size: 12px; color: #333; vertical-align: top; text-align: center; display: inline-block; *display: inline/*IE7*/; width: 33.333%; height: 32px; *zoom: 1;}
.ncs-detail-rate li h5 { color: #777; line-height: 16px; height: 16px;}
.ncs-detail-rate li div { line-height: 16px; height: 16px; }
.ncs-detail-rate .credit { color: #555; display: inline-block; width: 35px; margin-left: 4px;}
.ncs-detail-rate li div i { background: url(../images/2014grate.png) no-repeat; vertical-align: middle; display: inline-block; width: 16px; height: 16px;}
.ncs-detail-rate .high { color: #DA542E; }
.ncs-detail-rate .high i { background-position: 0 -40px;}
.ncs-detail-rate .equal { color: #DA542E; }
.ncs-detail-rate .equal i { background-position: -32px -40px;}
.ncs-detail-rate .low { color: #28B779; }
.ncs-detail-rate .low i { background-position: -16px -40px;}
.ncs-info .btns { font-size: 0; *word-spacing:-1px/*IE6、7*/; text-align: center; padding-bottom: 12px; border-bottom: solid 1px #E6E6E6;}
.ncs-info .btns a { font-size: 12px; line-height: 20px; color: #333; background-color: #F5F5F5; vertical-align: top; text-align: center; display: inline-block; *display: inline/*IE7*/; height: 20px; padding: 3px 10px; border: solid 1px #CCC; *zoom: 1;}
.ncs-info .btns a.goto { color: #FFF; background-color: #333; border-color: #333; margin-right: 10px;}
.ncs-info .btns a span { line-height: 20px; color: #999; margin-left: 3px; }
.ncs-info .btns a:hover { text-decoration: none;}
.ncs-info .no-border { border: 0!important; padding-bottom: 0!important;}
.ncs-info .special_business1{background: url(../images/shop/special_business1.png) no-repeat 0 0;  display: block;  height: 22px;}
.ncs-info .special_business2{background: url(../images/shop/special_business2.png) no-repeat 0 0;  display: block;  height: 22px;}
.ncs-info .special_business3{background: url(../images/shop/special_business3.png) no-repeat 0 0;  display: block;  height: 22px;}
.ncs-info .special_business4{background: url(../images/shop/special_business4.png) no-repeat 0 0;  display: block;  height: 22px;}
/* ====================== */
/* 左侧边栏样式 -> left.php */
/* ====================== */


/* 客服中心列表 */
.ncs-message-bar { border: solid 1px #E6E6E6; margin-bottom: 10px;}
.ncs-message-bar .default { padding: 9px; border-top: solid 1px #E6E6E6; margin-top: -1px;}
.ncs-message-bar .default h5 { line-height: 20px; font-weight: 600; display: inline-block;}
.ncs-message-bar .default span { color: #555; height: 20px;}
.ncs-message-bar .service-list { border-top: solid 1px #D8D8D8; }
.ncs-message-bar dl { width: 180px; padding: 9px; overflow: hidden;}
.ncs-message-bar dt { line-height: 20px; font-weight: 600; color: #333; display: block;}
.ncs-message-bar dd { color: #555; margin-left: 16px; clear:both; padding: 4px 0;}
.ncs-message-bar dd span { line-height: 22px; margin: 0 6px 0 0;}
.ncs-message-bar dd img { vertical-align: middle;}
.ncs-message-bar dd p { line-height: 20px;}


/* 侧边栏搜索 */
.ncs-search { display: block; padding: 5px; border-bottom: dotted 1px #E6E6E6; }



/* 侧边栏商品分类 */
.ncs-class-bar p { background-color: #F9F9F9; border-bottom: solid 1px #E7E7E7; height: 28px;}
.ncs-class-bar p span { text-align: center; display:inline-block; width: 24%; height:20px; padding: 4px 0;}
.ncs-class-bar p span { *display:block; *float:left;}
.ncs-class-bar p a { line-height: 16px; color: #777; padding: 2px;}
.ncs-class-bar p a:hover { text-decoration: none; color: #FFF; background-color: #999; border-radius: 4px;}
.ncs-submenu { width:170px; margin: 5px 13px 5px 15px; _margin: 5px 6px 5px 8px; _display: inline-block; _float:left;}
.ncs-submenu li { font-weight: 600; text-align:left; margin: 6px 0; clear:both; }
.ncs-submenu li a { line-height: 20px; word-wrap: break-word; display: inline-block; *dispaly: inline; max-width: 135px; color: #333; overflow:hidden; *zoom:1;}
.ncs-submenu li ul { width: 150 ; margin: 5px 0px 5px 20px; }
.ncs-submenu li ul li { line-height: 20px; font-weight: normal; text-align:left; margin: 4px 0; }
.ico-none, .ico-block, .ico-sub { display: inline-block; float:left; cursor: default;}
.ico-none, .ico-block, .ico-sub { *display: inline;}
.ico-none, .ico-block { width: 10px; height:10px; text-align:center; margin: 5px 10px 5px 0; border-radius: 2px;}
.ico-none em, .ico-block em { font-size: 12px; line-height:10px!important; height:10px; }
.ico-sub { font-size: 0px; line-height:0; width: 3px; height:3px; margin: 8px 6px 8px 0; border-radius: 3px;}

.ncs-mall-category-list { font-size: 0; *word-spacing:-1px/*IE6、7*/; padding: 4px 0;}
.ncs-mall-category-list li { font-size: 12px; text-overflow: ellipsis; white-space: nowrap; display: inline-block; *display: inline; width: 40%; padding: 2px 5%; *zoom: 1; overflow: hidden;}

.ncs-mall-brand-list { font-size: 0; *word-spacing:-1px/*IE6、7*/; padding: 4px 0;}
.ncs-mall-brand-list li { font-size: 12px; text-overflow: ellipsis; white-space: nowrap; display: inline-block; *display: inline; width: 28.33%; padding: 4px 2% 4px 3%; *zoom: 1; overflow: hidden;}

/*商品列表页面侧边栏-推广商品*/
.ncs-mall-booth-list { padding: 9px; overflow: hidden; }
.ncs-mall-booth-list li { display: block; margin-bottom: 5px; padding-top: 5px; position: relative; z-index: 1;}
.ncs-mall-booth-list .goods-pic { width: 120px; height: 120px; padding: 0; margin: 0 auto;}
.ncs-mall-booth-list .goods-pic a { line-height: 0; text-align: center; vertical-align: middle; display: table-cell; *display: block;  width: 120px; height: 120px; overflow: hidden;}
.ncs-mall-booth-list .goods-pic img { max-width: 120px; max-height: 120px; margin-top: expression( 120-this.height/2); *margin-top:expression(60-this.height/2)/*IE6,7*/;}
.ncs-mall-booth-list .goods-name { *line-height: 18px; max-height: 36px; overflow: hidden;}
.ncs-mall-booth-list .goods-price { font-weight: 600; color: #F32613; text-align: center; padding: 0;}

.ncs-mall-booth-list li p { width: 80px; margin: 0 10px; float: left; display: inline; }
.ncs-mall-booth-list li p a { display: block; width: 78px; height: 78px; overflow: hidden; border: 1px solid #bbb; }
.ncs-mall-booth-list li p a:hover {  border: 1px solid #999; }
.ncs-mall-booth-list h3 { width: 90px; float: right; }
.ncs-mall-booth-list h3 a { display: block; width: 90px; height: 48px; line-height: 16px; overflow: hidden; font-weight: normal; color: #666; text-decoration: none; font-size: 12px; margin-bottom: 10px; }
.ncs-mall-booth-list h3 a:hover { text-decoration: underline; color: #f60; }
.ncs-mall-booth-list h3 span { display: block; color: #fe5504; font-weight: bold; font-size: 14px; }

/* 侧边栏排行榜 */
.ncs-top-tab { height:28px; border-bottom: solid 1px #E6E6E6; }
.ncs-top-tab li { float:left;}
.ncs-top-tab li a { color: #777; line-height: 20px; text-decoration:none; background-color: #F7F7F7; text-align:center; width: 86px; height: 20px; float:left; margin: 4px 0 1px 4px; padding:1px 0; border: solid #D8D8D8; border-width: 1px 1px 0 1px; border-radius: 4px 4px 0 0; text-shadow: 1px 1px 0 rgba(255,255,255,.75);}
.ncs-top-tab li.current a { color:#000; background-color: #FFF; padding: 1px 0 2px 0; margin: 4px 0 0 4px; box-shadow: 0 -1px 1px rgba(0,0,0,.05);}
.ncs-top-bar .hide { display: none;}

.ncs-top-panel { display: block; margin: 1px;}
.ncs-top-panel li { display: block; padding: 8px 12px 8px 4px; clear: both; margin-top: -1px; border-bottom: dashed 1px #E7E7E7;}
.ncs-top-panel li:hover { background-color: #F7F7F7; z-index: 1;}
.ncs-top-panel dl { display: block; height:50px; position: relative; z-index: 1;}
.ncs-top-panel dl:hover { z-index: 9;}
.ncs-top-panel dt { line-height: 16px; text-overflow: ellipsis ; overflow:hidden; white-space: nowrap; display: block; width: 130px; height: 16px; position: absolute; z-index: 1; top: 0; left: 60px;}
.ncs-top-panel dd.goods-pic { background-color: #FFF; display:block; width: 50px; height: 50px; position:absolute; z-index:999; top:0; left:0; }
.ncs-top-panel dd.goods-pic a { border: solid 1px #E7E7E7; width:40px; height: 40px; padding: 4px; display: inline-block; }
.ncs-top-panel dd.goods-pic p { background-color: #FFF; display:none; width:100px; height: 100px; padding:4px; border: solid 1px #F60; position:absolute; z-index:2; top:-10px; left:58px; box-shadow: 2px 2px 0px rgba(0,0,0,.2);}
.ncs-top-panel dd.goods-pic p big { font-size: 0; line-height: 0; width: 0; height: 0; display: block; border: 4px solid; border-color: transparent #FF6600 transparent transparent; position: absolute; z-index: 2; top: 18px; left: -9px;}
.ncs-top-panel dd.goods-pic p small { font-size: 0; line-height: 0; width: 0; height: 0; display: block; border: 4px solid; border-color: transparent #FFFFFF transparent transparent; position: absolute; z-index: 2; top: 18px; left: -8px;}
.ncs-top-panel dd.goods-pic:hover p { display: block;}
.ncs-top-panel dd.goods-pic:hover a { border-color: #F60;}
.ncs-top-panel dd.price { line-height: 16px; background: url(../images/shop/public.png) no-repeat 0 -80px; text-overflow: ellipsis ; overflow:hidden; white-space: nowrap; padding-left: 10px; width:110px; height: 16px; position: absolute; z-index: 1; top: 18px; left: 60px;} 
.ncs-top-panel dd.selled { line-height: 16px; background: url(../images/shop/public.png) no-repeat 0 -100px; text-overflow: ellipsis ; overflow:hidden; white-space: nowrap; width:100px; height: 16px; padding-left:20px; position: absolute; z-index: 1; top: 36px; left: 60px;}
.ncs-top-panel dd.selled strong { margin: 0 3px;}
.ncs-top-panel dd.collection{ line-height: 16px; background: url(../images/shop/public.png) no-repeat 0 -120px; float:left; text-overflow: ellipsis ; overflow:hidden; white-space: nowrap;  width:100px; height: 16px; padding-left:20px; position: absolute; z-index: 1; top: 36px; left: 60px;}
.ncs-top-panel dd.collection strong { margin: 0 3px;}
.ncs-top-bar p { width: 130px;  margin: 10px auto;}
.ncs-top-bar p a { line-height: 38px; color: #333; background-color: #FEF4B1; text-align: center; width: 128px; height: 38px; border: solid 1px #FFD863;border-radius: 5px; display:inline-block; box-shadow: 0 -1px 1px rgba(0,0,0,0.1)}
.ncs-top-bar p a:hover { text-decoration: none; color: #777; background-color: #FEF6C7; box-shadow: none; }


.ncs-comment-goods { width: 180px; margin: 10px auto;}
.ncs-comment-goods .goods-name { font: bold 12px/18px "Microsoft Yahei"; color: #AAA; width: 100%; height: 36px; overflow: hidden;}
.ncs-comment-goods .goods-pic { width: 160px; height: 160px; margin: 5px auto;}
.ncs-comment-goods .goods-pic a {line-height: 0; background-color: #FFF; text-align: center; vertical-align: middle; display: table-cell; *display: block; width: 160px; height: 160px; overflow: hidden;}
.ncs-comment-goods .goods-pic img { max-width: 160px; max-height: 160px; margin-top:expression(160-this.height/2); *margin-top:expression(80-this.height/2)/*IE6,7*/;}
.ncs-comment-goods .goods-price {}
.ncs-comment-goods .goods-raty span {}

/* 评价评分样式 */
.raty { font-size: 0; line-height: 0; *word-spacing:-1px/*IE6、7*/; vertical-align: middle; display: inline-block; *display: inline/*IE7*/; zoom: 1;}
.raty img { letter-spacing: normal; word-spacing: normal; display: inline-block; width: 16px; height: 16px; margin: 2px 0;}


.delivery-map { background-color: #FFF; text-align: center; width: 760px;  margin: 0 auto; border-radius: 10px;}
.delivery-map img { margin: 10px;}

.ownshop .ncs-info { display: none !important;}


/*1810new*/
.ncs-goods-summary .name{padding:8px 0px 14px 0;}
.ncs-goods-summary .name strong{color: #f23; font-size: 14px;height: 17px;}
.ncs-goods-summary dl dt, .ncs-goods-summary dl dd{padding: 3px 0;line-height: 32px; }
.ncs-goods-summary dl dt{font-family:simsun; width: 70px;}
.ncs-meta{padding: 10px 0;background-position-y: 10%;}
.ncs-meta .price strong{color: #f23;}
.ncs-detail{border: none;}
.ncs-goods-summary{border: none; width: 550px;margin-right:230px;}
.ncs-goods-summary dd{width: 460px;}
.ncs-lal{display: block; /*border-left: 1px solid #eee;*/ background: #fafafa;}
.ncs-handle{left: 0; top:478px; padding-top: 2px; border-top: none; width: 400px;}
.ncs-handle a span{display: none;}
.ncs-handle a{padding: 3px 10px; margin-right: 6px;}
/*.ncs-goods-summary dl.rate{position: absolute; right: 0px; top: 18px; width: 100px; height: 60px; overflow: hidden;}
.ncs-goods-summary dl.rate dt,.ncs-goods-summary dl.rate dd{height: 20px; height: 20px; padding: 0;}*/
.ncs-goods-summary .ncs-meta dt,.ncs-goods-summary dt{margin-left: 12px;}
.ncs-buy{padding-left: 12px;}
.ncs-key ul li.sp-img a{padding: 2px 10px 2px 4px;}
.ncs-key ul li.sp-img a:hover, .ncs-key ul li.sp-img a.hovered{padding: 2px 10px 2px 4px;border: 1px solid #F32613;}
.ncs-key ul li a.hovered i{right: 1px; bottom: 1px;}
#ncs-freight-prompt strong{color: #999;font-size: 12px;font-weight: 400;}
.ncs-top-panel dl{height: 204px;}
.ncs-top-panel li{padding: 22px 8px 18px;}
.ncs-top-panel dt{ height: 18px; line-height: 18px; left: 2px; width: 188px; top: 170px; text-align: center;}
.ncs-top-panel dd.selled,.ncs-top-panel dd.collection{display: none;}
.ncs-top-panel dd.price{top: 190px; left: 72px; color: #d23;padding-left:0;background:transparent; font-size: 1.1em;}
.ncs-top-panel dd.price:before{content: "¥"; margin-right: 2px; font: 12px/1.5 微软雅黑; color: #999;}
.ncs-top-panel dd.goods-pic{width: 160px; height: 160px; left: 14px;}
.ncs-top-panel dd.goods-pic a{height: 160px; width: 160px; padding:0; }
.ncs-top-panel dd.goods-pic a img{height: 160px; width: 160px; padding:0; }
.ncs-top-panel dd.goods-pic:hover p{display: none;}
.ncs-top-panel dd.goods-pic:hover a,.ncs-top-panel dd.goods-pic a{border:none;}
.ncs-goods-summary .ncs-goods-code{top: 0; display: block; border: none; padding: 4px;background-color: #f7dfbd; height: 120px; text-align: center; }
.ncs-goods-code{text-align: center;}
.ncs-goods-summary .ncs-goods-code span{color: #666; }
.ncs-goods-summary .nch-dp,.ncs-goods-title-nav .nch-dp{display: inline-block; }
.ncs-goods-summary .nch-dp span,.ncs-goods-title-nav .nch-dp span{width: 64px;display: block; position: relative; padding-left: 30px; line-height: 26px;}
.ncs-goods-summary .nch-dp span a,.ncs-goods-title-nav .nch-dp span a{display: block;opacity: 1; border: 1px solid #33a2ff; margin-left: 0; padding: 1px 0 1px 30px; border-radius: 4px; width: 56px; float: left; /*margin: 0 4px 0 6px;*/ position: absolute; top: 1px; left: 0px;}
.ncs-goods-summary .nch-dp span a.chat_offline{border: 1px solid #cbb;}
.ncs-goods-summary .nch-dp span .chat_online, .ncs-goods-summary .nch-dp span .chat_offline,.ncs-goods-title-nav .nch-dp span .chat_online, .ncs-goods-title-nav .nch-dp span .chat_offline{background:url(/chat/templates/default/images/chat_state_01.gif) no-repeat 5px 1px;}
.ncs-goods-summary .nch-dp span .chat_offline,.ncs-goods-title-nav .nch-dp span .chat_offline{background-position:6px -20px;}
.ncs-key ul li.sp-img a img{height: 32px; max-width: 32px; max-height: 32px; margin: 3px 5px 3px 0;}
.nch-breadcrumb-layout{background: #fafafa; margin-bottom: 10px;}
.ncs-handle a.inform{margin-right: 0;}
.ncs-lal .content ul li{width: 190px; height:170px;position: relative;}
.ncs-lal .content ul li .goods-pic{ width: 120px; height: 120px;}
.ncs-lal .content ul li .goods-pic a{ width: 120px; height: 120px; border: 1px solid #fafafa;}
.ncs-lal .content ul li .goods-pic a img{max-width: 120px; max-height: 120px; width:120px !important; height:120px !important;}	
.ncs-lal .content ul li .goods-price{position: absolute; top: 110px;display:block; width:120px; height:20px; line-height:20px; left:36px; background:rgba(255,255,255,0.85); color:#000;font-weight: 400; }
.ncs-lal .content ul li .goods-trname{position: absolute;top: 140px; height:16px; overflow:hidden; left:10px; width:170px;}
.ncs-lal .content ul li .goods-pic a:hover{border-color: #fff;}
.ncs-goods-summary{min-height: 560px;}
.ncs-lal .title{text-align: center; font-weight: 400; text-align: center;background-color:transparent; font-size: 13px; border-bottom: 1px solid #eee;}
.ncs-handle{padding-top: 5px !important;}
.ncs-handle a.share, .ncs-handle a.favorite{padding-left:22px; position: relative}
.ncs-handle a.share i, .ncs-handle a.favorite i{ background:url(../images/shop/f-icons-ex1.png) -83px -367px;    width: 16px !important;    height: 16px !important;     position: absolute; top:5px; left:3px;opacity: 0.8;}
.ncs-handle a span{display: none;}
.ncs-handle a.favorite i{background-position:-67px -367px;}
.ncs-handle .share:hover i{background-position:-99px -367px;opacity: 0.9;}
.ncs-handle .favorite:hover i{background-position:-51px -367px;opacity: 0.9;}
.ncs-key{margin-top:10px; }
.ncs-goods-summary hr{margin-bottom: 6px;}
.ncs-goodshandle{display: block; float: right; margin: 3px;}
.ncs-goodshandle a:link{display: block; width: 100px; height: 21px; line-height: 21px; background: #d12; color: #fff; font-size: 13px; text-align: center; padding: 4px 10px; position: relative;}
.ncs-goodshandle a:link i{ font-size: 14px; margin-right: 4px; }
.ncs-goodshandle.no-addcart_bar a:link{background: #aaa;cursor: not-allowed;}
.ncs-goods-title-nav .nch-dp{float: right; font-size: 12px; margin: 4px;}
.ncs-goods-title-nav .nch-dp span a,.ncs-goods-title-nav .nch-dp span a.chat_offline{border-color: transparent;}

/*商品数量换行*/
.ivan_sl{
  font-family: simsun;
  width: 70px;
  padding: 3px 0;
  line-height: 32px;
  /* margin: 0; */
  margin-left: 0px!important;
  float: left;
}
.ivan_input{
  display: inline-block;
    height: 45px;
    /* border: 1px solid; */
}
.ivan_input #quantity{
  height: 41px;
  text-align: center;
}
.ivan_input .ncs-figure-input a{
  left: 0px;
}
.ncs-goods-summary .ncs-btn{
/*   left: 70px; */
/* padding: 30px 0; */

}
