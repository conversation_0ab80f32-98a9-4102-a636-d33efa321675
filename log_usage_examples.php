<?php
/**
 * log_error和log_info函数使用示例
 * 展示如何使用兼容数组和字符串的$data参数
 */

// 引入必要的文件
require_once 'framework/function/global.func.php';

echo "=== log_error和log_info函数使用示例 ===\n\n";

// 1. 使用字符串作为$data参数
echo "1. 使用字符串作为data参数:\n";
log_info('user_action', 'User logged in successfully');
log_error('database_error', 'Connection to database failed');

// 2. 使用数组作为$data参数
echo "2. 使用数组作为data参数:\n";
log_info('order_created', [
    'order_id' => 12345,
    'user_id' => 67890,
    'amount' => 99.99,
    'status' => 'pending'
]);

log_error('validation_failed', [
    'field' => 'email',
    'value' => 'invalid-email',
    'rule' => 'email_format',
    'message' => 'Invalid email format'
]);

// 3. 使用默认空数组
echo "3. 使用默认空数组:\n";
log_info('system_startup');
log_error('unknown_error');

// 4. 使用其他数据类型（会被转换）
echo "4. 使用其他数据类型:\n";
log_info('counter_value', 42);
log_info('feature_enabled', true);
log_error('error_code', 500);

// 5. 实际业务场景示例
echo "5. 实际业务场景示例:\n";

// Pet Prize更新场景
$voucher_code = 'VOUCHER_' . time();

// 成功情况 - 使用字符串
log_info('pet_prize_update', "Pet prize status updated successfully for voucher_code: {$voucher_code}");

// 成功情况 - 使用数组（更结构化）
log_info('pet_prize_update', [
    'action' => 'status_update',
    'voucher_code' => $voucher_code,
    'old_status' => 1,
    'new_status' => 3,
    'timestamp' => date('Y-m-d H:i:s')
]);

// 失败情况 - 使用字符串
log_error('pet_prize_update', "Failed to update pet prize status for voucher_code: {$voucher_code}, error: Database connection timeout");

// 失败情况 - 使用数组（更详细）
log_error('pet_prize_update', [
    'action' => 'status_update',
    'voucher_code' => $voucher_code,
    'error_type' => 'database_error',
    'error_message' => 'Connection timeout',
    'retry_count' => 3,
    'timestamp' => date('Y-m-d H:i:s')
]);

// 6. 复杂数据结构示例
echo "6. 复杂数据结构示例:\n";
log_info('api_request', [
    'endpoint' => '/api/v1/orders',
    'method' => 'POST',
    'headers' => [
        'Content-Type' => 'application/json',
        'Authorization' => 'Bearer ***'
    ],
    'payload' => [
        'store_id' => 2,
        'voucher_code' => $voucher_code,
        'items' => [
            ['id' => 1, 'quantity' => 2],
            ['id' => 2, 'quantity' => 1]
        ]
    ],
    'response_time' => 150
]);

echo "\n=== 日志记录完成 ===\n";
echo "请检查以下日志文件:\n";
echo "- 信息日志: data/log/" . date('Ymd') . ".run.log\n";
echo "- 错误日志: data/log/" . date('Ymd') . ".err.log\n\n";

echo "=== 函数特性说明 ===\n";
echo "1. 当\$data为字符串时，会被包装为 ['message' => \$data]\n";
echo "2. 当\$data为数组时，直接使用原数组\n";
echo "3. 当\$data为其他类型时，会被包装为 ['value' => \$data]\n";
echo "4. 当\$data为空或未传入时，使用默认空数组 []\n";
echo "5. 所有日志都会以JSON格式记录，包含时间戳\n";

?>
