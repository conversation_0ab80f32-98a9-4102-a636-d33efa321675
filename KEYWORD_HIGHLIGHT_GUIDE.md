# 🎨 VSCode 关键词高亮配置指南

## 📁 已配置的文件

- **`.vscode/settings.json`** - 主要配置文件
- **`.vscode/extensions.json`** - 推荐扩展列表
- **`keyword_highlight_demo.php`** - 关键词高亮演示文件

## 🌈 配置的关键词类型

### 1. TODO (橙色背景)
- **用途**: 标记待完成的任务
- **颜色**: 橙色背景 (#ffab00)
- **示例**: `// TODO: 实现用户登录功能`

### 2. FIXME (红色背景)
- **用途**: 标记需要修复的问题
- **颜色**: 红色背景 (#f44336)
- **示例**: `// FIXME: 这里有一个潜在的安全漏洞`

### 3. NOTE (绿色背景)
- **用途**: 重要说明和注释
- **颜色**: 绿色背景 (#4caf50)
- **示例**: `// NOTE: 记住要验证用户输入`

### 4. HACK (黄色背景)
- **用途**: 临时解决方案或不优雅的代码
- **颜色**: 黄色背景 (#ffc107)
- **示例**: `// HACK: 临时解决方案，需要重构`

### 5. XXX (紫色背景)
- **用途**: 标记有问题或过时的代码
- **颜色**: 紫色背景 (#9c27b0)
- **示例**: `// XXX: 这个方法已经过时了`

### 6. BUG (粉色背景)
- **用途**: 标记已知的错误
- **颜色**: 粉色背景 (#e91e63)
- **示例**: `// BUG: 密码验证逻辑有问题`

### 7. DEBUG (蓝色背景)
- **用途**: 调试相关的代码
- **颜色**: 蓝色背景 (#2196f3)
- **示例**: `// DEBUG: 输出调试信息`

### 8. REVIEW (深橙色背景)
- **用途**: 需要代码审查的部分
- **颜色**: 深橙色背景 (#ff5722)
- **示例**: `// REVIEW: 这段代码需要代码审查`

## 🔧 主要功能特性

### 关键词高亮
- ✅ 支持多种关键词类型
- ✅ 自定义颜色和样式
- ✅ 大小写不敏感
- ✅ 在概览标尺中显示
- ✅ 鼠标悬停效果

### 语法高亮增强
- ✅ PHP 语法高亮优化
- ✅ 注释斜体显示
- ✅ 关键字加粗
- ✅ 变量和函数名特殊颜色
- ✅ 字符串高亮

### 编辑器增强
- ✅ 括号配对着色
- ✅ 括号引导线
- ✅ 语义高亮
- ✅ 搜索结果高亮
- ✅ 选择内容高亮

## 📦 推荐安装的扩展

### 必装扩展
1. **TODO Highlight** (`wayou.vscode-todo-highlight`)
   - 关键词高亮核心扩展

2. **Todo Tree** (`gruntfuggly.todo-tree`)
   - 在侧边栏显示所有 TODO 项目

3. **PHP Debug** (`felixfbecker.php-debug`)
   - PHP 调试支持

4. **PHP Intelephense** (`bmewburn.vscode-intelephense-client`)
   - PHP 智能感知

### 可选扩展
- **vscode-icons** - 文件图标主题
- **GitLens** - Git 增强功能
- **Prettier** - 代码格式化
- **Bracket Pair Colorizer** - 括号着色

## 🚀 使用方法

### 1. 安装扩展
```bash
# 在 VSCode 中按 Ctrl+Shift+P，输入：
Extensions: Show Recommended Extensions
```

### 2. 查看关键词高亮效果
打开 `keyword_highlight_demo.php` 文件查看效果

### 3. 使用 Todo Tree
- 按 `Ctrl+Shift+E` 打开资源管理器
- 查看 "TODO TREE" 面板
- 点击任意项目跳转到对应代码

### 4. 在代码中使用
```php
// TODO: 添加用户验证
// FIXME: 修复内存泄漏
// NOTE: 这个函数处理文件上传
// BUG: 这里有一个逻辑错误
// HACK: 临时解决方案
// XXX: 过时的代码
// DEBUG: 调试输出
// REVIEW: 需要审查
```

## ⚙️ 自定义配置

### 添加新的关键词
在 `.vscode/settings.json` 中的 `todohighlight.keywords` 数组添加：

```json
{
    "text": "IMPORTANT:",
    "color": "#fff",
    "backgroundColor": "#ff9800",
    "overviewRulerColor": "#ff9800"
}
```

### 修改现有关键词颜色
找到对应的关键词配置，修改 `backgroundColor` 值：

```json
{
    "text": "TODO:",
    "backgroundColor": "#your-color-here"
}
```

### 禁用特定关键词
将不需要的关键词从 `keywords` 数组中删除。

## 🎯 最佳实践

### 1. 关键词使用规范
- **TODO**: 计划要做的功能
- **FIXME**: 已知需要修复的问题
- **NOTE**: 重要说明
- **BUG**: 确认的错误
- **HACK**: 临时解决方案
- **XXX**: 有问题的代码
- **DEBUG**: 调试代码
- **REVIEW**: 需要审查的代码

### 2. 注释格式
```php
// TODO: 具体描述要做什么
/* FIXME: 详细说明问题和解决方案 */
// NOTE: 重要信息说明
```

### 3. 团队协作
- 统一关键词使用规范
- 定期清理完成的 TODO 项目
- 及时处理 FIXME 和 BUG 标记

## 🔍 故障排除

### 关键词不高亮
1. 确认已安装 `TODO Highlight` 扩展
2. 检查 `settings.json` 配置是否正确
3. 重启 VSCode

### 颜色显示异常
1. 检查主题兼容性
2. 尝试切换到默认主题测试
3. 检查 `workbench.colorCustomizations` 配置

### Todo Tree 不显示
1. 确认已安装 `Todo Tree` 扩展
2. 检查侧边栏是否展开
3. 刷新工作区

现在您可以在代码中使用这些关键词，它们会自动高亮显示！
