{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "a2daa83d708a35cd9e9cd60dcdac71b8", "packages": [{"name": "box/spout", "version": "v2.7.3", "source": {"type": "git", "url": "https://github.com/box/spout.git", "reference": "3681a3421a868ab9a65da156c554f756541f452b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/box/spout/zipball/3681a3421a868ab9a65da156c554f756541f452b", "reference": "3681a3421a868ab9a65da156c554f756541f452b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-xmlreader": "*", "ext-zip": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.0"}, "suggest": {"ext-iconv": "To handle non UTF-8 CSV files (if \"php-intl\" is not already installed or is too limited)", "ext-intl": "To handle non UTF-8 CSV files (if \"iconv\" is not already installed)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8.x-dev"}}, "autoload": {"psr-4": {"Box\\Spout\\": "src/Spout"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Library to read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way", "homepage": "https://www.github.com/box/spout", "keywords": ["OOXML", "csv", "excel", "memory", "odf", "ods", "office", "open", "php", "read", "scale", "spreadsheet", "stream", "write", "xlsx"], "support": {"issues": "https://github.com/box/spout/issues", "source": "https://github.com/box/spout/tree/v2.7.3"}, "time": "2017-09-25T19:44:35+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "48b8a90e2b97b4d69ce42851c1b9e59f8054661a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/48b8a90e2b97b4d69ce42851c1b9e59f8054661a", "reference": "48b8a90e2b97b4d69ce42851c1b9e59f8054661a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/ringphp": "~1.0", "php": "^5.6|^7.0", "psr/log": "~1.0"}, "require-dev": {"cpliakas/git-wrapper": "~1.0", "doctrine/inflector": "^1.1", "mockery/mockery": "0.9.4", "phpunit/phpunit": "^4.7|^5.4", "sami/sami": "~3.2", "symfony/finder": "^2.8", "symfony/yaml": "^2.8"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/5.0"}, "time": "2019-07-18T15:11:30+00:00"}, {"name": "guzzlehttp/ringphp", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/guzzle/RingPHP.git", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/RingPHP/zipball/5e2a174052995663dd68e6b5ad838afd47dd615b", "reference": "5e2a174052995663dd68e6b5ad838afd47dd615b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/streams": "~3.0", "php": ">=5.4.0", "react/promise": "~2.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple API and specification that abstracts away the details of HTTP into a single PHP function.", "support": {"issues": "https://github.com/guzzle/RingPHP/issues", "source": "https://github.com/guzzle/RingPHP/tree/1.1.1"}, "abandoned": true, "time": "2018-07-31T13:22:33+00:00"}, {"name": "guzzlehttp/streams", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/guzzle/streams.git", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/streams/zipball/47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple abstraction over streams of data", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"issues": "https://github.com/guzzle/streams/issues", "source": "https://github.com/guzzle/streams/tree/master"}, "abandoned": true, "time": "2014-10-12T19:18:40+00:00"}, {"name": "php-curl-class/php-curl-class", "version": "8.10.0", "source": {"type": "git", "url": "https://github.com/php-curl-class/php-curl-class.git", "reference": "c97c96d5d422b4085aba32ace096c3e404dde607"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-curl-class/php-curl-class/zipball/c97c96d5d422b4085aba32ace096c3e404dde607", "reference": "c97c96d5d422b4085aba32ace096c3e404dde607", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-curl": "*", "php": ">=5.3"}, "require-dev": {"ext-gd": "*", "phpunit/phpunit": "*", "squizlabs/php_codesniffer": "*"}, "suggest": {"ext-mbstring": "*"}, "type": "library", "autoload": {"psr-4": {"Curl\\": "src/Curl/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Unlicense"], "authors": [{"name": "<PERSON>"}], "description": "PHP Curl Class makes it easy to send HTTP requests and integrate with web APIs.", "homepage": "https://github.com/php-curl-class/php-curl-class", "keywords": ["API-Client", "api", "class", "client", "curl", "framework", "http", "http-client", "http-proxy", "json", "php", "php-curl", "php-curl-library", "proxy", "requests", "restful", "web-scraper", "web-scraping ", "web-service", "xml"], "support": {"issues": "https://github.com/php-curl-class/php-curl-class/issues", "source": "https://github.com/php-curl-class/php-curl-class/tree/8.10.0"}, "time": "2021-06-23T14:34:00+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "1441011fb7ecdd8cc689878f54f8b58a6805f870"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/1441011fb7ecdd8cc689878f54f8b58a6805f870", "reference": "1441011fb7ecdd8cc689878f54f8b58a6805f870", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "php": "^5.2|^7.0"}, "require-dev": {"squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://rootslabs.net"}, {"name": "<PERSON>", "homepage": "http://markbakeruk.net"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PHPExcel", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PHPExcel/issues", "source": "https://github.com/PHPOffice/PHPExcel/tree/master"}, "abandoned": "phpoffice/phpspreadsheet", "time": "2018-11-22T23:07:24+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "react/promise", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "31ffa96f8d2ed0341a57848cbb84d88b89dd664d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/31ffa96f8d2ed0341a57848cbb84d88b89dd664d", "reference": "31ffa96f8d2ed0341a57848cbb84d88b89dd664d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-4": {"React\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/2.x"}, "time": "2019-01-07T21:25:54+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.6.4"}, "platform-dev": [], "plugin-api-version": "2.1.0"}