<?php
// 检查 Xdebug 配置
echo "<h2>Xdebug 配置检查</h2>";

if (extension_loaded('xdebug')) {
    echo "<p style='color: green;'>✓ Xdebug 已加载</p>";
    
    echo "<h3>Xdebug 版本信息:</h3>";
    echo "<pre>" . phpversion('xdebug') . "</pre>";
    
    echo "<h3>相关配置:</h3>";
    $configs = [
        'xdebug.mode',
        'xdebug.start_with_request',
        'xdebug.client_host',
        'xdebug.client_port',
        'xdebug.idekey',
        'xdebug.discover_client_host',
        'xdebug.log',
        'xdebug.log_level'
    ];
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>配置项</th><th>当前值</th></tr>";
    foreach ($configs as $config) {
        $value = ini_get($config);
        echo "<tr><td>$config</td><td>" . ($value === false ? '未设置' : $value) . "</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>当前请求信息:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>项目</th><th>值</th></tr>";
    echo "<tr><td>XDEBUG_SESSION (GET)</td><td>" . ($_GET['XDEBUG_SESSION'] ?? '未设置') . "</td></tr>";
    echo "<tr><td>XDEBUG_SESSION_START (GET)</td><td>" . ($_GET['XDEBUG_SESSION_START'] ?? '未设置') . "</td></tr>";
    echo "<tr><td>XDEBUG_SESSION (Cookie)</td><td>" . ($_COOKIE['XDEBUG_SESSION'] ?? '未设置') . "</td></tr>";
    echo "<tr><td>调试是否激活</td><td>" . (xdebug_is_debugger_attached() ? '是' : '否') . "</td></tr>";
    echo "</table>";
    
    echo "<h3>测试链接:</h3>";
    $current_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    $base_url = strtok($current_url, '?');
    
    echo "<p><a href='{$base_url}?XDEBUG_SESSION_START=1'>启动调试会话</a></p>";
    echo "<p><a href='{$base_url}?XDEBUG_SESSION_STOP=1'>停止调试会话</a></p>";
    echo "<p><a href='{$base_url}'>普通访问（无调试）</a></p>";
    
} else {
    echo "<p style='color: red;'>✗ Xdebug 未加载</p>";
    echo "<p>请检查 php.ini 配置或安装 Xdebug 扩展</p>";
}

echo "<h3>PHP 信息:</h3>";
echo "<p>PHP 版本: " . phpversion() . "</p>";
echo "<p>配置文件: " . php_ini_loaded_file() . "</p>";

// 设置一个断点测试点
$test_variable = "这是一个测试变量";
echo "<p>如果调试器连接，可以在这里设置断点</p>";
?>
