
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0,maximum-scale=1, minimum-scale=1, user-scalable=no">
<title></title>
<style>
.h5open_ad{opacity: 0; pointer-events: none; position: fixed;top: 0;left: 0;right: 0;left: 0;}
.pa_open{opacity: 1; transition: opacity .3s; pointer-events: auto; transition: all 0.3s;}
.h5open_ad.pa_open.pa_out{opacity: 0; pointer-events: none;}
.h5open_ad .bgb{background: rgba(0,0,0,0.7); display: block; position: fixed;top: 0;left: 0;right: 0;bottom: 0; z-index: 1100; }
.h5open_ad a{display: block;opacity: 0; margin: calc(50vh - 60vw) auto 0; width: 90vw; max-height:120vw; padding: 0 0 2vw; overflow: hidden; position: relative; z-index: 1200; }
.h5open_ad a img{width: 100%; display: block; animation:pa_openimg 5s ease-in-out infinite 0.6s;}
.h5open_ad .close{width: 7vw; z-index: 1300; position: relative; height: 7vw; margin: 2vw auto; border-radius: 50%; background: url(http://www.upetmart.com/wap/chain/images/i_closew.png) center center no-repeat; background-size: 70% 70%; border:0.2vw solid #fff; opacity: 0; }
.h5open_ad.pa_open a,.h5open_ad.pa_open .close{animation:pa_open cubic-bezier(.19,1,.22,1) 1s 0.3s forwards;}
.h5open_ad.pa_open.pa_out a{animation:pa_out 1s forwards;}

@keyframes pa_open {
    0% {transform: scale(0.2); opacity: 0; }
    100% {transform: scale(1.0); opacity: 1;}
}
@keyframes pa_out {
    0% {transform: scale(1.0); opacity: 1; }
    100% {transform: scale(0.2); opacity: 0;}
}
@keyframes pa_openimg {
    0%{transform: translateY(0vw);}
    50%{transform: translateY(2vw);}
    100% {transform: translateY(0vw);}
}
</style>


</head>
<body>
<div class="h5open_ad">
	<a href="###"><img src="http://www.upetmart.com/templates/default/skin/1906/open.png"></a>
	<div class="close"></div>
	<div class="bgb"></div>
</div>

	

<script type="text/javascript" src="js/zepto.min.js"></script>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery.cookie.js"></script>
<script type="text/javascript">
if(!getCookie('ad_ck')){	
	$(".h5open_ad").addClass("pa_open");
};
$(".pa_open .close,.pa_open").click(function(){
	$(".pa_open").addClass("pa_out");
	addCookie('ad_ck', "open", 0.01);
});
</script>
</body>
</html>
