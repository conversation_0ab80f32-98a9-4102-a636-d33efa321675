<?php
/**
 * 商品图片和视频统一调用函数
 *
 *
 *
 * @package    function
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * <AUTHOR> Team
 * @since      File available since Release v1.1
 */

use Upet\Integrates\Storage\Storage;
use Upet\Integrates\Storage\Stream;
use Upet\Integrates\Wechat\Wechat;
use Upet\Models\Goods;

defined('InShopNC') or exit('Access Invalid!');

/**
 * 取得商品缩略图的完整URL路径，接收商品信息数组，返回所需的商品缩略图的完整URL
 *
 * @param array $goods 商品信息数组
 * @param string $type 缩略图类型  值为60,240,360,1280
 * @return string
 */
function thumb($goods = array(), $type = ''){
    $type_array = explode(',_', ltrim(GOODS_IMAGES_EXT, '_'));
    if (!in_array($type, $type_array)) {
        $type = '240';
    }
    if (empty($goods)){
        return UPLOAD_SITE_URL.'/'.defaultGoodsImage($type);
    }
    if(stripos($goods['goods_image'],'?watermark/3') !== false || stripos($goods['goods_image'],'?x-oss-process=') !== false) {
        if (strpos($goods['goods_image'], 'http://')===0 || strpos($goods['goods_image'], 'https://')===0){
            return $goods['goods_image'];
        }
        return Storage::url(ATTACH_GOODS . '/1/' . $goods['goods_image']);
    }
    if (array_key_exists('apic_cover', $goods)) {
        $goods['goods_image'] = $goods['apic_cover'];
    }
    if (strpos($goods['goods_image'], 'http://')!==false || strpos($goods['goods_image'], 'https://')!==false){
        if(stripos($goods['goods_image'],'file.vetscloud.com') !== false){
            if(empty($type) || $type > 360){
                $thumbExt = 'equal_750X750';
            }elseif ($type == 360){
                $thumbExt = 'equal_360X360';
            }else{
                $thumbExt = 'equal_proportion';
            }
            return $goods['goods_image']."|$thumbExt";
        }
        return $goods['goods_image'];
    }

    if (empty($goods['goods_image'])) {
        return UPLOAD_SITE_URL.'/'.defaultGoodsImage($type);
    }

    $search_array = explode(',', GOODS_IMAGES_EXT);
    $file = str_ireplace($search_array,'',$goods['goods_image']);
    $fname = basename($file);
    //取店铺ID
    if (preg_match('/^(\d+_)/',$fname)){
        $store_id = substr($fname,0,strpos($fname,'_'));
    }else{
        $store_id = $goods['store_id'];
    }

    if($type){
        $type = 'product-'.$type;
    }

    return Storage::url(ATTACH_GOODS . '/' . $store_id . '/' . $file, $type);
}
/**
 * 取得商品缩略图的完整URL路径，接收图片名称与店铺ID
 *
 * @param string $file 图片名称
 * @param string $type 缩略图尺寸类型，值为60,240,360,1280
 * @param mixed $store_id 店铺ID 如果传入，则返回图片完整URL,如果为假，返回系统默认图
 * @return string
 */
function cthumb($file, $type = '', $store_id = 1) {
    $type_array = explode(',_', ltrim(GOODS_IMAGES_EXT, '_'));
    if (!in_array($type, $type_array)) {
        $type = '240';
    }
    if (empty($file)) {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage ( $type );
    }
    if(stripos($file,'?watermark/3') !== false || stripos($file,'?x-oss-process=') !== false) {
        if (strpos($file, 'http://')===0 || strpos($file, 'https://')===0){
            return $file;
        }
        return Storage::url(ATTACH_GOODS . '/' . $store_id . '/' . $file);
    }
    if (strpos($file, 'http://')!==false || strpos($file, 'https://')!==false){
        if(stripos($file,'file.vetscloud.com') !== false){
            if(empty($type) || $type > 360){
                $thumbExt = 'equal_750X750';
            }elseif ($type == 360){
                $thumbExt = 'equal_360X360';
            }else{
                $thumbExt = 'equal_proportion';
            }
            return "$file|$thumbExt";
        }
        return $file;
    }
    $search_array = explode(',', GOODS_IMAGES_EXT);
    $file = str_ireplace($search_array,'',$file);
    $fname = basename($file);
//    取店铺ID
    if (preg_match('/^(\d+_)/',$fname)){
        $store_id = substr($fname,0,strpos($fname,'_'));
    }

    if($type){
        $type = 'product-'.$type;
    }

    return Storage::url(ATTACH_GOODS . '/' . $store_id . '/' . $file, $type);
}

function getOriginImage($file, $store_id = 1)
{
    if (strpos($file, 'http://')!==false || strpos($file, 'https://')!==false){
        return $file;
    }
    return Storage::url(ATTACH_GOODS . '/' . $store_id . '/' . $file);
}

/**
 * 商品视频连接
 */
function goodsVideoPath($video_file, $store_id = false){
    if (empty($video_file)) {
        return UPLOAD_SITE_URL . '/' . defaultGoodsVideo();
    }
    if(empty($store_id)){
        //取店铺ID
        if (preg_match('/^(\d+_)/',$video_file)){
            $store_id = substr($video_file,0,strpos($video_file,'_'));
        }
    }
    if (strpos($video_file, 'http://')!==false || strpos($video_file, 'https://')!==false){
        return $video_file;
    }
    if(file_exists(BASE_UPLOAD_PATH . '/' . ATTACH_GOODS . '/' . $store_id . '/' . 'goods_video' . '/' . $video_file )){
        return UPLOAD_SITE_URL . '/' . ATTACH_GOODS . '/' . $store_id . '/' . 'goods_video' . '/' . $video_file ;
    }
    
}
/**
 * 商品二维码
 * @param array $goods_info
 * @return string
 */
function goodsQRCode($goods_info) {
    try {
        $goods_qrcode = Goods::field('goods_qrcode')->where('goods_id',$goods_info['goods_id'])
            ->where('store_id',$goods_info['store_id'])->find();
        if (!empty($goods_qrcode['goods_qrcode'])) {
            return $goods_qrcode['goods_qrcode'];
        }
        $path = trim(ATTACH_DISTRI, '/') . '/' . $goods_info['store_id'] . '-' . $goods_info['goods_id'] . '.png';
        Storage::disk('oss')->put(getCodeStream(
            'app/mall/page/goodsDetail', $goods_info['goods_id'], $goods_info['store_id']
        ), $path);
        $qr_url = Storage::disk('oss')->url($path);
        //把二维码地址存入数据库
        Goods::where(array('goods_id' => $goods_info['goods_id']))->where('store_id',$goods_info['store_id'])->update(array('goods_qrcode' => $qr_url));
        return $qr_url;
    } catch (Exception $exception) {
        return UPLOAD_SITE_URL . DS . ATTACH_STORE . DS . 'default_qrcode.png';
    }
}

function getCodeStream($page, $scene,$store_id = 0){
    $mini = Wechat::miniProgram(null,null,$store_id);
    $isProduction = get_env() == 'www';
    $resource = $mini->getWXACodeUnLimit([
        'page' => $page,
        'scene' => $scene,
        'width' => $_POST['width'] ?: 400,
        'check_path' => $isProduction,
        'env_version' => $isProduction ? 'release' : 'trial'
//            'env_version' =>  'trial'
    ]);
    return new Stream($resource);
}
/**
 * 取得团购缩略图的完整URL路径
 *
 * @param string $imgurl 商品名称
 * @param string $type 缩略图类型  值为small,mid,max
 * @return string
 */
function gthumb($image_name = '', $type = ''){
    if (!in_array($type, array('small','mid','max'))) $type = 'small';
    if (empty($image_name)){
        return UPLOAD_SITE_URL.'/'.defaultGoodsImage('240');
    }
    list($base_name, $ext) = explode('.', $image_name);
    list($store_id) = explode('_', $base_name);
    $file_path = ATTACH_GROUPBUY.DS.$store_id.DS.$base_name.'_'.$type.'.'.$ext;
    if(!file_exists(BASE_UPLOAD_PATH.DS.$file_path)) {
        return UPLOAD_SITE_URL.'/'.defaultGoodsImage('240');
    }
    return UPLOAD_SITE_URL.DS.$file_path;

}

/**
 * 取得买家缩略图的完整URL路径
 *
 * @param string $imgurl 商品名称
 * @param string $type 缩略图类型  值为240,1024
 * @return string
 */
function snsThumb($image_name = '', $type = ''){

    if(strstr($image_name,'file.rvet.cn') || strstr($image_name,'https://')){
        return $image_name;
    }

    if (!in_array($type, array('240','1024'))) $type = '240';
    if (empty($image_name)){
        return UPLOAD_SITE_URL.'/'.defaultGoodsImage('240');
    }

    if(strpos($image_name, '/')) {
        $image = explode('/', $image_name);
        $image = end($image);
    } else {
        $image = $image_name;
    }

    list($member_id) = explode('_', $image);

    return member_image($member_id,$image_name,$type);
}

/**
 * 取得积分商品缩略图的完整URL路径
 *
 * @param string $path 图片路径
 * @param string $type 缩略图类型 值为small
 * @return string
 */
function pointprodThumb($path = '', $type = '')
{
    if (!in_array($type, ['small', 'mid'])) {
        $type = '';
    } else {
        $type = [
            'small' => 'small/60',
            'mid' => 'mid/240',
        ][$type];
    };

    if (empty($path)) {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
    }

    return storage_url($path, ATTACH_POINTPROD, $type);
}

/**
 * 取得品牌图片
 *
 * @param string $image_name
 * @return string
 */
function brandImage($image_name = '') {
    if (strpos($image_name, 'http://')!==false || strpos($image_name, 'https://')!==false){
        return $image_name;
    }
    elseif ($image_name != '') {
        return UPLOAD_SITE_URL.'/'.ATTACH_BRAND.'/'.$image_name;
    }
    return UPLOAD_SITE_URL.'/'.ATTACH_COMMON.'/default_brand_image.gif';
}

/**
* 取得订单状态文字输出形式
*
* @param array $order_info 订单数组
* @return string $order_state 描述输出
*/
function orderState($order_info) {
    if ($order_info['lock_state']){
        $order_state = '退款/退货';
    }else {
        switch ($order_info['order_state']) {
            case ORDER_STATE_CANCEL:
                $order_state = '已取消';
                break;
            case ORDER_STATE_NEW:
                if ($order_info['chain_code']) {
                    $order_state = '门店付款自提';
                } else {
                    $order_state = '待付款';
                }
                break;
            case ORDER_STATE_PAY:
                if ($order_info['chain_code']) {
                    $order_state = '待自提';
                } else {
                    $order_state = '待发货';
                }
                break;
            case ORDER_STATE_SEND:
                $order_state = '待收货';
                break;
            case ORDER_STATE_SUCCESS:
                $order_state = '交易完成';
                break;
            case 50:
                $order_state = '部分发货';
                break;
        }
    }
    return $order_state;
}

/**
 * 取得订单支付类型文字输出形式
 *
 * @param array $payment_code
 * @return string
 */
function orderPaymentName($payment_code) {
    return str_replace(
            array('offline','online','ali_native','alipay','tenpay','chinabank','predeposit','wxpay','wx_jsapi','wx_saoma','chain','card','bd_pay'),
            array('货到付款','在线付款','支付宝移动支付','支付宝','财付通','网银在线','站内余额支付','微信支付[客户端]','微信支付[jsapi]','微信支付[扫码]','门店支付','储值卡', '百度支付'),
            $payment_code);
}

/**
 * 取得订单商品销售类型文字输出形式
 *
 * @param string $goods_type
 * @return string 描述输出
 */
function orderGoodsType($goods_type) {
    $types = [
        20 => '砍价', 10 => '拼团', 12 => '会员价', 15 => '门店',
        1 => '', 2 => '团购', 3 => '限时折扣', 4 => '优惠套装',
        5 => '赠品', 6 => '秒杀', 7 => '闪购', 8 => '',
        9 => '换购', 16 => '付费会员价',21=>'付费会员卡'
    ];
    return isset($types[$goods_type]) ? $types[$goods_type] : '';
}

/**
 * 取得结算文字输出形式
 *
 * @param array $bill_state
 * @return string 描述输出
 */
function billState($bill_state) {
    return str_replace(
            array('1','2','3','4'),
            array('已出账','商家已确认','平台已审核','结算完成'),
            $bill_state);
}
