<?php
/**
 * 远程操作常用函数
 *
 *
 *
 * @package    function
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * <AUTHOR> Team
 * @since      File available since Release v1.1
 */

defined('InShopNC') or exit('Access Invalid!');

/**
 * 通过FTP同步图片到远程服务器
 *
 * @param string $path 图片路径（upload/store/goods/4）
 * @param string $file 图片名称（含年月日2012/06/12/03f625d923bfb1ca84355007487ed68b.jpg）
 * @param boolean $ifdel 是否删除本地图片
 * @return string 远程图片路径部分
 */
function remote_ftp($path, $file, $ifdel = true){

    ftpcmd('upload', $path.'/'.$file);
    $img_ext = explode(',', GOODS_IMAGES_EXT);
    foreach ($img_ext as $val) {
        if(!ftpcmd('error')) ftpcmd('upload', $path.'/'.str_ireplace('.', $val . '.', $file));
    }

    if(!ftpcmd('error')) {
        if ($ifdel){
            @unlink(BASE_PATH.'/'.$path.'/'.$file);
            foreach ($img_ext as $val) {
                @unlink(BASE_PATH.'/'.$path.'/'.str_ireplace('.', $val . '.', $file));
            }
        }
        return C('ftp_access_url').'/'.$path;
    }
    return false;
}

function ftpcmd($cmd, $arg1 = '') {
    import('libraries.ftp');
    static $ftp;
    $ftpon = C('ftp_open');
    if(!$ftpon) {
        return $cmd == 'error' ? -101 : 0;
    } elseif($ftp == null) {
        $ftp = & NcFtp::instance();
    }
    if(!$ftp->enabled) {
        return $ftp->error();
    } elseif($ftp->enabled && !$ftp->connectid) {
        $ftp->connect();
    }
    switch ($cmd) {
        case 'upload' : return $ftp->upload(BASE_PATH.'/'.$arg1, $arg1); break;
        case 'delete' : return $ftp->ftp_delete($arg1); break;
        case 'close'  : return $ftp->ftp_close(); break;
        case 'error'  : return $ftp->error(); break;
        case 'object' : return $ftp; break;
        default       : return false;
    }

}

function getremotefile($file) {
    @set_time_limit(0);
    $file = $file.'?'.time().rand(1000, 9999);
    $str = @implode('', @file($file));
    if(!$str) {
        $str = dfsockopen($file);
    }
    return $str;
}

function dfsockopen($url, $limit = 0, $post = '', $cookie = '', $bysocket = FALSE, $ip = '', $timeout = 15, $block = TRUE, $encodetype  = 'URLENCODE', $allowcurl = TRUE) {
    return _dfsockopen($url, $limit, $post, $cookie, $bysocket, $ip, $timeout, $block, $encodetype, $allowcurl);
}

function _dfsockopen($url, $limit = 0, $post = '', $cookie = '', $bysocket = FALSE, $ip = '', $timeout = 15, $block = TRUE, $encodetype  = 'URLENCODE', $allowcurl = TRUE) {
    $return = '';
    $matches = parse_url($url);
    $scheme = $matches['scheme'];
    $host = $matches['host'];
    $path = $matches['path'] ? $matches['path'].($matches['query'] ? '?'.$matches['query'] : '') : '/';
    $port = !empty($matches['port']) ? $matches['port'] : 80;

    if(function_exists('curl_init') && function_exists('curl_exec') && $allowcurl) {
        $ch = curl_init();
        $ip && curl_setopt($ch, CURLOPT_HTTPHEADER, array("Host: ".$host));
        curl_setopt($ch, CURLOPT_URL, $scheme.'://'.($ip ? $ip : $host).':'.$port.$path);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if($post) {
            curl_setopt($ch, CURLOPT_POST, 1);
            if($encodetype == 'URLENCODE') {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
            } else {
                parse_str($post, $postarray);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $postarray);
            }
        }
        if($cookie) {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        $data = curl_exec($ch);
        $status = curl_getinfo($ch);
        $errno = curl_errno($ch);
        curl_close($ch);
        if($errno || $status['http_code'] != 200) {
            return;
        } else {
            return !$limit ? $data : substr($data, 0, $limit);
        }
    }

    if($post) {
        $out = "POST $path HTTP/1.0\r\n";
        $header = "Accept: */*\r\n";
        $header .= "Accept-Language: zh-cn\r\n";
        $boundary = $encodetype == 'URLENCODE' ? '' : '; boundary='.trim(substr(trim($post), 2, strpos(trim($post), "\n") - 2));
        $header .= $encodetype == 'URLENCODE' ? "Content-Type: application/x-www-form-urlencoded\r\n" : "Content-Type: multipart/form-data$boundary\r\n";
        $header .= "User-Agent: $_SERVER[HTTP_USER_AGENT]\r\n";
        $header .= "Host: $host:$port\r\n";
        $header .= 'Content-Length: '.strlen($post)."\r\n";
        $header .= "Connection: Close\r\n";
        $header .= "Cache-Control: no-cache\r\n";
        $header .= "Cookie: $cookie\r\n\r\n";
        $out .= $header.$post;
    } else {
        $out = "GET $path HTTP/1.0\r\n";
        $header = "Accept: */*\r\n";
        $header .= "Accept-Language: zh-cn\r\n";
        $header .= "User-Agent: $_SERVER[HTTP_USER_AGENT]\r\n";
        $header .= "Host: $host:$port\r\n";
        $header .= "Connection: Close\r\n";
        $header .= "Cookie: $cookie\r\n\r\n";
        $out .= $header;
    }

    $fpflag = 0;
    if(!$fp = @fsocketopen(($ip ? $ip : $host), $port, $errno, $errstr, $timeout)) {
        $context = array(
            'http' => array(
                'method' => $post ? 'POST' : 'GET',
                'header' => $header,
                'content' => $post,
                'timeout' => $timeout,
            ),
        );
        $context = stream_context_create($context);
        $fp = @fopen($scheme.'://'.($ip ? $ip : $host).':'.$port.$path, 'b', false, $context);
        $fpflag = 1;
    }

    if(!$fp) {
        return '';
    } else {
        stream_set_blocking($fp, $block);
        stream_set_timeout($fp, $timeout);
        @fwrite($fp, $out);
        $status = stream_get_meta_data($fp);
        if(!$status['timed_out']) {
            while (!feof($fp) && !$fpflag) {
                if(($header = @fgets($fp)) && ($header == "\r\n" ||  $header == "\n")) {
                    break;
                }
            }

            $stop = false;
            while(!feof($fp) && !$stop) {
                $data = fread($fp, ($limit == 0 || $limit > 8192 ? 8192 : $limit));
                $return .= $data;
                if($limit) {
                    $limit -= strlen($data);
                    $stop = $limit <= 0;
                }
            }
        }
        @fclose($fp);
        return $return;
    }
}

function fsocketopen($hostname, $port = 80, &$errno, &$errstr, $timeout = 15) {
    $fp = '';
    if(function_exists('fsockopen')) {
        $fp = @fsockopen($hostname, $port, $errno, $errstr, $timeout);
    } elseif(function_exists('pfsockopen')) {
        $fp = @pfsockopen($hostname, $port, $errno, $errstr, $timeout);
    } elseif(function_exists('stream_socket_client')) {
        $fp = @stream_socket_client($hostname.':'.$port, $errno, $errstr, $timeout);
    }
    return $fp;
}
