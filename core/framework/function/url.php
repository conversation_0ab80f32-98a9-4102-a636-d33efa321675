<?php

use Upet\Integrates\Event\Event;
use Upet\Integrates\Queue\Queue;
use Upet\Integrates\Storage\Storage;

/**
 * 会员图片
 *
 * @param $prefix
 * @param $path
 * @param null $size
 * @return string
 */
function member_image($prefix, $path, $size = null)
{
    if (empty($path)) {
        return ADMIN_SITE_URL . '/templates/' . TPL_NAME . '/images/member/default_image.png';
    }

    return Storage::url(sprintf('%s/%s/%s', ATTACH_MALBUM, $prefix, $path), $size);
}

/**
 * 运费模板图片url
 *
 * @param $path
 * @return string
 */
function way_bill_image($path)
{
    if (empty($path)) {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
    }

    return Storage::url(ATTACH_WAYBILL . '/' . $path);
}

/**
 * 商品分类图片
 *
 * @param $path
 * @return string | void
 */
function good_class_image($path)
{
    return storage_url($path, ATTACH_GOODS_CLASS);
}

/**
 * 店铺图片
 *
 * @param $path
 * @return string | void
 */
function store_image($path)
{
    return storage_url($path, ATTACH_STORE);
}

/**
 * 代金券图片
 *
 * @param $storeId
 * @param $path
 * @param null $size
 * @return string | void
 */
function voucher_image($storeId, $path, $size = null)
{
    if (strpos($path, 'http') === 0) {
        return $path;
    }

    if (empty($path)) {
        return UPLOAD_SITE_URL . DS . defaultGoodsImage(240);
    }

    return Storage::url(sprintf('%s/%s/%s', ATTACH_VOUCHER, $storeId, $path), $size);
}

/**
 * 退款图片
 *
 * @param $path
 * @return string|void
 */
function refund_image($path)
{
    if (empty($path)) {
        return;
    }
    if(is_array($path) && $path['pic']){
        $path = $path['pic'];
    }
    if(strpos($path, 'http') === 0){
        return $path;
    }

    return Storage::url(ATTACH_PATH . '/refund/' . $path);
}

/**
 * 爱省钱用户头像
 *
 * @param $path
 * @return string|void
 */
function member_head_image($path)
{
    if (empty($path)) {
        return;
    }
    if(strpos($path, 'http') === 0){
        return $path;
    }

    return Storage::url(ATTACH_AVATAR . '/' . $path);
}

/**
 * 锦鲤活动图片
 *
 * @param $path
 * @param string $default
 * @return string
 */
function lottery_dial_image($path, $default = '')
{
    if (empty($path)) {
        if ($default) {
            return UPLOAD_SITE_URL . DS . ATTACH_LOTTERY_DIAL . DS . "/images/" . $default;
        }
        return $default;
    }

    return Storage::url(ATTACH_LOTTERY_DIAL . DS . $path);
}

/**
 * 返回存储文件 url
 *
 * @param string $path 路径
 * @param $prefix
 * @param null $size
 * @return string|void
 */
function storage_url($path, $prefix = null, $size = null)
{
    if (empty($path)) {
        return;
    }

    if ($prefix) {
        $path = rtrim($prefix, '/') . '/' . $path;
    }

    return Storage::url($path, $size);
}

/**
 * 默认图片
 *
 * @param $size
 * @return string
 */
function default_image($size = null)
{
    $image = $size ? "default_goods_image_$size.gif" : "default_goods_image.gif";

    return UPLOAD_SITE_URL . '/' . ATTACH_COMMON . '/' . $image;
}

/**
 * 触发事件
 *
 * @param Event $event
 */
function event(Event $event)
{
    foreach ($event->getListeners() as $listener) {
        $ref = new ReflectionClass($listener);
        // 如果监听器实现了队列，则异步执行
        if (array_key_exists(Queue::class, $ref->getInterfaces())) {
            $listener::dispatch($event);
            continue;
        }
        (new $listener($event))->handle();
    }
}

/**
 * 调用动作
 *
 * @param $action
 * @return mixed
 */
function action($action)
{
    return $action->handle();
}

/**
 * 递归创建目录
 *
 * @param $dir
 * @return bool
 */
function Directory($dir)
{
    return is_dir($dir) or Directory(dirname($dir)) and mkdir($dir, 0777);
}

/**
 * 获取当前环境，sit、uat、www
 *
 * @return string
 */
function get_env()
{
    $host = parse_url(SHOP_SITE_URL,PHP_URL_HOST);
    $env = explode('.',$host)[0];

    if(is_numeric($env) || $env === 'localhost'){
        return 'uat1';
    }

    return $env;
}