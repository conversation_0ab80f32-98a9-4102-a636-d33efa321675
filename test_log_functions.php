<?php
/**
 * 测试log_error和log_info函数的兼容性
 * 验证$data参数能够兼容数组和字符串类型
 */

require_once 'framework/function/global.func.php';

echo "=== 测试log_error和log_info函数兼容性 ===\n\n";

// 测试log_info函数
echo "1. 测试log_info函数:\n";

// 测试传入字符串
echo "   - 传入字符串参数:\n";
log_info('test_message_1', 'This is a string data');

// 测试传入数组
echo "   - 传入数组参数:\n";
log_info('test_message_2', ['key1' => 'value1', 'key2' => 'value2']);

// 测试传入空数组（默认值）
echo "   - 传入空数组（默认值）:\n";
log_info('test_message_3');

// 测试传入数字
echo "   - 传入数字参数:\n";
log_info('test_message_4', 12345);

// 测试传入布尔值
echo "   - 传入布尔值参数:\n";
log_info('test_message_5', true);

echo "\n2. 测试log_error函数:\n";

// 测试传入字符串
echo "   - 传入字符串参数:\n";
log_error('error_message_1', 'This is an error string');

// 测试传入数组
echo "   - 传入数组参数:\n";
log_error('error_message_2', ['error_code' => 500, 'error_detail' => 'Internal server error']);

// 测试传入空数组（默认值）
echo "   - 传入空数组（默认值）:\n";
log_error('error_message_3');

// 测试传入对象（会被转换为数组）
echo "   - 传入对象参数:\n";
$obj = new stdClass();
$obj->property = 'test_value';
log_error('error_message_4', $obj);

echo "\n3. 测试实际使用场景:\n";

// 模拟pet_prize更新成功的情况
$voucher_code = 'TEST_VOUCHER_' . time();
log_info('pet_prize_update_log', 'Pet prize status updated successfully for voucher_code: ' . $voucher_code);

// 模拟pet_prize更新失败的情况
log_error('pet_prize_update_log', 'Failed to update pet prize status for voucher_code: ' . $voucher_code . ', error: Database connection failed');

// 模拟传入结构化数据
log_info('pet_prize_update_log', [
    'action' => 'update_status',
    'voucher_code' => $voucher_code,
    'old_status' => 1,
    'new_status' => 3,
    'timestamp' => time()
]);

echo "\n测试完成！请检查日志文件:\n";
echo "- 信息日志: data/log/" . date('Ymd') . ".run.log\n";
echo "- 错误日志: data/log/" . date('Ymd') . ".err.log\n";

// 显示日志文件内容（如果存在）
$log_path = BASE_ROOT_PATH . DS . 'data' . DS . 'log' . DS;
$info_log = $log_path . date('Ymd') . '.run.log';
$error_log = $log_path . date('Ymd') . '.err.log';

if (file_exists($info_log)) {
    echo "\n=== 信息日志内容（最后10行）===\n";
    $lines = file($info_log);
    $last_lines = array_slice($lines, -10);
    foreach ($last_lines as $line) {
        echo $line;
    }
}

if (file_exists($error_log)) {
    echo "\n=== 错误日志内容（最后10行）===\n";
    $lines = file($error_log);
    $last_lines = array_slice($lines, -10);
    foreach ($last_lines as $line) {
        echo $line;
    }
}

echo "\n";
?>
